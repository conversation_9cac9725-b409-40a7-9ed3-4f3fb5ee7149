package com.sgs.gpo.facade.model.payment.paymentlist.rsp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sgs.framework.core.base.BaseIdBO;
import com.sgs.gpo.facade.model.quotation.rsp.QuotationRsp;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class PaymentListRsp extends BaseIdBO<String> {

    private String orderId;
    private String orderNo;
    private String externalOrderNo;
    private BigDecimal finalAmount;
    private String bossOrderNo;
    private String invoiceNo;
    private String invoicePdfCloudId;
    private BigDecimal invoiceAmount;
    private String currencyCode;

    private Date invoiceDate;
    private BigDecimal paidAmount;
    private BigDecimal balanceAmount;
    private String taxInvoiceNo;
    private BigDecimal taxInvoiceAmount;

    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date taxInvoiceDate;

    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date taxInvoiceDeliveredDate;
    private String taxInvoiceDeliveredAWBNo;
    private String taxInvoiceLink;
    private String applicant;
    private String csName;
    private Integer paymentStatus;
    private String paymentStatusName;
    private String refBossOrderNo;
    private String refBossInvoiceNo;
    private List<QuotationRsp> quotationHeadDTOS;
    private String payerName;
    private String payerContact;

    private Integer operationMode;
}
