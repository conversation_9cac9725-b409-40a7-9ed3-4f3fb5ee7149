package com.sgs.gpo.facade.model.tag.req;

import com.sgs.framework.core.base.BaseRequest;
import com.sgs.gpo.facade.model.tag.bo.TagBO;
import com.sgs.gpo.facade.model.tag.bo.TagValueBO;
import lombok.Data;

import java.util.List;

@Data
public class TagValidateReq extends BaseRequest {

    private String productLine;
    private String tagObjectType;
    // 待校验的Tag列表
    private List<TagBO> thisTagList;
    // BU 配置的可以选择的Tag列表
    private List<TagBO> allTagList;
}
