package com.sgs.gpo.facade.model.otsnotes.testline.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @title: TestItemNameReq
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/7/25 8:28
 */
@Data
public class TestLineNameReq extends BaseRequest {
    private String testLineNameKey;
    private String labCode;
    private Integer limit;
    private String testDueDateFrom;
    private String testDueDateTo;
    private String testStartDateFrom;
    private String testStartDateTo;
    private String testEndDateFrom;
    private String testEndDateTo;
    private String createdDateFrom;
    private String createdDateTo;

    private Set<String> orderNoList;
    private Set<String> testItemNoList;
    private String testItemNo;
    private String orderNo;
    private String jobNo;
    private String subcontractNo;
}
