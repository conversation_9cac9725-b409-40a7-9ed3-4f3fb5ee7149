package com.sgs.gpo.facade.model.payment.paiduplist.rsp;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class PaidUpListRsp {

    private String id;

    private String labCode;

    private Short buId;

    private String buCode;

    private Short locationId;

    private String locationCode;

    private String paidUpNo;

    private String paidBy;

    private String remittanceNo;

    private BigDecimal remittanceAmount;

    private String currencyCode;

    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date remittanceReceivedDate;

    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date paidDate;

    private String bank;

    private String paymentMethod;

    private String remark;

    private Date createdDate;

    private String createdBy;

    private Date modifiedDate;

    private String modifiedBy;

    private Integer activeIndicator;

    private String orderId;

    private String orderNo;

    private String externalOrderNo;

    private String payerEnName;

    private String payerCnName;

    private String payerCustomerNo;

    private String csName;

    private Integer operationMode;

    private List<String> orderNoList;

    private Boolean currencyErrorShow;

    private String currencyErrorMessage;

    private BigDecimal totalPrice;

    private String quoteCurrencyId;

    private String  payStatus;

}
