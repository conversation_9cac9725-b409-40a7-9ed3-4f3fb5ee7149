package com.sgs.gpo.facade.model.tag.req;

import com.sgs.framework.core.base.BaseRequest;
import com.sgs.gpo.facade.model.tag.bo.TagBO;
import com.sgs.gpo.facade.model.tag.bo.TagValueBO;
import lombok.Data;

import java.util.List;

@Data
public class TagIntegrateReq extends BaseRequest {

    private String productLine;
    private String json;
    private String tagObjectType;
    // 记录需要新增的TagValue
    private List<TagValueBO> tagValueList;
    // BU 配置的可以选择的Tag列表
    private List<TagBO> allTagList;
}
