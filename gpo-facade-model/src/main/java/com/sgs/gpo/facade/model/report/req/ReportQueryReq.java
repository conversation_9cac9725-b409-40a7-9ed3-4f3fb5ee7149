package com.sgs.gpo.facade.model.report.req;

import com.sgs.framework.core.base.BaseQueryReq;
import com.sgs.framework.model.report.report.v2.ReportIdBO;
import lombok.Data;
import java.util.Set;

/**
 * 报告列表查询
 */
@Data
public class ReportQueryReq extends BaseQueryReq<ReportIdBO> {

    /**订单编码集合*/
    private Set<String> orderNoList;
    /**Report 标识集合*/
    private Set<String> reportIdList;
    /**Report 编码集合*/
    private Set<String> reportNoList;

    private String reportId;
    private String reportNo;
    private Set<String> testMatrixIds;

    private Set<String> testLineInstanceIdList;

    private Set<String> actualReportNoList;
    private Integer labId;

    private Boolean baseQuery = true;
    private boolean useAccurateQuery;
    private String subReportId;
}
