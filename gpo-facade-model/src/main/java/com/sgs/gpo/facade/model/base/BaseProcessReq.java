package com.sgs.gpo.facade.model.base;

import com.sgs.framework.core.base.StdBaseRequest;
import lombok.Data;

import java.util.Set;

@Data
public class BaseProcessReq extends StdBaseRequest {
    private Set<String> objectIdList;
    private String processCode;
    private String action;
    private Integer currentStatus;
    private Integer newStatus;
    private String modifiedBy;
    private boolean needStatusControl = true;
}
