package com.sgs.gpo.facade.model.otsnotes.job.req;

import com.sgs.framework.core.base.BaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @title: JobLabInReq
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/16 9:09
 */
@Data
public class JobLabInReq extends BaseRequest {
    /**
     * JobNos，基于Job LabIn
     */
    @ApiModelProperty(value = "Job编码集合")
    private Set<String> jobNos;
    /**
     * jobOwner
     */
    @ApiModelProperty(value = "Job负责人")
    private String jobOwner;
    private String secondJobOwner;
    private String thirdJobOwner;
}
