package com.sgs.gpo.facade.model.otsnotes.testdata.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@Data
public class TLDataHeaderSaveReq extends BaseRequest {
   private String orderNo;
   private boolean checkOneReport = false;
   private boolean needDueDateConfirm = true;
   private boolean checkFlag = false;
   private Set<String> reportIdList;
   private boolean excludeSubContractTestLine = true;
   private Set<String> subcontractIdList;
   /**
    * Assign Sample，Del/Cancel TL通过dubbo调用有事务问题，需要传入
    */
   private String triggerBy;
   private List<ReportMatrixItemReq> reportMatrixItemReqList;

   @Data
   public static class ReportMatrixItemReq implements Serializable {
      private String reportId;
      private String reportNo;
      private String testLineInstanceId;
   }
}
