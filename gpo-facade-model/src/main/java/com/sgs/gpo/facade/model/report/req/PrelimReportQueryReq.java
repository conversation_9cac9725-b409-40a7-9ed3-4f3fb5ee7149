package com.sgs.gpo.facade.model.report.req;

import com.sgs.framework.core.base.BaseQueryReq;
import lombok.Data;
import java.util.Date;

/**
 * 报告列表查询
 */
@Data
public class PrelimReportQueryReq extends BaseQueryReq {

    private String prelimResultNo;
    private String prelimResultId;
    private String reportId;
    private String buCode;
    private Date deliveredDateStart;
    private Date deliveredDateEnd;
    private Integer prelimResultStatus;

}
