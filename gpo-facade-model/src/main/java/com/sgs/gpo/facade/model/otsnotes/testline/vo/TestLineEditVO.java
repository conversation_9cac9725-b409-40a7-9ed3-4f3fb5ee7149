package com.sgs.gpo.facade.model.otsnotes.testline.vo;

import com.sgs.gpo.facade.model.otsnotes.testline.rsp.*;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @title: TestLineEditVO
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/26 9:55
 */
@Data
public class TestLineEditVO implements Serializable {
    private Integer reportLanguage;

    private List<TestLineEditCitationRsp> testLineEditCitationList;
    private List<TestLineEditConditionRsp> testLineEditConditionList;
    private List<TestLineEditAnalyteRsp> testLineEditAnalyteList;
    private List<TestLineEditLabSectionRsp> testLineEditLabSectionList;
    private List<TestLineEditLabTeamRsp> testLineEditLabTeamList;
    private List<TestLineEditSampleRsp> testLineEditSampleList;
    private List<TestLineEditWIRsp> testLineEditWIForSampleList;

    private boolean labSectionRequired;

    private boolean citationDisabled;
    private boolean conditionDisabled;
    private boolean analyteDisabled;
    private boolean labSectionDisabled;
    private boolean labTeamDisabled;
    private boolean sampleDisabled;

    /**
     * button按钮可编辑状态
     */
    private boolean saveBtnDisabled;
    private boolean delBtnDisabled;
    private boolean copyBtnDisabled;
    private boolean cancelBtnDisabled;
    private boolean seqBtnDisabled;
    private boolean pendingBtnDisabled;

}
