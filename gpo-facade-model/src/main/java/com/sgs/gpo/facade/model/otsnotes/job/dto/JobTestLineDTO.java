package com.sgs.gpo.facade.model.otsnotes.job.dto;

import com.sgs.gpo.facade.model.otsnotes.testline.dto.TestLineDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @title: JobTestLineDTO
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/8/22 15:21
 */
@Data
public class JobTestLineDTO {
    private String orderNo;
    private Integer orderStatus;
    private String jobNo;
    private Integer jobStatus;
    private List<TestLineDTO> testLineDTOList;
}
