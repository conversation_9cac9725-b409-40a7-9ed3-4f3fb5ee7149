package com.sgs.gpo.facade.model.otsnotes.testline.req;

import com.sgs.gpo.facade.model.base.BaseProcessReq;
import lombok.Data;

import java.util.Set;

@Data
public class TestLineProcessReq extends BaseProcessReq {
    private Set<String> testMatrixIdList;
    private String trigger;
    //有些操作需要填写一些备注信息，并记录到bizlog中去
    private String remark;
    /**
     * Matrix和TestLine的状态变化会相互联动，此参数用来控制是否相互影响
     * */
    private boolean triggerMatrix = true;
}
