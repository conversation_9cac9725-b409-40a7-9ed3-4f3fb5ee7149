package com.sgs.gpo.facade.model.kafka;

import lombok.Data;

import java.util.Map;

@Data
public class MaxWellMessage {
    /**变更数据所属的数据库*/
    private String database;
    /**表更数据所属的表*/
    private String table;
    /**数据变更类型:insert/update/delete/select*/
    private String type;
    /**数据变更发生的时间*/
    private Long ts;
    /**事务id*/
    private Long xid;

    /**Binlog中的具体位置*/
    private Long xoffset;

    /**事务提交标志，可用于重新组装事务*/
    private boolean commit;
    /**对于insert类型，表示插入的数据；对于update类型，标识修改之后的数据；对于delete类型，表示删除的数据*/
    private Map<String,Object> data;
    /**对于update类型，表示修改之前的数据，只包含变更字段*/
    private Map<String,Object> old;
}