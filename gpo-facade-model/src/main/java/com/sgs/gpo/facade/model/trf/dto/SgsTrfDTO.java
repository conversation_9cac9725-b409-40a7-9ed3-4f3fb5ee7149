package com.sgs.gpo.facade.model.trf.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SgsTrfDTO implements Serializable {
    private HeaderDTO header;
    private ProductDTO product;
    private Object careLabelList;
    private Object testSampleList;
    private Object testMatrixList;
    private Object testLineList;
    private ServiceRequirementDTO serviceRequirement;
    private List<CustomerListDTO> customerList;
    private List<SampleListDTO> sampleList;
    private List<AttachmentListDTO> attachmentList;

    @Data
    public static class HeaderDTO {
        private Integer refSystemId;
        private String trfNo;
        private Integer serviceType;
        private Object sampleLevel;
        private Object trfReportLevel;
        private Object parcelNoList;
        private Object trfSubmissionDate;
        private Object selfTestFlag;
        private OthersDTO others;
        private LabDTO lab;
        private ExtFieldsDTO extFields;
        private Object customerTrfContent;

        @Data
        public static class OthersDTO {
            private Object pending;
            private Object cancel;
            private Object remove;
            private Object repeatService;
            private Object trfRemark;

        }

        @Data
        public static class LabDTO {
            private String labCode;
            private String buCode;
            private Object labContact;
        }

        @Data
        public static class ExtFieldsDTO {
            private LowesDTO lowes;
            private CustomerDTO customer;

            @Data
            public static class LowesDTO {
                private Integer trfVersionNumber;
                private String labShortName;

            }

            @Data
            public static class CustomerDTO {
                private String trfVersion;

            }
        }
    }

    @Data
    public static class ProductDTO {
        private String templateId;
        private List<ProductAttrListDTO> productAttrList;


        @Data
        public static class ProductAttrListDTO {
            private String labelCode;
            private String labelName;
            private String labelValue;
            private Integer attrSeq;
            private String customerLabel;
            private Object dataType;
            private List<LanguageListDTO> languageList;

            @Data
            public static class LanguageListDTO {
                private Integer languageId;
                private String labelName;
                private String labelValue;
                private String customerLabel;
                private String value;
            }
        }
    }

    @Data
    public static class ServiceRequirementDTO {
        private ReportDTO report;
        private Object invoice;
        private Object sample;
        private Object otherRequestRemark;

        @Data
        public static class ReportDTO {
            private Integer reportLanguage;
            private Object reportForm;
            private Object reportHeader;
            private Object reportAddress;
            private Object languageList;
            private Object accreditation;
            private Object needConclusion;
            private Object needPhoto;
            private Object needDraft;
            private Object softcopy;
            private Object hardcopy;
            private Object splitReportBy;
            private Object commentFlag;
            private Object confirmCoverPageFlag;
            private Object certificateRequired;
            private Object activeIndicator;
            private Object lastModifiedTimestamp;
        }
    }

    @Data
    public static class CustomerListDTO {
        private Object customerInstanceId;
        private Object customerId;
        private Integer customerUsage;
        private Integer bossNo;
        private String customerGroupCode;
        private String customerName;
        private Object customerAddress;
        private Object marketSegmentCode;
        private Object marketSegmentName;
        private Object customerRefId;
        private Object paymentTerm;
        private Object blackFlag;
        private Object customerContactList;
        private Object languageList;
        private Object activeIndicator;
        private Object lastModifiedTimestamp;

    }

    @Data
    public static class SampleListDTO {
        private String templateId;
        private Object sampleInstanceId;
        private Object testSampleInstanceId;
        private Object sampleNo;
        private Object externalSampleNo;
        private String productItemNo;
        private Object conclusion;
        private List<SampleAttrListDTO> sampleAttrList;


        @Data
        public static class SampleAttrListDTO {
            private Object productInstanceId;
            private Object languageId;
            private Integer attrSeq;
            private Object seq;
            private String labelCode;
            private String labelName;
            private String labelValue;
            private Object fieldCode;
            private Object dataType;
            private String customerLabel;
            private Object displayInReport;
            private Object standardDff;
            private String value;
            private List<LanguageListDTOX> languageList;


            @Data
            public static class LanguageListDTOX {
                private Integer languageId;
                private String labelName;
                private String labelValue;
                private String customerLabel;
                private String value;

            }
        }
    }

    @Data
    public static class AttachmentListDTO {
        private Object fileType;
        private String fileName;
        private Object filePath;
        private Object toCustomerFlag;
        private String cloudId;
    }
}
