package com.sgs.gpo.facade.model.otsnotes.testdata.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sgs.gpo.facade.model.attachment.FileLinkDTO;
import com.sgs.gpo.facade.model.otsnotes.attachment.ObjectAttachmentDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class TLDataEntryDTO implements Serializable {
    private String id;
    private Integer labId;
    private String labCode;
    private String orderId;
    private String orderNo;
    private String reportId;
    private String reportNo;
    private String actualReportNo;
    private String status;
    private String dimension;
    private String extData;
    private String testLineInstanceId;
    private Integer testLineVersionId;
    private Integer reportStatus;
    private String testItemNo;
    private String sampleNos;
    private String labSectionName;
    private String labTeamName;
    private String testItemName;
    private String standardName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date testStartDate;
    private String engineer;
    private List<FileLinkDTO> fileLinkList;
    private List<ObjectAttachmentDTO> objectAttachmentDTOList;
}
