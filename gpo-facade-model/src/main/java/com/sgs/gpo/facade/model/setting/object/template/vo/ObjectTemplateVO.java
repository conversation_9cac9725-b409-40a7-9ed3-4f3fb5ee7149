package com.sgs.gpo.facade.model.setting.object.template.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sgs.framework.core.model.Lab;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/17 17:36
 */
@Data
public class ObjectTemplateVO implements Serializable {
    /**模版标识*/
    private String templateId;
    /**对象名称*/
    private String objectName;
    /**实验室信息*/
    private Lab lab;
    /**模版编码*/
    private String templateCode;
    /**模版描述*/
    private String templateDescription;
    /**有效性标识*/
    private Integer activeIndicator;
    /**创建人*/
    private String createBy;
    /**创建时间*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", locale = "zh", timezone = "GMT+8")
    private Date createDate;
    /**修改人*/
    private String modifiedBy;
    /**修改时间*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", locale = "zh", timezone = "GMT+8")
    private Date modifiedDate;
}
