package com.sgs.gpo.facade.model.report.req;

import com.sgs.framework.core.base.BaseRequest;
import com.sgs.gpo.facade.model.digital.dto.GenerateReportTemplateDTO;
import lombok.Data;

@Data
public class TestResultGenerateReq extends BaseRequest {

    private String reportId;
    private GenerateReportTemplateDTO generateTemplateDtoCN;
    private GenerateReportTemplateDTO generateTemplateDtoEN;
    private Integer reportActionType = 2;
    
}
