package com.sgs.gpo.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/14 10:10
 */
public enum Object {

    trf(1,"测试申请单"),
    enquiry(2,"询价单"),
    order(3,"订单");

    private int id;
    private String name;

    Object(int id,String name){
        this.id = id;
        this.name = name;
    }

    public int getId(){
        return id;
    }

    public String getName(){
        return name;
    }

    public static final Map<Integer, Object> maps = new HashMap<Integer, Object>() {
        {
            for (Object object : Object.values()) {
                put(object.getId(), object);
            }
        }
    };

    public static Object get(Integer id) {
        if (id == null || !maps.containsKey(id)) {
            return null;
        }
        return maps.get(id);
    }
}
