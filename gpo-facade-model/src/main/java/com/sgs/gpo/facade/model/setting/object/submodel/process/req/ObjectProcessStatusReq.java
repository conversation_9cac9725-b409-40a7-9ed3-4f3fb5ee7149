package com.sgs.gpo.facade.model.setting.object.submodel.process.req;

import com.sgs.framework.model.common.object.ObjectIdBO;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/12/11 16:27
 */
@Data
public class ObjectProcessStatusReq {
    /**实验室编码*/
    private Integer labId;
    /**对象*/
    private ObjectIdBO object;
    /**流程*/
    private String schemeId;
    /**状态类型*/
    private Set<String> statusTypeList;
    /**可编辑标识*/
    private String editableFlag;
    /**有效标识*/
    private String validFlag;
}
