package com.sgs.gpo.facade.model.print.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
public class PrintDataOrderReq extends BaseRequest {
    // 打印订单
    private String orderNo;
    // Sample对应的categoryList
    private List<String> categoryList;
    // TestLine对应的LabSectionList
    private List<String> labSectionList;
    // 订单下选择的ReportList
    private Set<String> reportIdList;

}
