package com.sgs.gpo.facade.model.otsnotes.testline.vo;

import com.sgs.framework.model.test.testline.v2.TestLineBO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @title: TestLineDetailVO
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/21 17:47
 */
@Data
public class TestLineDetailVO extends TestLineBO {
    private String testItemNameDisplay;
    private String citationNameDisplay;

    private String testConditionDisplay;
    private String testAnalyteDisplay;
    private String testSampleDisplay;
    private String wiForSampleDisplay;
    private String quotationRemarkDisplay;
    private String remarkDisplay;
    private String labSectionDisplay;
    private String labTeamDisplay;
    private String ppNotesAndWiForCsDisplay;
    private String wiForCsDisplay;
    private String ppNotesDisplay;
    private String statusDisplay;
    private String buCodeDisplay;

    private String testLineInstanceId;
    private Integer testLineStatus;
    private Boolean pendingFlag;
    private Integer testLineType;
    private Integer testLineId;

    private Integer reportLanguage;
    private String orderId;

    private List<Integer> selectedAnalyteIdList;
    private List<Long> selectedLabSectionBaseId;
    private Integer selectedCitationVersionId;
    private List<String> selectedTestSampleIdList;
    private String labTeamCode;
    private Long sampleSegegrationWIID;
    private String sampleSegegrationWIText;

}
