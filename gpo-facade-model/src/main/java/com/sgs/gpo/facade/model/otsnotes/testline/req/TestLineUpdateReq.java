package com.sgs.gpo.facade.model.otsnotes.testline.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @title: TestLineUpdateReq
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/29 16:55
 */
@Data
public class TestLineUpdateReq extends BaseRequest {
    private String testLineInstanceId;
    private List<CitationUpdateReq> citationUpdateReqList;
    private List<ConditionUpdateReq> conditionUpdateReqList;
    private List<AnalyteUpdateReq> analyteUpdateReqList;
    private List<LabSectionUpdateReq> labSectionUpdateReqList;
    private List<AssignSampleReq> assignSampleReqList;

}
