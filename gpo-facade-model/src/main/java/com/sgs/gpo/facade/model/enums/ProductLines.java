package com.sgs.gpo.facade.model.enums;

import lombok.Getter;

@Getter
public enum ProductLines {
    GPO("GPO","GPO"), SOFTLINE("SL","Soft Line"),HARDLINE("HL","Hard Line");
    private final String code;
    private final String name;

    ProductLines(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ProductLines getProductLine(String code) {
        ProductLines[] productLines = values();
        for(ProductLines productLine : productLines){
            if(productLine.getCode().equals(code)){
                return productLine;
            }
        }
        return null;
    }

}
