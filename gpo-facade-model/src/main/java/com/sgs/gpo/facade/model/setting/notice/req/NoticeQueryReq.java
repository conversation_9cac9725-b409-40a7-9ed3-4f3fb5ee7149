package com.sgs.gpo.facade.model.setting.notice.req;

import com.sgs.framework.core.base.BaseIdBO;
import com.sgs.framework.core.base.BaseQueryReq;
import com.sgs.gpo.facade.model.setting.notice.bo.NoticeIdBO;
import lombok.Data;

import java.util.Date;

@Data
public class NoticeQueryReq extends BaseQueryReq<NoticeIdBO> {

    private Integer systemId;
    private String noticeTitle;
    private String keyword;
    private String createdBy;
    private Date createdDate;

}
