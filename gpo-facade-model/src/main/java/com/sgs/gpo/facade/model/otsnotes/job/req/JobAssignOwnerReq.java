package com.sgs.gpo.facade.model.otsnotes.job.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @title: JobAssignOwnerReq
 * @projectName gpo-micro-service
 * @date 2023/7/18 11:13
 */
@Data
public class JobAssignOwnerReq extends BaseRequest {
    private Set<String> jobNoList;
    private String jobOwner;
    private String secondJobOwner;
    private String thirdJobOwner;
    private String engineer;
    private List<String> testLineInstanceIDs;
}
