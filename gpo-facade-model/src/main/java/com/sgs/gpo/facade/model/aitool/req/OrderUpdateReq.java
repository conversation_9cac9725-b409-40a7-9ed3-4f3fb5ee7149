package com.sgs.gpo.facade.model.aitool.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.List;

@Data
public class OrderUpdateReq extends BaseRequest {
    //对象业务号
    private String objectNo;
    //对象类型
    private String objectType;
    // 申请方
    private CustomerUpdateReq applicant;
    // 付款方
    private CustomerUpdateReq payer;
    // 购买方
    private CustomerUpdateReq buyer;
    // 代理商
    private CustomerUpdateReq agent;
    // 供应商
    private CustomerUpdateReq supplier;
    // 制造商
    private CustomerUpdateReq manufacturer;
    // ReportInfo 出报告要求
    private ReportInfoUpdateReq reportInfo;
    // 报告测试要求
    private ServiceRequirementUpdateReq serviceRequirement;
    // DFF更新
    private List<DFFUpdateReq> dffList;
}
