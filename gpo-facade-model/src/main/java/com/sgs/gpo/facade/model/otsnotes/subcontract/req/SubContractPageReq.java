package com.sgs.gpo.facade.model.otsnotes.subcontract.req;

import com.sgs.framework.core.base.BaseRequest;
import com.sgs.framework.model.annotation.SearchValidate;
import com.sgs.preorder.facade.model.req.CustomerReq;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @title: SubContractPageReq
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/8 13:07
 */
@Data
public class SubContractPageReq extends BaseRequest {
    private Integer labId;
    private String labCode;
    @SearchValidate(buSettingCode = "subcontractNo")
    private String subcontractNo;
    @SearchValidate(buSettingCode = "subcontractLabCodeList")
    private List<String> subcontractLabCodeList;
    @SearchValidate(buSettingCode = "orderNo")
    private String orderNo;
    @SearchValidate(buSettingCode = "batchOrderNo")
    private String batchOrderNo;
    private List<String> orderNoList;
    @SearchValidate(buSettingCode = "batchSubcontractNo")
    private String batchSubcontractNo;
    private List<String> subcontractNoList;
    @SearchValidate(buSettingCode = "subcontractStatus")
    private List<String> subcontractStatusList;
    @SearchValidate(buSettingCode = "createDateArr")
    private String createBeginDate;
    @SearchValidate(buSettingCode = "createDateArr")
    private String createEndDate;
    @SearchValidate(buSettingCode = "expectDueDateArr")
    private String expectBeginDate;
    @SearchValidate(buSettingCode = "expectDueDateArr")
    private String expectEndDate;

    @SearchValidate(buSettingCode = "referenceDueDateArr")
    private String referenceBeginDueDate;
    @SearchValidate(buSettingCode = "referenceDueDateArr")
    private String referenceEndDueDate;

    @SearchValidate(buSettingCode = "cSNameList")
    private List<String> cSNameList;
    @SearchValidate(buSettingCode = "externalOrderDueDateMatch")
    private String externalOrderDueDateMatch;

    @SearchValidate(buSettingCode = "subContractRemark")
    private String subContractRemark;

    private String blockTops;

    private Boolean detailFlag = false;
    private Boolean ignoreCancelled = true;

    private String subContractOrder;

    private String createdBy;
    private CustomerReq applicant;
    @SearchValidate(buSettingCode = "referenceNo")
    private String referenceNo;
}
