package com.sgs.gpo.facade.model.otsnotes.testline.req;

import com.sgs.framework.core.base.BaseRequest;
import com.sgs.framework.model.order.order.OrderBO;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @title: TestLineLabOutReq
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/8/21 11:08
 */
@Data
public class TestLineLabInReq extends BaseRequest {
    private Set<String> testLineInstanceIdList;
    private String engineer;
    private String jobOwner;
    private Date testStartDate;
    private Long labSectionId;
    private Long labSectionBaseId;
    private List<OrderBO> orderBOList;
}
