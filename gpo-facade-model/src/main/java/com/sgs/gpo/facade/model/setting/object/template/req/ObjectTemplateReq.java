package com.sgs.gpo.facade.model.setting.object.template.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/17 17:15
 */
@Data
public class ObjectTemplateReq extends BaseRequest {
    /**模版标识*/
    private String templateId;
    /**实验室标识*/
    private Integer labId;
    /**国家标识*/
    private Integer countryId;
    /**业务线标识*/
    private Integer buId;
    /**对象标识*/
    private Long objectId;
    /**数据有效性标记*/
    private Integer activeIndicator;
    /**模版编码*/
    private String templateCode;
    /**模版名称*/
    private String templateDescription;
}
