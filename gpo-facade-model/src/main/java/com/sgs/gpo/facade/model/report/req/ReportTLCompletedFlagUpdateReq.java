package com.sgs.gpo.facade.model.report.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @title: ReportTestLineUpdateReq
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2024/3/15 14:11
 */
@Data
public class ReportTLCompletedFlagUpdateReq extends BaseRequest {
    private Set<String> reportIdList;
    private Set<String> testLineInstanceIdList;
    private Integer testCompletedFlag = 0;
    private String modifiedBy;
}
