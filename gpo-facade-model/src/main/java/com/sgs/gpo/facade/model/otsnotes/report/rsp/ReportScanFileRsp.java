package com.sgs.gpo.facade.model.otsnotes.report.rsp;

import com.sgs.gpo.facade.model.otsnotes.reportmatrix.vo.ReportMatrixVO;
import com.sgs.gpo.facade.model.scantool.rsp.ScanToolFile;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @title: ReportScanFileRsp
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2024/3/25 17:50
 */
@Data
public class ReportScanFileRsp {
    private String reportNo;
    private String orderNo;
    private List<ReportMatrixVO> reportMatrixVOList;
    private List<ScanToolFile> scanToolFileList;
}
