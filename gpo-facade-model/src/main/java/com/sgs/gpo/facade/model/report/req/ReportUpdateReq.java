package com.sgs.gpo.facade.model.report.req;

import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseRequest;
import com.sgs.framework.model.order.trf.TrfBO;
import com.sgs.framework.model.order.v2.OrderBO;
import lombok.Data;

import java.util.Set;

@Data
public class ReportUpdateReq extends BaseRequest {

    private String reportId;
    private String reportNo;
    private String labCode;
    private String userName;
    private String tsPerson;
    private String locationCode;

    private OrderBO order;
    private TrfBO trf;

    // Report关联的TestSampleId的集合
    private Set<String> testSampleIdList;
    //Report Pending
    private String reasonType;
    private String remark;
    private UserInfo userInfo;

}
