package com.sgs.gpo.facade.model.digital.dto;

import lombok.Data;

@Data
public class GenerateReportTemplateDTO {
    private Integer templateSettingID;
    private String templateName;
    private String container;
    private String containerTemplateFilePath;
    private String otsFlag;
    private String templateFilePath;
    private String templateID;
    private String dataObject;
    private Integer buSubTypeID;
    private String templateTypeId;
    private String sectionId;
    private Integer applicationID;
    private Integer languageID;
    private String sectionName;
    private Integer protocolStructureFlag;
}
