package com.sgs.gpo.facade.model.digital.dto;

import lombok.Data;

import java.util.List;

@Data
public class UserSignatureDTO {

	private String autographId;
	private String id;
	private String regionAccount;
	private String name;
	private String signUrl;
	private String signatureName;
	private Integer isAuthSignUser;
	private String languageCode;
	private List<String> authSignTypeKey;
	private List<String> authSignTypeValue;
	private String email;

}
