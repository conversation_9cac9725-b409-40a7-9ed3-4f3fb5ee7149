package com.sgs.gpo.facade.model.setting.objectshare.bo;

import com.sgs.framework.core.base.BaseBO;
import com.sgs.framework.core.base.BaseIdBO;
import com.sgs.gpo.facade.model.enums.Object;
import com.sgs.gpo.facade.model.enums.ObjectShareMode;
import com.sgs.gpo.facade.model.enums.Organization;
import lombok.Data;

/**
 * 对象共享规则
 * <AUTHOR>
 * @date 2023/6/14 10:08
 */

@Data
public class ObjectShareBO extends BaseIdBO<Long> {

    /**业务对象*/
    private Object object;
    /**业务组织*/
    private Organization organization;
    /**共享模式*/
    private ObjectShareMode mode;
    /**分享方*/
    private String host;
    /**共享到*/
    private String shareTo;
    /**共享到，别名*/
    private String shareToAlias;
    /**权限,JSON*/
    private String auth;
    /**顺序*/
    private int seq;

}
