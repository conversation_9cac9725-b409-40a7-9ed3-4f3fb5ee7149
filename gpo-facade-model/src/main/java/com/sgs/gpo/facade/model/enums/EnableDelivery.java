package com.sgs.gpo.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum EnableDelivery {
    DEFAULT_VALUE(0, "默认值"),
    MONTHLY_PAYMENT(1, "月结客户"),
    PAID(2, "Paid"),
    UPLOAD_PAID_UP(1<<2, "Upload paid up"),
    DELIVERY_APPROVE(1<<3, "Delivery Approve"),
    FREE_ORDER(1<<4, "Order is free");

    private Integer status;
    private String message;

    EnableDelivery(Integer status, String message) {
        this.status = status;
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public Integer getStatus() {
        return status;
    }

    public static final Map<Integer, EnableDelivery> maps = new HashMap<Integer, EnableDelivery>() {
        private static final long serialVersionUID = -8986866330615001847L;

        {
            for (EnableDelivery enu : EnableDelivery.values()) {
                put(enu.status, enu);
            }
        }
    };

    public static String getMessage(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status).getMessage();
    }

    public static boolean check(Integer type, EnableDelivery enableDelivery) {
        if (type == null || enableDelivery == null){
            return false;
        }
        return (type & enableDelivery.getStatus()) > 0;
    }
}
