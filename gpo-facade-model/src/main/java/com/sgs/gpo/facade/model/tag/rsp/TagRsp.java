package com.sgs.gpo.facade.model.tag.rsp;

import com.sgs.gpo.facade.model.tag.bo.TagValueBO;
import lombok.Data;

import java.util.List;

@Data
public class TagRsp {

    private String tagId;
    private String tagName;
    private Boolean isHand;
    private Boolean isCheck;
    private Boolean isSave;
    private Boolean isRequired;
    private List<TagValueBO> tagValues;
    private List<String> selectedValue;
    private String productLineCode;
}
