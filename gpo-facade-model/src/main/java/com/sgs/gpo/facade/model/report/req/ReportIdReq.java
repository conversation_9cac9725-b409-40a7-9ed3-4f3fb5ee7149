package com.sgs.gpo.facade.model.report.req;

import com.sgs.framework.core.base.StdBaseRequest;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @title: ReportNoReq
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/7/26 17:15
 */
@Data
public class ReportIdReq extends StdBaseRequest {
    private Set<String> reportNoList;
    private Set<String> reportIdList;
    private String reportId;
    private String reportNo;
    private Set<String> testMatrixIds;
}
