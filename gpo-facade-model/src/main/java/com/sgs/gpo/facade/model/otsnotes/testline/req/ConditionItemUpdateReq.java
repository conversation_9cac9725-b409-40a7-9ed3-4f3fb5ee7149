package com.sgs.gpo.facade.model.otsnotes.testline.req;

import com.sgs.framework.core.base.BaseRequest;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @title: ConditionItemUpdateReq
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/28 15:54
 */
@Data
@ApiModel(value = "ConditionItemUpdateReq",description = "conditionType数据")
public class ConditionItemUpdateReq extends BaseRequest {
    private boolean conditionTypeBlock;
    private boolean procedureCondition;
    private Integer testConditionTypeId;
    private Integer testConditionTypeBaseId;
    private String testConditionTypeName;
    private Integer testConditionTypeBlockLevel;
    private List<ConditionSubItemUpdateReq> conditionSubItemUpdateReqList;
}
