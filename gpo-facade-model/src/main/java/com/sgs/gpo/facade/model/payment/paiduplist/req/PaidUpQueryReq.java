package com.sgs.gpo.facade.model.payment.paiduplist.req;

import com.sgs.framework.core.base.BaseRequest;
import com.sgs.framework.model.annotation.SearchValidate;
import com.sgs.gpo.facade.model.preorder.enquiry.req.CustomerReq;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
public class PaidUpQueryReq extends BaseRequest {
    private static final long serialVersionUID = 6616597758066794397L;
    private String id;
    @SearchValidate(buSettingCode = "paidUpNo")
    private String paidUpNo;
    /**
     * 订单号
     */
    @SearchValidate(buSettingCode = "orderNo")
    private String orderNo;
    /**
     * 汇款单号
     */
    @SearchValidate(buSettingCode = "remittanceNo")
    private String remittanceNo;
    /**
     * 汇款到账时间
     */
    @SearchValidate(buSettingCode = "remittanceReceivedDate")
    private String remittanceReceivedDateStart;
    @SearchValidate(buSettingCode = "remittanceReceivedDate")
    private String remittanceReceivedDateEnd;
    /**
     * 付款人
     */
    @SearchValidate(buSettingCode = "payer")
    private CustomerReq payer;
    private List<Long> payerNumberList;
    private List<String> payerCnNameList;
    private List<String> payerEnNameList;
    /**
     * 实际付款人
     */
    @SearchValidate(buSettingCode = "paidBy")
    private String paidBy;
    /**
     * 付款时间
     */
    @SearchValidate(buSettingCode = "paidDate")
    private String paidDateStart;
    @SearchValidate(buSettingCode = "paidDate")
    private String paidDateEnd;
    /**
     * 申请人
     */
    @SearchValidate(buSettingCode = "applicant")
    private CustomerReq applicant;
    private List<Long> applicantNumberList;
    private List<String> applicantCnNameList;
    private List<String> applicantEnNameList;

    @SearchValidate(buSettingCode = "csName")
    private String csName;
    /**
     * 创建时间
     */
    @SearchValidate(buSettingCode = "createdDate")
    private String createdDateStart;
    @SearchValidate(buSettingCode = "createdDate")
    private String createdDateEnd;



    private String labCode;
    private Integer page;
    private Integer rows;

    @SearchValidate(buSettingCode = "createdBy")
    private String createdBy;
    @SearchValidate(buSettingCode = "remark")
    private String remark;
    @SearchValidate(buSettingCode = "paymentStatus")
    private String paymentStatus;
    @SearchValidate(buSettingCode = "paymentStatusList")
    private List<String> paymentStatusList;

    Set<String> orderIdList;

    Set<String> orderNoList;
}
