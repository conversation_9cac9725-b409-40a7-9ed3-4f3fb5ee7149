package com.sgs.gpo.facade.model.payment.paymentlist.req;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class Invoice {

    List<InvoiceBossOrder> bossOrderList;
    String invoiceNo;
    String invoiceCurrency;
    BigDecimal invoiceTotalAmount;
    String originalInvoiceNo;
    String originalBossOrderNo;
    String invoiceMainCurrency;
    BigDecimal invoiceMainPreTaxAmount;
    BigDecimal invoiceMainTaxAmount;
    Date invoiceDueDate;
    List<InvoiceReceipt> receiptList;
}
