package com.sgs.gpo.facade.model.otsnotes.report.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.Date;

@Data
public class ReportCertificateSaveReq extends BaseRequest {
    private String id;
    private String reportId;
    private String certificateNo;
    private String certificateType;
    private Date expireDate;
    private Integer toCustomerFlag;
    private Integer deliverToCustomerFlag;
    private String remark;
    private Integer status;
    private String prefix;

    private String orderProductCategory;
}
