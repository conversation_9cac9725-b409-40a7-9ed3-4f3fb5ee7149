package com.sgs.gpo.facade.model.system.req;

import com.sgs.framework.core.base.BaseQueryReq;
import com.sgs.framework.core.model.date.DateRange;
import com.sgs.gpo.facade.model.setting.notice.bo.NoticeIdBO;
import lombok.Data;

import java.util.Date;

@Data
public class SystemLogQueryReq extends BaseQueryReq {

    private DateRange createdDate;
    private String objectNo;
    private String operationType;
}
