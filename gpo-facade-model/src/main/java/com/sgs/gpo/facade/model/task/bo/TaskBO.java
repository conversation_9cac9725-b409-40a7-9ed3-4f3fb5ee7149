package com.sgs.gpo.facade.model.task.bo;

import com.sgs.framework.core.base.BaseIdBO;
import lombok.Data;

@Data
public class TaskBO extends BaseIdBO<String> {

    // 执行任务类型(MQ Interface)
    private String taskType;
    // 执行接口(Topic URL)
    private String callFunction;
    // 接口参数
    private String param;
    // Location
    private String locationCode;
    // 定时任务延迟执行时间，
    private int delayTime;
}
