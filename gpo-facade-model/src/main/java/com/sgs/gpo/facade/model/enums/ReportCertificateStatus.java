package com.sgs.gpo.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum ReportCertificateStatus {
    VALID(1, "Valid"),
    CANCELED(9, "Cancelled");
    private Integer status;
    private String name;

    ReportCertificateStatus(Integer status, String name) {
        this.status = status;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public Integer getStatus() {
        return status;
    }

    public static final Map<Integer, ReportCertificateStatus> maps = new HashMap<Integer, ReportCertificateStatus>() {
        {
            for (ReportCertificateStatus enu : ReportCertificateStatus.values()) {
                put(enu.status, enu);
            }
        }
    };

    public static String getName(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status).getName();
    }

    public static boolean check(Integer status, ReportCertificateStatus reportCertificateStatus) {
        if (status == null || reportCertificateStatus == null){
            return false;
        }
        return status.intValue()== reportCertificateStatus.status ;
    }
}
