package com.sgs.gpo.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/14 10:10
 */
public enum ObjectShareMode {

    share(1,"共享"),
    parallel(2,"并行");

    private int id;
    private String name;

    ObjectShareMode(int id, String name){
        this.id = id;
        this.name = name;
    }

    public int getId(){
        return id;
    }

    public String getName(){
        return name;
    }

    public static final Map<Integer, ObjectShareMode> maps = new HashMap<Integer, ObjectShareMode>() {
        {
            for (ObjectShareMode objectShareMode : ObjectShareMode.values()) {
                put(objectShareMode.getId(), objectShareMode);
            }
        }
    };

    public static ObjectShareMode get(Integer id) {
        if (id == null || !maps.containsKey(id)) {
            return null;
        }
        return maps.get(id);
    }
}
