package com.sgs.gpo.facade.model.report.req;

import com.sgs.framework.core.base.BaseRequest;
import com.sgs.framework.core.model.Lab;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * 报告文件查询参数
 */
@Data
public class ReportFileQueryReq extends BaseRequest {

    /**Report ID集合*/
    private Set<String> reportIdList;
    /**Report 编码集合*/
    private Set<String> reportNoList;
    /**Report File 类型**/
    private Integer reportFileType;
    /**Report File 状态**/
    private List<Integer> reportFileStatus;
    /**Report File ID集合*/
    private List<String> reportFileIdList;
}
