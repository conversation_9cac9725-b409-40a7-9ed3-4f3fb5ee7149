package com.sgs.gpo.facade.model.report.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.Date;
import java.util.Set;

@Data
public class ReportMatrixSeqItemUpdateReq extends BaseRequest {
    private String testLineInstanceId;
    private Set<String> subTestLineInstanceIdList;
    private Long sequence = 0L;
    private String reportMatrixRelId;
    private Date modifiedDate;
    private String modifiedBy;
}
