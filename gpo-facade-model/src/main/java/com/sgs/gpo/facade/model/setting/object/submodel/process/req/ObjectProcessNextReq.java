package com.sgs.gpo.facade.model.setting.object.submodel.process.req;

import com.sgs.framework.model.common.object.ObjectBO;
import com.sgs.framework.model.common.object.action.ObjectActionIdBO;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/11 16:27
 */
@Data
public class ObjectProcessNextReq {
    private Integer labId;
    private ObjectBO objectBO;
    private String schemeId;
    private Integer currentStatus;
    private ObjectActionIdBO action;
    private Map<String,Object> variable;
}
