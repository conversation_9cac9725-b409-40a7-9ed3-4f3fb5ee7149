package com.sgs.gpo.facade.model.payment.costlist.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sgs.framework.core.base.BaseIdBO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @title: CostListBo
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/6/1515:14
 */
@Data
public class CostListBO extends BaseIdBO<String> {
    private String orderId;
    private String buCode;
    private Integer buId;
    private String locationCode;
    private Integer locationId;
    private String orderNo;
    private String enquiryNo;
    private String enquiryId;
    private String externalOrderNo;
    private String productCategory;
    private Integer operationMode;
    private String subcontractFrom;
    private String customerRefNo;
    private String labCode;
    private String oldOrderNo;
    private Integer orderStatus;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createdDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date expectedOrderDueDate;
    private Integer sampleCount;
    private String serviceItemName;
    private Integer serviceType;
    private BigDecimal mainCurrencyTotalPrice;
    private BigDecimal subContractFee;
    private BigDecimal mainCurrencySubContractFee;
    private String subContractFeeFormat;
    private BigDecimal diffPrice;
    private String diffPriceFormat;
    private String contractPerson;
    private String csName;
    private String remark;
    private String quoteCurrencyId;
    private String referenceNo;
    private String currencyCode;
    private String mainCurrencyCode;
    private String caseType;
    private String costCodeFlag;
    private String quotationFlag;
    private String invoiceConfirmFlag;
    private Date invoiceConfirmDate;
    private Date orderConfirmDate;
    private String totalPrice;
    private String createdBy;
    private Integer orderCategory;
    private Integer toBossFlag;
    private Date serviceStartDate;
    private String responsibleTeamCode;
    private Integer chargingStatus;
    private String returnOrderNo;
    private String csa;
    private String actualFeeCurrency;
    private BigDecimal actualFee;
}
