package com.sgs.gpo.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: PrintBarCodeTypeEnums
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2024/4/9 17:03
 */
public enum PrintBarCodeTypeEnums {
    OrderBarcode(0, "OrderBarcode"),
    SampleBarcode(1, "SampleBarcode"),
    MatrixBarcode(2, "MatrixBarcode"),
    JobBarcode(3, "JobBarcode"),
    SubContractBarcode(4, "SubContractBarcode"),
    TestItemBarcode(5, "TestItemBarcode"),
            ;

    private Integer type;
    private String code;

    PrintBarCodeTypeEnums(Integer type, String code) {
        this.type = type;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public Integer getType() {
        return type;
    }

    static Map<Integer, PrintBarCodeTypeEnums> typeMaps = new HashMap();
    static Map<String, PrintBarCodeTypeEnums> codeMaps = new HashMap<>();
    static {
        for (PrintBarCodeTypeEnums enu : PrintBarCodeTypeEnums.values()) {
            typeMaps.put(enu.getType(), enu);
            codeMaps.put(enu.getCode(), enu);
        }
    }

    public static PrintBarCodeTypeEnums findType(Integer type) {
        if (type == null || !typeMaps.containsKey(type.intValue())) {
            return null;
        }
        return typeMaps.get(type.intValue());
    }

    /**
     *
     * @param type
     * @param asyncType
     * @return
     */
    public static boolean check(Integer type, PrintBarCodeTypeEnums asyncType) {
        if (type == null || !typeMaps.containsKey(type.intValue())){
            return false;
        }
        return typeMaps.get(type.intValue()) == asyncType;
    }

    public static boolean check(Integer asyncType) {
        if (asyncType == null){
            return false;
        }
        return typeMaps.containsKey(asyncType);
    }

}
