package com.sgs.gpo.facade.model.otsnotes.report.rsp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ReportCertificateRsp implements Serializable {
    private String id;
    private String reportId;
    private String certificateNo;
    private String certificateType;
    private String certificateTypeDisplay;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expireDate;
    private Integer toCustomerFlag;
    private Integer deliverToCustomerFlag;
    private String remark;
    private Integer status;
    private String statusDisplay;
    private Integer activeIndicator;
    private String createdBy;
    private Date createdDate;
    private String modifiedBy;
    private Date modifiedDate;

}
