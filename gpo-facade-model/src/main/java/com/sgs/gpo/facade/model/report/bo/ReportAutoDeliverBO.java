package com.sgs.gpo.facade.model.report.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.Date;


@Data
public class ReportAutoDeliverBO {
    private String taskId;
    private String orderNo;
    private String reportNo;
    private String applicantName;
    private String applicantNameCN;
    private String applicantNameEN;
    private String buyerName;
    private String buyerNameCN;
    private String buyerNameEN;
    private String csName;
    private String createdBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approverDate;
    private Integer reportStatus;
    private Integer orderStatus;
    private String orderType;
    private Integer taskStatus;
    private String taskMessage;
}
