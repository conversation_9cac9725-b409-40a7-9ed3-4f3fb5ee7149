package com.sgs.gpo.facade.model.barcode.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @title: printBarCodeVO
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2024/4/9 16:29
 */
@Data
public class BarcodePrintVO {

    private Integer barcodeType;

    @ApiModelProperty(value = "expectedOrderDueDate")
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh")
    private Date expectedOrderDueDate;

    @ApiModelProperty(value = "expectedOrderDueDate")
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh")
    private Date testLineDueDate;

    @ApiModelProperty(value = "orderConfirmDate")
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh")
    private Date orderConfirmDate;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "外部订单号")
    private String externalOrderNo;

    @ApiModelProperty(value = "refNo")
    private String refNo;

    @ApiModelProperty(value = "returnResidueSample")
    private String returnResidueSample;

    @ApiModelProperty(value = "sampleNo")
    private String sampleNo;

    @ApiModelProperty(value = "subCs")
    private String subCs;

    @ApiModelProperty(value = "testLineItemName")
    private String testLineItemName;

    @ApiModelProperty(value = "testStandardName")
    private String testStandardName;

    @ApiModelProperty(value = "CSName")
    private String csName;

    @ApiModelProperty(value = "barcodeObjectNo")
    private String barcodeObjectNo;

    @ApiModelProperty(value = "条码")
    private String barcode;

    @ApiModelProperty(value = "二维码")
    private String qrCode;

    private Integer orderSeq;
    private Long seq;
}
