package com.sgs.gpo.facade.model.report.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @title: ReportExtForTLUpdateReq
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2024/2/28 17:43
 */
@Data
public class ReportExtForTLUpdateReq extends BaseRequest {
    private Set<String> reportIdList;
    private Set<String> testLineInstanceIdList;
}
