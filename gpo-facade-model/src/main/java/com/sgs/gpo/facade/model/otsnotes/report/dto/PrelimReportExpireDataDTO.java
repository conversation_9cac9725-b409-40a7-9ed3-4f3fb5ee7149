package com.sgs.gpo.facade.model.otsnotes.report.dto;

import lombok.Data;

import java.util.Date;

@Data
public class PrelimReportExpireDataDTO {
    private String prelimResultId;
    private Integer buId;
    private String buCode;
    private Integer locationId;
    private String locationCode;
    private String reportId;
    private String reportNo;
    private String orderId;
    private String orderNo;
    private String prelimResultNo;
    private String csName;
    private String csEmail;
    private Integer prelimResultStatus;
    private Date deliveredDate;
    private String applicantNameCN;
    private String applicantNameEN;
    private String payerNameCN;
    private String payerNameEN;
    private String buyerNameCN;
    private String buyerNameEN;
    private String discountPersonEmail;
    private String responsibleEngineer;
    private String reviewBy;

}
