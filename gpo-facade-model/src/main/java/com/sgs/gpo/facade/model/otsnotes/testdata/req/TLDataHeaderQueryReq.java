package com.sgs.gpo.facade.model.otsnotes.testdata.req;

import com.sgs.framework.core.base.BaseRequest;
import com.sgs.framework.model.annotation.SearchValidate;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
public class TLDataHeaderQueryReq extends BaseRequest {
    private String testDataHeaderId;
    private Integer labId;
    private String labCode;
    @SearchValidate(buSettingCode = "orderNo")
    private String orderNo;
    @SearchValidate(buSettingCode = "reportNo")
    private String reportNo;
    private String reportId;
    private Set<String> orderNoList;
    private Set<String> reportNoList;
    private Set<String> reportIdList;
    @SearchValidate(buSettingCode = "tlReportStatusList",tables = {"tb_test_line_instance"})
    private Set<String> tlReportStatusList;
    private Set<String> testLineInstanceIdList;
    @SearchValidate(buSettingCode = "testItemNo",tables = {"tb_test_line_instance"})
    private String testItemNo;
    @SearchValidate(buSettingCode = "testItemNos",tables = {"tb_test_line_instance"})
    private String testItemNos;
    @SearchValidate(buSettingCode = "testItemNos",tables = {"tb_test_line_instance"})
    private List<String> testItemNoList;
    @SearchValidate(buSettingCode = "engineerList",tables = {"tb_test_line_instance"})
    private List<String> engineerList;
    @SearchValidate(buSettingCode = "testLineStatusList",tables = {"tb_test_line_instance"})
    private Set<String> testLineStatusList;
    private Set<String> idList;
    @SearchValidate(buSettingCode = "testDueDate",tables = {"tb_test_line_instance"})
    private String testDueDateFrom;
    @SearchValidate(buSettingCode = "testDueDate",tables = {"tb_test_line_instance"})
    private String testDueDateTo;
    @SearchValidate(buSettingCode = "reportedDueDate")
    private String reportDueDateFrom;
    @SearchValidate(buSettingCode = "reportedDueDate")
    private String reportDueDateTo;
    @SearchValidate(buSettingCode = "testLineIdList",tables = {"tb_test_line_instance"})
    private Set<Integer> testLineIdList;
    @SearchValidate(buSettingCode = "citationVersionIdList",tables = {"tb_test_line_instance"})
    private Set<Long> citationVersionIdList;
    @SearchValidate(buSettingCode = "labTeamCodeList",tables = {"tb_test_line_instance"})
    private List<String> labTeamCodeList;
    @SearchValidate(buSettingCode = "labSectionIdList",tables = {"tb_test_line_instance","tb_testline_labsection_relationship"})
    private List<Long> labSectionIdList;
    private boolean queryPage = false;
    /*
    *
    * 默认为false，当为true时，表示查询条件使用了TestLine的相关字段查询，需要动态关联tb_test_line_instance表
    * */
    private boolean useTlCondition = false;
    private boolean useTlLabSectionCondition = false;
}
