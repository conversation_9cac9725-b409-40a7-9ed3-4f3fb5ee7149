package com.sgs.gpo.facade.model.aitool.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

@Data
public class ReportInfoUpdateReq extends BaseRequest {

    // Same As Applicant标记
    private Integer sameAsApplicant;
    // 报告语言
    private String reportLanguage;
    // 报告Header英文信息
    private String reportHeaderEn;
    // 报告Header中文信息
    private String reportHeaderCn;
    // 报告地址英文
    private String reportAddressEn;
    // 报告地址中文
    private String reportAddressCn;

}
