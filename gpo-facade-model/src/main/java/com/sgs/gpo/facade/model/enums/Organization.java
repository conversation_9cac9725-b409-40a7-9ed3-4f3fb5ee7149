package com.sgs.gpo.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/14 10:10
 */
public enum Organization {

    bu (1,"业务部门"),
    location (2,"区域"),
    lab (3,"实验室"),
    labSection (4,"实验室区块"),
    labTeam (5,"实验室团队"),
    account (6,"账号");

    private int id;
    private String name;

    Organization(int id,  String name){
        this.id = id;
        this.name = name;
    }

    public int getId(){
        return id;
    }

    public String getName(){
        return name;
    }

    public static final Map<Integer, Organization> maps = new HashMap<Integer, Organization>() {
        {
            for (Organization organization : Organization.values()) {
                put(organization.getId(), organization);
            }
        }
    };

    public static Organization get(Integer id) {
        if (id == null || !maps.containsKey(id)) {
            return null;
        }
        return maps.get(id);
    }
}
