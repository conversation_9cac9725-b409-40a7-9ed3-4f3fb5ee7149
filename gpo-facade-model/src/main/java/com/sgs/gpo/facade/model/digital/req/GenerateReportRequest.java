package com.sgs.gpo.facade.model.digital.req;

import com.sgs.framework.core.base.BaseProductLine;
import com.sgs.gpo.facade.model.digital.dto.GenerateReportTemplateDTO;
import com.sgs.gpo.facade.model.digital.dto.UserSignatureDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class GenerateReportRequest extends BaseProductLine implements Serializable {

    private String notesOrderId;
    private String objectNo;
    private String orderNo;
    private String reportNo;
    private String reportId;

    /**
     * 外部系统报告号
     */
    private String externalReportNo;
    private String locationCode;
    private String productLineCode;
    private String language;
    private Date reportDueDate;
    private Integer languageId;
    private String groupCode;
    //签名用户
    List<UserSignatureDTO> userSignatures;
    /**
     * digital report 区分
     * reportType=0  nomal report
     * reportType=1  testResult report*
     */
    private Integer reportActionType = 1;

    /**
     * 出具报告语言类型
     * SINGLE-单语言报告
     * DUAL-双语言报告
     */
    private String reportLanguageType;
    /**
     * 记录Order中的ReportLanguage，查询SubReport时用到，双语报告查询languageId=3的
     */
    private String orderReportLanguage;

//    private GenerateReportDetailDTO generateReportDetailDTO;
    private GenerateReportTemplateDTO generateReportTemplateDtoCN;
    private GenerateReportTemplateDTO generateReportTemplateDtoEn;
}
