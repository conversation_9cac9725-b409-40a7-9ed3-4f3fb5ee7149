package com.sgs.gpo.facade.model.enums;

public enum QuotationFlag {
	FREE_QUOTATON_APPROVED(1<<0, "FreeQuotationApproved"),
	CHARGE_ORDER(1<<1, "CHARGE_ORDER");

	private Integer code;
	private String name;

	private QuotationFlag(Integer code, String name) {
		this.code = code;
		this.name = name;
	}

	public Integer getCode() {
		return this.code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public static QuotationFlag enumOf(Integer code) {
		for (QuotationFlag type : QuotationFlag.values()) {
			if (type.getCode().intValue() == code.intValue()) {
				return type;
			}
		}
		return null;
	}

	/**
	 *  添加一项flag
	 */
	public static int addFlag(int quotationFlagValue, QuotationFlag quotationFlag) {
		quotationFlagValue |= quotationFlag.getCode();
		return quotationFlagValue;
	}

	/**
	 *  删除一项flag
	 */
	public static int deleteFlag(int quotationFlagValue, QuotationFlag quotationFlag) {
		quotationFlagValue &= ~quotationFlag.getCode();
		return quotationFlagValue;
	}

	public static boolean hasChargeOrderFlags(Integer quotationFlagValue){
		if(quotationFlagValue==null){
			return false;
		}

		return (quotationFlagValue.intValue() & CHARGE_ORDER.getCode().intValue()) == CHARGE_ORDER.getCode().intValue();
	}

	public static boolean hasFlags(int quotationFlagValue, QuotationFlag quotationFlag){
		return (quotationFlagValue & quotationFlag.getCode()) == quotationFlag.getCode();
	}

	public static boolean hasFreeQuotationApprovedFlags(Integer quotationFlagValue){
		if(quotationFlagValue==null){
			return false;
		}
		return (quotationFlagValue.intValue() & FREE_QUOTATON_APPROVED.getCode()) == FREE_QUOTATON_APPROVED.getCode();
	}

	/**
	 *  get 最新的flag
	 */
	public static int getFlagValue(Integer quotationFlagValue, QuotationFlag quotationFlag, boolean markFlag) {
		if(quotationFlagValue==null){
			quotationFlagValue=0;
		}
		if(markFlag){//开启flag
			return addFlag(quotationFlagValue,quotationFlag);
		}else{//关闭flag
			return deleteFlag(quotationFlagValue,quotationFlag);
		}
	}

	public static void main(String[] args) {
		int serviceItemFlagValue=0;

		System.out.println(QuotationFlag.getFlagValue(1, QuotationFlag.FREE_QUOTATON_APPROVED,true));
	}
}
