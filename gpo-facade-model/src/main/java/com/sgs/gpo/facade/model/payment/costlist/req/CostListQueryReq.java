package com.sgs.gpo.facade.model.payment.costlist.req;

import com.sgs.framework.core.base.BaseRequest;
import com.sgs.framework.model.annotation.SearchValidate;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @title: CostListQueryReq
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/6/1515:06
 */
@Data
public class CostListQueryReq extends BaseRequest {
    @SearchValidate(buSettingCode = "orderNo")
    private String orderNo;
    private String orderId;
    private String externalOrderNo;
    @SearchValidate(buSettingCode = "orderStatusList")
    private String orderStatus;
    @SearchValidate(buSettingCode = "orderStatusList")
    private List<String> orderStatusList;
    @SearchValidate(buSettingCode = "csName")
    private String csName;
    private String subToCsName;
    @SearchValidate(buSettingCode = "referenceNo")
    private String referenceNo;
    private String oldOrderNo;
    @SearchValidate(buSettingCode = "operationModeList")
    private List<String> operationModeList;
    @SearchValidate(buSettingCode = "createDate")
    private String orderCreateDateFrom;
    @SearchValidate(buSettingCode = "createDate")
    private String orderCreateDateTo;
    private String orderExpectDueDateFrom;
    private String orderExpectDueDateTo;
    @SearchValidate(buSettingCode = "orderNoBatch")
    private String orderNoBatch;
    private String labCode;
    private String customerRefNo;
    private Integer cancelStatus;
    private List<String> batchOrderList;
    @SearchValidate(buSettingCode = "caseTypeList")
    private String caseType;
    private Integer operationMode;
    private String referenceOrderNo;
    private List<String> productCategoryList;
    private String productCategory;
    private String subcontractName;
    private String responsibleTeam;
    @SearchValidate(buSettingCode = "subContractFeeFlag")
    private String subContractFeeFlag;
    private String serviceLevel;
    private List<String> caseTypeList;
    @SearchValidate(buSettingCode = "QuotationFlag")
    private String quotationFlag;
    @SearchValidate(buSettingCode = "costCodeFlag")
    private String costCodeFlag;
    @SearchValidate(buSettingCode = "invoiceConfirmFlag")
    private String invoiceConfirmFlag;
    @SearchValidate(buSettingCode = "idbLab")
    private List<String> idbLab;
}
