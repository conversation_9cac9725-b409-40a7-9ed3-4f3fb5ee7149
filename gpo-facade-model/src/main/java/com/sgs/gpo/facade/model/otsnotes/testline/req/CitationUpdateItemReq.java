package com.sgs.gpo.facade.model.otsnotes.testline.req;

import com.sgs.framework.core.base.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: CitationUpdateItemReq
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/28 15:34
 */
@ApiModel(value = "CitationUpdateItemReq",description = "更新TestLine Citation入参")
@Data
public class CitationUpdateItemReq extends BaseRequest {
    @ApiModelProperty(notes = "citationBaseId")
    private Long citationBaseId;
    @ApiModelProperty(notes = "citation Version Id")
    private Integer citationVersionId;
    @ApiModelProperty(notes = "citation Id")
    private Integer citationId;
    @ApiModelProperty(notes = "citation type :2Regulation  3Standard")
    private Integer citationType;
    @ApiModelProperty(notes = "citation 的名称")
    private String citationName;
    @ApiModelProperty(notes = "是否客户指定")
    private boolean clientSpecifiedFlag;
    @ApiModelProperty(notes = "citation Section id")
    private Integer citationSectionId;
    @ApiModelProperty(notes = "客户指定英文内容,仅clientSpecifiedFlag为true时生效")
    private String editCitationName;
    @ApiModelProperty(notes = "客户指定中文内容,仅clientSpecifiedFlag为true时生效")
    private String editCitationNameCN;

}
