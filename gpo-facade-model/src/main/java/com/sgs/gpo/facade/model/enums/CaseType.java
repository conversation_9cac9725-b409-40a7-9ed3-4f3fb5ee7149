package com.sgs.gpo.facade.model.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

public enum CaseType {
    Local("Local", "Local"),
    IDN("IDN", "IDN"),
    IDB("IDB", "IDB"),
    OverseaPayment("Oversea Payment", "Oversea Payment"),
    IDNTJ("IDN TJ", "IDN TJ"),
    HKPayment("HK Payment", "HK Payment"),
    ;
    private String status;
    private String value;

    CaseType(String status, String value) {
        this.status = status;
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public String getStatus() {
        return status;
    }

    public static final Map<String, CaseType> maps = new HashMap<String, CaseType>() {
        private static final long serialVersionUID = -8986866330615001847L;

        {
            for (CaseType enu : CaseType.values()) {
                String status = enu.getStatus().toUpperCase();
                put(status, enu);
            }
        }
    };

    public static boolean check(String status, CaseType caseType) {
        if (StringUtils.isBlank(status)) {
            return false;
        }
        status = status.toUpperCase();
        return maps.get(status) == caseType;
    }

    public static boolean check(String status, CaseType... caseTypes) {
        if (StringUtils.isBlank(status)) {
            return false;
        }
        for (CaseType caseType : caseTypes) {
            if (StringUtils.equalsIgnoreCase(status, caseType.getStatus())) {
                return true;
            }
        }
        return false;
    }

    public static String getValue(String status) {
        if (StringUtils.isBlank(status)) {
            return null;
        }
        status = status.toUpperCase();
        CaseType orderStatus = maps.get(status);
        return orderStatus == null ? null : orderStatus.getValue();
    }
    public static CaseType getCaseType(String status) {
        if (StringUtils.isBlank(status)) {
            return null;
        }
        status = status.toUpperCase();
        CaseType caseType = maps.get(status);
        return caseType == null ? null : caseType;
    }
}
