package com.sgs.gpo.facade.model.report.bo;

import com.sgs.framework.core.base.BaseIdBO;
import com.sgs.framework.model.test.testsample.TestSampleBO;
import lombok.Data;

import java.util.List;

@Data
public class ReportMatrixRelBO extends BaseIdBO<String> {
    private String reportMatrixRelId;
    private String testMatrixId;
    private String testLineInstanceId;
    private String testSampleId;
    private String sampleNo;
    private String testSampleInstanceId;
    private String reportId;
    private String reportNo;
    private String actualReportNo;
    private String orderNo;
    private Integer testLineStatus;
    private Integer reportStatus;
    private Integer matrixStatus;

}
