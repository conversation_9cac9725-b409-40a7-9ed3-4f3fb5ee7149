package com.sgs.gpo.facade.model.report.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @title: ReportTestLineQueryReq
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2024/3/15 15:47
 */
@Data
public class ReportMatrixQueryReq extends BaseRequest {
    private Set<String> reportIdList;
    private Set<String> reportNoList;
    private Set<String> sampleIdList;
    private Set<String> testLineInstanceIdList;
    private Set<String> matrixIdList;
}
