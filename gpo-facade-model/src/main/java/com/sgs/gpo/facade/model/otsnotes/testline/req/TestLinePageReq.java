package com.sgs.gpo.facade.model.otsnotes.testline.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @title: TestLinePageReq
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/7/5 11:00
 */
@Data
public class TestLinePageReq extends BaseRequest {
    private String testItemNo;
    private Set<String> testItemNoList;
    private String orderNo;
    private Set<String> orderNoList;
    private Set<String> orderStatusList;
    private String jobNo;
    private String subcontractNo;
    private Set<Integer> testLineIdList;
    private Set<Long> citationVersionIdList;
    private String labSectionId;
    private String subcontractLabCode;
    private String labTeamCode;
    private Set<String> testLineStatusList;
    private String testDueDateFrom;
    private String testDueDateTo;
    private String testStartDateFrom;
    private String testStartDateTo;
    private String testEndDateFrom;
    private String testEndDateTo;
    private String testCreatedDateFrom;
    private String testCreatedDateTo;
    private String engineer;
    private String filterOption;
}
