package com.sgs.gpo.facade.model.payment.costlist.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class PaidUpImportDTO {

    private String orderId;
    private String orderNo;

    /**
     * 汇款号
     */
    private String remittanceNo;

    /**
     * 收到汇款时间
     */
    private Date remittanceReceivedDate;

    /**
     * 汇款金额
     */
    private BigDecimal remittanceAmount;

    /**
     * 货币编码
     */
    private String currencyCode;

    /**
     * 付款时间
     */
    private Date paidDate;

    /**
     * 付款方式
     */
    private String paymentMethod;

    /**
     * 付款人
     */
    private String paidBy;

    /**
     * 银行
     */
    private String bank;

    /**
     * 备注
     */
    private String remark;

    private int indexNo;

}
