package com.sgs.gpo.facade.model.preorder.order.req;

import com.sgs.framework.core.base.BaseQueryReq;
import com.sgs.framework.model.order.v2.OrderIdBO;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/7/5 15:35
 */
@Data
public class OrderQueryReq extends BaseQueryReq<OrderIdBO> {

    private Set<String> orderIdList;
    private Set<String> orderNoList;
    private Set<String> enquiryIdList;
}
