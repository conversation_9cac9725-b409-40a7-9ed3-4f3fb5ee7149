package com.sgs.gpo.facade.model.otsnotes.job.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @title: JobLabOutReq
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/8/22 8:38
 */
@Data
public class JobLabOutReq extends BaseRequest {
    /**
     * 可选值
     * Constants.OBJECT.TEST_LINE
     * Constants.OBJECT.JOB
     */
    private String triggerFrom;
    private Set<String> jobNoList;
    private String engineer;
    private String jobOwner;
    private String secondJobOwner;
    private String thirdJobOwner;
    private String operatedBy;
    private String jobNos;
}
