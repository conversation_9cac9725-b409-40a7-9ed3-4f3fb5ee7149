package com.sgs.gpo.facade.model.setting.object.req;

import com.sgs.framework.core.base.BaseRequest;
import com.sgs.framework.model.common.object.ObjectIdBO;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/1/5 14:29
 */
@Data
public class ObjectBuSettingReq extends BaseRequest {
    /**对象信息*/
    private ObjectIdBO object;
    /**实验室信息*/
    private String labCode;
    /**用于校验表达式*/
    private Map<String, Object> data;
}
