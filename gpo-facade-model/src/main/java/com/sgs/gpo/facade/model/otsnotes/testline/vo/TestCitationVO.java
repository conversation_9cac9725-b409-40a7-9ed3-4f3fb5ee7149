package com.sgs.gpo.facade.model.otsnotes.testline.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @title: TestStandardVO
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/26 10:13
 */
@Data
public class TestCitationVO implements Serializable {
    private String testLineInstanceId;
    private Integer testLineId;
    private Integer testLineStatus;
    private String evaluationAlias;
    private String standardName;
    private String clientStandard;
    private String editCitationName;
    private String editCitationNameCN;

//    private List<TestStandardExtRsp> standardList;
}
