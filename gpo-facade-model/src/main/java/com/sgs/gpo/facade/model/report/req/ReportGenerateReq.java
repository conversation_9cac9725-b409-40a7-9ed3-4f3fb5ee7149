package com.sgs.gpo.facade.model.report.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
public class ReportGenerateReq extends BaseRequest {
    /**
     * 生成报告的前台入参
     */
    private String reportId;
    private Set<String> reportIds;
    // 1 report 2 testResult 4 prelim
    private Integer reportActionType = 1;

    /**
     * 执行IReportDomainService.generateReport需要的入参
     */
    private String reportNo;
    private String actualReportNo;
    private String orderNo;
    private String reportLanguage;
    private String editorBy;
    private String editor;
    private String reviewerBy;
    private String reviewer;
    private Integer workFlow;

    private boolean hasFileEn;
    private boolean hasFileCn;

    /**
     * 批量生成TestResult参数
     */

    List<TestResultGenerateReq> generateTestResultReqs;

}
