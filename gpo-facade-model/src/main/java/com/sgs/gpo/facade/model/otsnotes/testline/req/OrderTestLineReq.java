package com.sgs.gpo.facade.model.otsnotes.testline.req;

import com.sgs.framework.core.base.BaseQueryReq;
import com.sgs.framework.model.test.testline.v2.TestLineIdBO;
import com.sgs.gpo.facade.model.lab.req.LabTeamReq;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/6/29 15:09
 */
@Data
public class OrderTestLineReq extends BaseQueryReq<TestLineIdBO> {

    /** 订单ID */
    private String orderId;
    /** 订单号 */
    private Set<String> orderNoList;
    /** 订单号 */
    private Set<String> orderIdList;
    /** 测试项实例ID集合 */
    private Set<String> testLineInstanceIdList;
    /** 测试项号码 */
    private Set<String> testItemNoList;
    /** 测试ID集合 */
    private Set<String> testMatrixIdList;


    private String testItemNo;
    private String orderNo;
    private Set<String> orderStatusList;
    private String jobNo;
    private Set<String> jobNoList;
    private String subcontractNo;
    private Set<String> subcontractNoList;
    private Set<Integer> testLineIdList;
    private Set<Long> citationVersionIdList;
    private String labSectionId;
    private String subcontractLabCode;
    private String labTeamCode;
    private Set<String> testLineStatusList;
    private String testDueDateFrom;
    private String testDueDateTo;
    private String testStartDateFrom;
    private String testStartDateTo;
    private String testEndDateFrom;
    private String testEndDateTo;
    private String createdDateFrom;
    private String createdDateTo;
    private String engineer;
    private String filterOption;
    private String labCode;
    private Integer labId;
    private Boolean isToDoList;
    private String orderCreatedDateFrom;
    private String orderCreatedDateTo;
    private List<String> labTeamCodeList;
    private List<String> subcontractLabCodeList;
    private List<String> labSectionIdList;
    private List<Long> labSectionBaseIdList;
    private List<String> engineerList;
    private Boolean ignoreChargeOrder = false;
    private Boolean delay;
    private Boolean dashBoard = false;
    private Boolean isPending;
    private boolean crossDbQuery = true;
    private boolean baseQuery = false;
    // 查询TestLine时是否打平
    private boolean mergePP;
    private Integer buId;
    private String caller;
}
