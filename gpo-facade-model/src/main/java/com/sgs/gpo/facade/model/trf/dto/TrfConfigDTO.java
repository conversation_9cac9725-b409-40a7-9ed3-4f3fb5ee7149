package com.sgs.gpo.facade.model.trf.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TrfConfigDTO implements Serializable {
    private Integer id;

    private Integer productLineId;

    private String customerGroupCode;

    private Integer refSystemId;

    private String refSystemName;

    private Integer refObjectType;

    private Integer sceneType;

    private Integer bindRule;

    private Integer bindOrderRule;

    private Integer sendType;

    private String desc;

    private String dffFormGroupId;

    private String gridFormGroupId;

    private String refSystemLabelName;

    private Integer status;

    private Integer refCheck;

    private Date createDate;

    private String customerRules;

    private String integrationChannel;
}
