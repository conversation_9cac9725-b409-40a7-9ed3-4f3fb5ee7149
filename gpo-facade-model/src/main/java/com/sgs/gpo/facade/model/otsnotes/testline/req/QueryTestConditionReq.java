package com.sgs.gpo.facade.model.otsnotes.testline.req;

import com.sgs.framework.core.base.BaseQueryReq;
import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @title: QueryTestConditionReq
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/1 20:24
 */
@Data
public class QueryTestConditionReq extends BaseQueryReq {
    private Set<String> testLineInstanceIdList;
    private Set<String> TestConditionInstanceIdList;
    private Set<String> testMatrixIdList;
    private Set<String> orderIdList;
}
