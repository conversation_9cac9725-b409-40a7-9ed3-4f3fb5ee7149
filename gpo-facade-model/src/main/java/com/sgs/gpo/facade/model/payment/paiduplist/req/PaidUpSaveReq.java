package com.sgs.gpo.facade.model.payment.paiduplist.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class PaidUpSaveReq extends BaseRequest {

    private Short buId;
    private String labCode;
    private String buCode;
    private Short locationId;
    private String locationCode;
    private String paidBy;
    private String remittanceNo;
    private BigDecimal remittanceAmount;
    private String currencyCode;
    private Date remittanceReceivedDate;
    private Date paidDate;
    private String bank;
    private String paymentMethod;
    private String remark;
    private List<String> orderIds;
}
