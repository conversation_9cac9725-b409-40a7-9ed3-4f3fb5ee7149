package com.sgs.gpo.facade.model.payment.paymentlist.req;

import com.sgs.framework.core.base.BaseRequest;
import com.sgs.framework.core.model.date.DateRange;
import com.sgs.framework.model.annotation.SearchValidate;
import com.sgs.gpo.facade.model.customer.req.CustomerReq;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
public class PaymentQueryReq extends BaseRequest {

    private static final long serialVersionUID = 4778911105063229904L;
    private String id;

    //订单编号
    @SearchValidate(buSettingCode = "orderNo")
    private String orderNo;

    private String orderNos;
    @SearchValidate(buSettingCode = "orderNos")
    private Set<String> orderNoList;

    //申请人
    @SearchValidate(buSettingCode = "applicant")
    private CustomerReq applicant;

    private String applicantAccountID;
    private List<Long> applicantNumberList;
    private List<String> applicantCnNameList;
    private List<String> applicantEnNameList;


    @SearchValidate(buSettingCode = "payStatus")
    private Integer payStatus;
    @SearchValidate(buSettingCode = "payStatusList")
    private List<Integer> payStatusList;

    //发票号
    @SearchValidate(buSettingCode = "invoiceNo")
    private String invoiceNo;

    private String invoiceNos;
    @SearchValidate(buSettingCode = "invoiceNos")
    private Set<String> invoiceNoList;

    /**
     * 开票时间
     */
    @SearchValidate(buSettingCode = "invoiceDate")
    private String invoiceDateStart;
    @SearchValidate(buSettingCode = "invoiceDate")
    private String invoiceDateEnd;
    /**
     * 订单创建时间
     */
    @SearchValidate(buSettingCode = "orderCreatedDate")
    private String orderCreatedDateStart;
    @SearchValidate(buSettingCode = "orderCreatedDate")
    private String orderCreatedDateEnd;

    //bossOrder单号
    @SearchValidate(buSettingCode = "bossOrderNo")
    private String bossOrderNo;

    @SearchValidate(buSettingCode = "payer")
    private CustomerReq payer;

    private String payerAccountID;
    private List<Long> payerNumberList;
    private List<String> payerCnNameList;
    private List<String> payerEnNameList;

    //是否有boss发票 Y-是 N-否
    @SearchValidate(buSettingCode = "hasBossInvoice")
    private String hasBossInvoice;

    //水单创建时间
    @SearchValidate(buSettingCode = "paidUpCreatedDate")
    private String paidUpCreatedDateStart;
    @SearchValidate(buSettingCode = "paidUpCreatedDate")
    private String paidUpCreatedDateEnd;

    //是否有水单 Y-是 N-否
    @SearchValidate(buSettingCode = "hasPaid")
    private String hasPaid;

    //实验室编码
    private String labCode;
}
