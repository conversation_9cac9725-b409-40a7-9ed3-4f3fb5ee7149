package com.sgs.gpo.facade.model.otsnotes.testline.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @title: TestLineLabOutReq
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/8/21 11:08
 */
@Data
public class TestLineLabOutReq extends BaseRequest {
    private Set<String> testLineInstanceIdList;
    private Set<String> testItemNoList;
    private Boolean checkEngineer = true;
    private Boolean checkJobStatus = true;
    private String engineer;
    private Integer systemId;
    private String operatedBy;
    private String trigger = "testLine";
}
