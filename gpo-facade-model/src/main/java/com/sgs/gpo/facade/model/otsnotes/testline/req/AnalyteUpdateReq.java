package com.sgs.gpo.facade.model.otsnotes.testline.req;

import com.sgs.framework.core.base.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @title: AnalyteUpdateReq
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/28 15:35
 */
@Data
@ApiModel(value = "AnalyteUpdateReq" , description = "编辑TestLine Analyte请求数据")
public class AnalyteUpdateReq extends BaseRequest {
    @ApiModelProperty(notes = "testLineInstanceId")
    private String testLineInstanceId;
    private List<AnalyteUpdateItemReq> analyteUpdateItemReqList;
}
