package com.sgs.gpo.facade.model.aitool.req;

import com.sgs.framework.core.base.BaseRequest;
import com.sgs.framework.model.common.dff.DFFParallelAttrBO;
import lombok.Data;

import java.util.List;

@Data
public class DFFUpdateReq extends BaseRequest {
    // 语言
    private Integer languageId;
    // Product DFF 信息
    private DFFParallelAttrBO product;
    // Product Sample DFF 信息
    private List<DFFParallelAttrBO> productSamples;
}
