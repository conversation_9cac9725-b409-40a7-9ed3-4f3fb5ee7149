package com.sgs.gpo.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum ConclusionEnums {
    PASS(40, "Pass"),
    FAIL(10, "Fail"),
    INCONCLUSIVE(30, "Inconclusive"),
    DATA_ONLY(20, "Data Only"),
    NA(60, "NA"),
    EXEMPT(50, "Exempt");

    private Integer status;
    private String message;

    ConclusionEnums(Integer status, String message) {
        this.status = status;
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public Integer getStatus() {
        return status;
    }

    public static final Map<Integer, ConclusionEnums> maps = new HashMap<Integer, ConclusionEnums>() {
        private static final long serialVersionUID = -8986866330615001847L;

        {
            for (ConclusionEnums enu : ConclusionEnums.values()) {
                put(enu.status, enu);
            }
        }
    };

    public static String getMessage(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status).getMessage();
    }

    public static boolean check(Integer type, ConclusionEnums conclusionEnums) {
        if (type == null || conclusionEnums == null){
            return false;
        }
        return (type & conclusionEnums.getStatus()) > 0;
    }
}
