package com.sgs.gpo.facade.model.setting.object.submodel.process.req;

import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/12/11 16:27
 */
@Data
public class ObjectStatusVerifyReq<BO> {
    /**业务对象集合*/
    private List<BO> boList;
    /**实验室编码*/
    private String labCode;
    /**对象*/
    private String object;
    /**流程*/
    private String scheme;
    /**状态类型*/
    private Set<String> statusTypeList;
    /**可编辑标识*/
    private String editableFlag;
    /**有效标识*/
    private String validFlag;
}
