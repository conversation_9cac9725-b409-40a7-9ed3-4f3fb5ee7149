package com.sgs.gpo.facade.model.report.req;

import com.sgs.framework.core.base.StdBaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.Set;

@EqualsAndHashCode(callSuper = true)
@Data
public class  ReportAccreditationUpdateReq extends StdBaseRequest {
    private Set<String> reportIdList;
    private String certificateName;
    private String certificateFileCloudKey;
    private String certificateId;
    private Date modifiedDate;
    private String modifiedBy;
}
