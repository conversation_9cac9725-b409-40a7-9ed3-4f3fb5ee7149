package com.sgs.gpo.facade.model.otsnotes.testline.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @title: JobTestLineReq
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/8/21 13:26
 */
@Data
public class QueryJobTestLineReq extends BaseRequest {
    private Set<String> jobIdList;
    private Set<String> jobNoList;
    private Set<String> testLineInstanceIdList;
}
