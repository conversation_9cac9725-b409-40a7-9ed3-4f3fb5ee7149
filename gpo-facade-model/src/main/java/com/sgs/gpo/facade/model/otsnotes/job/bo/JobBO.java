package com.sgs.gpo.facade.model.otsnotes.job.bo;

import com.sgs.framework.core.base.BaseIdBO;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @title: JobBO
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/7/18 11:17
 */
@Data
public class JobBO extends BaseIdBO<String> {
    /**
     * ID VARCHAR(36) 必填<br>
     * ID,Primary key
     */
    private String ID;

    /**
     * GeneralOrderInstanceID VARCHAR(36)<br>
     * FK tb_GeneralOrderInstance
     */
    private String generalOrderInstanceID;

    /**
     * LabSectionId INTEGER(10)<br>
     *
     */
    private Integer labSectionId;

    /**
     * OrderNo VARCHAR(50)<br>
     *
     */
    private String orderNo;

    /**
     * JobNo VARCHAR(50)<br>
     *
     */
    private String jobNo;

    /**
     * JobStatus INTEGER(10)<br>
     *
     */
    private Integer jobStatus;

    /**
     * LabInDate TIMESTAMP(19)<br>
     *
     */
    private Date labInDate;

    /**
     * LabOutDate TIMESTAMP(19)<br>
     *
     */
    private Date labOutDate;

    /**
     * LabOutBy VARCHAR(50)<br>
     *
     */
    private String labOutBy;

    /**
     * Remark VARCHAR(1000)<br>
     *
     */
    private String remark;

    /**
     * ActiveIndicator BIT 默认值[1] 必填<br>
     * 0: inactive, 1: active
     */
    private Boolean activeIndicator;

    /**
     * CreatedDate TIMESTAMP(19) 必填<br>
     * CreatedDate
     */
    private Date createdDate;

    /**
     * CreatedBy VARCHAR(50)<br>
     * CreatedBy
     */
    private String createdBy;

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * ModifiedDate
     */
    private Date modifiedDate;

    /**
     * ModifiedBy VARCHAR(50)<br>
     * ModifiedBy
     */
    private String modifiedBy;

    /**
     * JobOwner VARCHAR(100)<br>
     *
     */
    private String jobOwner;

    /**
     * ExpectedDueDate TIMESTAMP(19)<br>
     *
     */
    private Date expectedDueDate;

    /**
     * TestEngineer VARCHAR(100)<br>
     *
     */
    private String testEngineer;

    /**
     * LabSectionBaseId BIGINT(19) 默认值[0]<br>
     * tb_trims_labsection_baseinfo.id
     */
    private Long labSectionBaseId;

}
