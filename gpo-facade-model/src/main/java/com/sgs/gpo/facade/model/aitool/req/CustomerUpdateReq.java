package com.sgs.gpo.facade.model.aitool.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

@Data
public class CustomerUpdateReq extends BaseRequest {
    // 客户中文名称
    private String customerNameCn;
    // 客户英文名称
    private String customerNameEn;
    // 客户中文地址
    private String addressCn;
    // 客户英文地址
    private String addressEn;
    // 联系人名称
    private String contactName;
    // 邮箱
    private String email;
    // 传真
    private String fax;
    // 电话
    private String telephone;
    // 移动电话
    private String mobile;

}
