package com.sgs.gpo.facade.model.report.req;

import com.sgs.framework.core.base.BaseRequest;
import com.sgs.preorder.facade.model.req.CustomerReq;
import lombok.Data;
import java.util.Date;
import java.util.List;

@Data
public class ReportAutoDeliverQueryReq extends BaseRequest {
    private String orderNo;
    private String reportNo;
    private CustomerReq applicant;
    private CustomerReq buyer;
    private String createdBy;
    private Date createdDateBegin;
    private Date createdDateEnd;
    private List<Integer> reportStatusList;

    private Integer labId;

}
