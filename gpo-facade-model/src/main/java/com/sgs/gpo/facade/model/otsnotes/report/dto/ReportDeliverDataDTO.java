package com.sgs.gpo.facade.model.otsnotes.report.dto;

import com.sgs.gpo.facade.model.emailmodel.EmailTemplateRsp;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ReportDeliverDataDTO implements Serializable {
    private List<EmailTemplateRsp> emailTemplateList;
    private List<String> historyEmailAddressList;
    private String emailTo;
    private String emailCc;
    private String emailBcc;
    private List<ReportData> reportDataList;

    @Data
    public static class ReportData {
        private String reportNo;
        private String reportId;
        private String subject;
        private List<String> fileNameList;
    }
}
