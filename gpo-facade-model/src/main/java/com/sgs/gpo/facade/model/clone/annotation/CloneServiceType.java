package com.sgs.gpo.facade.model.clone.annotation;



import com.sgs.framework.model.enums.OrderBizType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.TYPE_USE, ElementType.ANNOTATION_TYPE, ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface CloneServiceType {
    OrderBizType bizType();
    /**
     * 执行顺序
     * @return
     */
    int order();
}
