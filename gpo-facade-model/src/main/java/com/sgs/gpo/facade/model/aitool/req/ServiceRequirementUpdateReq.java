package com.sgs.gpo.facade.model.aitool.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.List;

@Data
public class ServiceRequirementUpdateReq extends BaseRequest {

    // Invoice Deliver Way
    private String invoiceDeliverWay;
    // 是否勾选了ResidueSample
    private Integer returnResidueSampleFlag;
    // Return Sample Way remark
    private String returnResidueSampleRemark;
    // 是否勾选了TestedSample
    private Integer returnTestedSampleFlag;
    //SoftCopyDeliverTo InvoiceDeliverTo ReturnSampleTo
    private List<ServiceRequirementContactInfo> contactInfos;

}
