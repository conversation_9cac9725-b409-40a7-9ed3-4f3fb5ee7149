package com.sgs.gpo.biz.preorder.order.impl;

import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseExecutor;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.model.Lab;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.biz.preorder.order.batchupdate.command.OrderBatchUpdateCMD;
import com.sgs.gpo.biz.preorder.order.batchupdate.context.OrderBatchUpdateContext;
import com.sgs.gpo.facade.model.preorder.order.req.OrderBatchUpdateReq;
import com.sgs.gpo.facade.model.preorder.order.rsp.OrderBatchUpdateRsp;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.powermock.api.mockito.PowerMockito.verifyStatic;

/**
 * OrderBizServiceImpl.batchUpdate方法单元测试类
 * 
 * 测试覆盖场景：
 * 1. 正常执行流程
 * 2. 参数边界条件
 * 3. Context异常情况
 * 4. BaseExecutor执行异常
 * 5. Mock验证和参数传递验证
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({
    SecurityContextHolder.class,
    SystemContextHolder.class,
    ProductLineContextHolder.class,
    BaseExecutor.class
})
public class OrderBizServiceImplBatchUpdateTest {

    @InjectMocks
    private OrderBizServiceImpl orderBizService;

    // Mock数据
    private UserInfo mockUserInfo;
    private String mockToken;
    private Lab mockLab;
    private String mockProductLineCode;

    @Before
    public void setUp() {
        // 初始化Mock数据
        mockUserInfo = createMockUserInfo();
        mockToken = "mock-token-123";
        mockLab = createMockLab();
        mockProductLineCode = "MOCK_PRODUCT_LINE";

        // Mock静态类
        PowerMockito.mockStatic(SecurityContextHolder.class);
        PowerMockito.mockStatic(SystemContextHolder.class);
        PowerMockito.mockStatic(ProductLineContextHolder.class);
        PowerMockito.mockStatic(BaseExecutor.class);
    }

    /**
     * 测试用例1：正常成功场景
     * 验证在所有参数和Context都正常的情况下，方法能够正确执行
     */
    @Test
    public void testBatchUpdate_Success() {
        // Given - 准备测试数据和Mock
        OrderBatchUpdateReq request = createValidOrderBatchUpdateReq();
        BaseResponse<List<OrderBatchUpdateRsp>> expectedResponse = createSuccessResponse();
        
        // Mock ContextHolder行为
        PowerMockito.when(SecurityContextHolder.getUserInfoFillSystem()).thenReturn(mockUserInfo);
        PowerMockito.when(SystemContextHolder.getSgsToken()).thenReturn(mockToken);
        PowerMockito.when(SystemContextHolder.getLab()).thenReturn(mockLab);
        PowerMockito.when(ProductLineContextHolder.getProductLineCode()).thenReturn(mockProductLineCode);
        PowerMockito.when(BaseExecutor.start(eq(OrderBatchUpdateCMD.class), any(OrderBatchUpdateContext.class)))
                .thenReturn(expectedResponse);

        // When - 执行被测试方法
        BaseResponse<List<OrderBatchUpdateRsp>> result = orderBizService.batchUpdate(request);

        // Then - 验证结果
        assertNotNull("返回结果不应为null", result);
        assertTrue("返回结果应为成功状态", result.isSuccess());
        assertEquals("返回数据应与期望一致", expectedResponse.getData(), result.getData());

        // 验证Mock调用
        verifyStatic(SecurityContextHolder.class, times(1));
        SecurityContextHolder.getUserInfoFillSystem();
        
        verifyStatic(SystemContextHolder.class, times(1));
        SystemContextHolder.getSgsToken();
        
        verifyStatic(SystemContextHolder.class, times(1));
        SystemContextHolder.getLab();
        
        verifyStatic(ProductLineContextHolder.class, times(1));
        ProductLineContextHolder.getProductLineCode();
        
        verifyStatic(BaseExecutor.class, times(1));
        BaseExecutor.start(eq(OrderBatchUpdateCMD.class), any(OrderBatchUpdateContext.class));
    }

    /**
     * 测试用例2：Context设置验证
     * 验证传递给BaseExecutor的Context对象属性设置正确
     */
    @Test
    public void testBatchUpdate_ContextSetupVerification() {
        // Given
        OrderBatchUpdateReq request = createValidOrderBatchUpdateReq();
        BaseResponse<List<OrderBatchUpdateRsp>> expectedResponse = createSuccessResponse();
        
        PowerMockito.when(SecurityContextHolder.getUserInfoFillSystem()).thenReturn(mockUserInfo);
        PowerMockito.when(SystemContextHolder.getSgsToken()).thenReturn(mockToken);
        PowerMockito.when(SystemContextHolder.getLab()).thenReturn(mockLab);
        PowerMockito.when(ProductLineContextHolder.getProductLineCode()).thenReturn(mockProductLineCode);
        PowerMockito.when(BaseExecutor.start(eq(OrderBatchUpdateCMD.class), any(OrderBatchUpdateContext.class)))
                .thenReturn(expectedResponse);

        // When
        orderBizService.batchUpdate(request);

        // Then - 使用ArgumentCaptor捕获Context参数进行验证
        ArgumentCaptor<OrderBatchUpdateContext> contextCaptor = 
            ArgumentCaptor.forClass(OrderBatchUpdateContext.class);
        
        verifyStatic(BaseExecutor.class);
        BaseExecutor.start(eq(OrderBatchUpdateCMD.class), contextCaptor.capture());

        OrderBatchUpdateContext capturedContext = contextCaptor.getValue();
        assertEquals("Context中的请求参数应正确设置", request, capturedContext.getParam());
        assertEquals("Context中的用户信息应正确设置", mockUserInfo, capturedContext.getUserInfo());
        assertEquals("Context中的Token应正确设置", mockToken, capturedContext.getToken());
        assertEquals("Context中的Lab信息应正确设置", mockLab, capturedContext.getLab());
        assertEquals("Context中的产品线代码应正确设置", mockProductLineCode, capturedContext.getProductLineCode());
    }

    /**
     * 测试用例3：请求参数为null
     * 验证当请求参数为null时，方法能够正常处理（参数验证由CMD负责）
     */
    @Test
    public void testBatchUpdate_NullRequest() {
        // Given
        OrderBatchUpdateReq request = null;
        BaseResponse<List<OrderBatchUpdateRsp>> expectedResponse = createSuccessResponse();
        
        PowerMockito.when(SecurityContextHolder.getUserInfoFillSystem()).thenReturn(mockUserInfo);
        PowerMockito.when(SystemContextHolder.getSgsToken()).thenReturn(mockToken);
        PowerMockito.when(SystemContextHolder.getLab()).thenReturn(mockLab);
        PowerMockito.when(ProductLineContextHolder.getProductLineCode()).thenReturn(mockProductLineCode);
        PowerMockito.when(BaseExecutor.start(eq(OrderBatchUpdateCMD.class), any(OrderBatchUpdateContext.class)))
                .thenReturn(expectedResponse);

        // When
        BaseResponse<List<OrderBatchUpdateRsp>> result = orderBizService.batchUpdate(request);

        // Then
        assertNotNull("即使请求为null，返回结果也不应为null", result);
        
        // 验证Context中的param为null
        ArgumentCaptor<OrderBatchUpdateContext> contextCaptor = 
            ArgumentCaptor.forClass(OrderBatchUpdateContext.class);
        verifyStatic(BaseExecutor.class);
        BaseExecutor.start(eq(OrderBatchUpdateCMD.class), contextCaptor.capture());
        
        OrderBatchUpdateContext capturedContext = contextCaptor.getValue();
        assertNull("Context中的请求参数应为null", capturedContext.getParam());
    }

    /**
     * 测试用例4：Context值为null的情况
     * 验证当各种ContextHolder返回null时，方法能够正常处理
     */
    @Test
    public void testBatchUpdate_NullContextValues() {
        // Given
        OrderBatchUpdateReq request = createValidOrderBatchUpdateReq();
        BaseResponse<List<OrderBatchUpdateRsp>> expectedResponse = createSuccessResponse();
        
        // Mock所有ContextHolder返回null
        PowerMockito.when(SecurityContextHolder.getUserInfoFillSystem()).thenReturn(null);
        PowerMockito.when(SystemContextHolder.getSgsToken()).thenReturn(null);
        PowerMockito.when(SystemContextHolder.getLab()).thenReturn(null);
        PowerMockito.when(ProductLineContextHolder.getProductLineCode()).thenReturn(null);
        PowerMockito.when(BaseExecutor.start(eq(OrderBatchUpdateCMD.class), any(OrderBatchUpdateContext.class)))
                .thenReturn(expectedResponse);

        // When
        BaseResponse<List<OrderBatchUpdateRsp>> result = orderBizService.batchUpdate(request);

        // Then
        assertNotNull("返回结果不应为null", result);
        
        // 验证Context中的属性都为null
        ArgumentCaptor<OrderBatchUpdateContext> contextCaptor = 
            ArgumentCaptor.forClass(OrderBatchUpdateContext.class);
        verifyStatic(BaseExecutor.class);
        BaseExecutor.start(eq(OrderBatchUpdateCMD.class), contextCaptor.capture());
        
        OrderBatchUpdateContext capturedContext = contextCaptor.getValue();
        assertNull("Context中的用户信息应为null", capturedContext.getUserInfo());
        assertNull("Context中的Token应为null", capturedContext.getToken());
        assertNull("Context中的Lab信息应为null", capturedContext.getLab());
        assertNull("Context中的产品线代码应为null", capturedContext.getProductLineCode());
    }

    /**
     * 测试用例5：BaseExecutor执行失败
     * 验证当BaseExecutor抛出异常时，异常能够正确传播
     */
    @Test(expected = RuntimeException.class)
    public void testBatchUpdate_BaseExecutorFailure() {
        // Given
        OrderBatchUpdateReq request = createValidOrderBatchUpdateReq();
        
        PowerMockito.when(SecurityContextHolder.getUserInfoFillSystem()).thenReturn(mockUserInfo);
        PowerMockito.when(SystemContextHolder.getSgsToken()).thenReturn(mockToken);
        PowerMockito.when(SystemContextHolder.getLab()).thenReturn(mockLab);
        PowerMockito.when(ProductLineContextHolder.getProductLineCode()).thenReturn(mockProductLineCode);
        
        // Mock BaseExecutor抛出异常
        PowerMockito.when(BaseExecutor.start(eq(OrderBatchUpdateCMD.class), any(OrderBatchUpdateContext.class)))
                .thenThrow(new RuntimeException("BaseExecutor执行失败"));

        // When & Then - 期望抛出RuntimeException
        orderBizService.batchUpdate(request);
    }

    /**
     * 测试用例6：空集合参数测试
     * 验证空订单列表和空更新区域的处理
     */
    @Test
    public void testBatchUpdate_EmptyCollections() {
        // Given
        OrderBatchUpdateReq request = createEmptyOrderBatchUpdateReq();
        BaseResponse<List<OrderBatchUpdateRsp>> expectedResponse = createSuccessResponse();
        
        PowerMockito.when(SecurityContextHolder.getUserInfoFillSystem()).thenReturn(mockUserInfo);
        PowerMockito.when(SystemContextHolder.getSgsToken()).thenReturn(mockToken);
        PowerMockito.when(SystemContextHolder.getLab()).thenReturn(mockLab);
        PowerMockito.when(ProductLineContextHolder.getProductLineCode()).thenReturn(mockProductLineCode);
        PowerMockito.when(BaseExecutor.start(eq(OrderBatchUpdateCMD.class), any(OrderBatchUpdateContext.class)))
                .thenReturn(expectedResponse);

        // When
        BaseResponse<List<OrderBatchUpdateRsp>> result = orderBizService.batchUpdate(request);

        // Then
        assertNotNull("返回结果不应为null", result);
        assertTrue("返回结果应为成功状态", result.isSuccess());
    }

    /**
     * 测试用例7：不同标志位组合测试
     * 验证sameAsApplicantFlag和sameAsApplicantShow不同值的处理
     */
    @Test
    public void testBatchUpdate_DifferentFlags() {
        // Given
        OrderBatchUpdateReq request = createValidOrderBatchUpdateReq();
        request.setSameAsApplicantFlag(true);
        request.setSameAsApplicantShow(true);
        
        BaseResponse<List<OrderBatchUpdateRsp>> expectedResponse = createSuccessResponse();
        
        PowerMockito.when(SecurityContextHolder.getUserInfoFillSystem()).thenReturn(mockUserInfo);
        PowerMockito.when(SystemContextHolder.getSgsToken()).thenReturn(mockToken);
        PowerMockito.when(SystemContextHolder.getLab()).thenReturn(mockLab);
        PowerMockito.when(ProductLineContextHolder.getProductLineCode()).thenReturn(mockProductLineCode);
        PowerMockito.when(BaseExecutor.start(eq(OrderBatchUpdateCMD.class), any(OrderBatchUpdateContext.class)))
                .thenReturn(expectedResponse);

        // When
        BaseResponse<List<OrderBatchUpdateRsp>> result = orderBizService.batchUpdate(request);

        // Then
        assertNotNull("返回结果不应为null", result);
        
        // 验证标志位正确传递
        ArgumentCaptor<OrderBatchUpdateContext> contextCaptor = 
            ArgumentCaptor.forClass(OrderBatchUpdateContext.class);
        verifyStatic(BaseExecutor.class);
        BaseExecutor.start(eq(OrderBatchUpdateCMD.class), contextCaptor.capture());
        
        OrderBatchUpdateContext capturedContext = contextCaptor.getValue();
        OrderBatchUpdateReq capturedRequest = capturedContext.getParam();
        assertTrue("sameAsApplicantFlag应为true", capturedRequest.isSameAsApplicantFlag());
        assertTrue("sameAsApplicantShow应为true", capturedRequest.isSameAsApplicantShow());
    }

    // ==================== 测试数据构建方法 ====================

    /**
     * 创建有效的OrderBatchUpdateReq对象
     */
    private OrderBatchUpdateReq createValidOrderBatchUpdateReq() {
        OrderBatchUpdateReq request = new OrderBatchUpdateReq();
        request.setOrderNoList(Sets.newHashSet("ORDER001", "ORDER002", "ORDER003"));
        request.setOrgOrderNo("ORG_ORDER001");
        request.setUpdateSection(Arrays.asList(createBatchUpdateSectionReq()));
        request.setSameAsApplicantFlag(false);
        request.setSameAsApplicantShow(false);
        return request;
    }

    /**
     * 创建空集合的OrderBatchUpdateReq对象
     */
    private OrderBatchUpdateReq createEmptyOrderBatchUpdateReq() {
        OrderBatchUpdateReq request = new OrderBatchUpdateReq();
        request.setOrderNoList(Sets.newHashSet());
        request.setOrgOrderNo("ORG_ORDER001");
        request.setUpdateSection(new ArrayList<>());
        request.setSameAsApplicantFlag(false);
        request.setSameAsApplicantShow(false);
        return request;
    }

    /**
     * 创建BatchUpdateSectionReq对象
     */
    private OrderBatchUpdateReq.BatchUpdateSectionReq createBatchUpdateSectionReq() {
        OrderBatchUpdateReq.BatchUpdateSectionReq section = new OrderBatchUpdateReq.BatchUpdateSectionReq();
        section.setAttributeCode("customer");
        section.setAttributeName("客户信息");
        section.setCheckAll(true);
        section.setIsIndeterminate(false);
        section.setSubSectionReqList(Arrays.asList(createBatchUpdateSubSectionReq()));
        return section;
    }

    /**
     * 创建BatchUpdateSubSectionReq对象
     */
    private OrderBatchUpdateReq.BatchUpdateSubSectionReq createBatchUpdateSubSectionReq() {
        OrderBatchUpdateReq.BatchUpdateSubSectionReq subSection = new OrderBatchUpdateReq.BatchUpdateSubSectionReq();
        subSection.setAttributeCode("applicant");
        subSection.setAttributeName("申请方");
        subSection.setDisplayInReport("Y");
        return subSection;
    }

    /**
     * 创建Mock用户信息
     */
    private UserInfo createMockUserInfo() {
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId("TEST_USER_001");
        userInfo.setUserName("测试用户");
        userInfo.setEmail("<EMAIL>");
        return userInfo;
    }

    /**
     * 创建Mock实验室信息
     */
    private Lab createMockLab() {
        Lab lab = new Lab();
        lab.setLabCode("TEST_LAB");
        lab.setBuCode("TEST_BU");
        lab.setLocationCode("TEST_LOCATION");
        return lab;
    }

    /**
     * 创建成功响应
     */
    private BaseResponse<List<OrderBatchUpdateRsp>> createSuccessResponse() {
        List<OrderBatchUpdateRsp> responseList = new ArrayList<>();
        
        OrderBatchUpdateRsp response1 = new OrderBatchUpdateRsp();
        response1.setOrderNo("ORDER001");
        response1.setMessage("更新成功");
        responseList.add(response1);
        
        OrderBatchUpdateRsp response2 = new OrderBatchUpdateRsp();
        response2.setOrderNo("ORDER002");
        response2.setMessage("更新成功");
        responseList.add(response2);
        
        return BaseResponse.newSuccessInstance(responseList);
    }
}
