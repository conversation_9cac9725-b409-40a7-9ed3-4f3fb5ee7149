package com.sgs.gpo.domain.service.preorder.devops.impl;

import com.alibaba.fastjson.JSON;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.tool.utils.DateUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.devops.DevopsMapper;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.report.ReportMapper;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.subreport.SubReportMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subreport.ReportSubReportRelationshipPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subreport.SubReportPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.attachment.OrderAttachmentPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.productinstance.ProductInstancePO;
import com.sgs.gpo.domain.service.otsnotes.subreport.IReportSubReportRelationshipService;
import com.sgs.gpo.domain.service.otsnotes.subreport.ISubReportService;
import com.sgs.gpo.domain.service.preorder.devops.IDevopsService;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IGeneralOrderService;
import com.sgs.gpo.domain.service.preorder.productinstance.IProductInstanceService;
import com.sgs.gpo.facade.model.preorder.productsample.dto.ProductSampleExtFieldDto;
import com.sgs.gpo.facade.model.preorder.productsample.req.ProductSampleQueryReq;
import com.sgs.preorder.facade.model.dto.productextfields.SampleExtFieldDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DevopsServiceImpl implements IDevopsService {

    @Autowired
    private DevopsMapper devopsMapper;
    @Autowired
    private ReportMapper reportMapper;
    @Autowired
    private ISubReportService subReportService;
    @Autowired
    private IReportSubReportRelationshipService reportSubReportRelationshipService;
    @Autowired
    private IProductInstanceService productService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse cancelTestingOrder(List<String> orderNoList) {
       if(Func.isNotEmpty(orderNoList)){
           devopsMapper.cancelTestingOrder(orderNoList);
       }
       return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public List<ReportPO> getOrderNoListByAccNone(String buCode) {
        return devopsMapper.getOrderNoListByAccNone(buCode);
    }

    @Override
    public BaseResponse repairSubReport() {
        Instant instant = Instant.parse("2025-03-16T13:00:00Z"); // 使用ISO-8601格式字符串创建Instant对象
        // 将Instant转换为Date对象
        Date date = Date.from(instant);
        List<SubReportPO> subReportPOList = devopsMapper.getErrorSubReportPO();
        if(Func.isEmpty(subReportPOList)){
            return BaseResponse.newSuccessInstance(true);
        }
        List<ReportSubReportRelationshipPO> reportRelationshipPOS = new ArrayList<>();
        subReportPOList.stream().forEach(subReportPO -> {
            //查询Report 01
            ReportPO reportPO = devopsMapper.get01ReportByOrderId(subReportPO.getGeneralOrderInstanceId());
            if(Func.isEmpty(reportPO)){
                return;
            }
            //修复SubReport数据
            SubReportPO subReportPO1 = new SubReportPO();
            subReportPO1.setReportNo(reportPO.getReportNo());
            subReportPO1.setId(subReportPO.getId());
            subReportPO1.setModifiedDate(date);
            subReportService.updateById(subReportPO1);
            //增加report subReport关系
            ReportSubReportRelationshipPO reportSubReportRelationshipPO = new ReportSubReportRelationshipPO();
            reportSubReportRelationshipPO.setId(IdUtil.uuId());
            reportSubReportRelationshipPO.setActiveIndicator(1);
            reportSubReportRelationshipPO.setSubReportId(subReportPO.getId());
            reportSubReportRelationshipPO.setReportId(reportPO.getId());
            reportSubReportRelationshipPO.setCreatedBy("system");
            reportSubReportRelationshipPO.setCreatedDate(date);
            reportSubReportRelationshipPO.setModifiedBy("system");
            reportSubReportRelationshipPO.setModifiedDate(date);
            reportRelationshipPOS.add(reportSubReportRelationshipPO);
        });
        if(Func.isNotEmpty(reportRelationshipPOS)) {
            reportSubReportRelationshipService.saveBatch(reportRelationshipPOS);
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse updateDFFForMR(String start, String end) {
        List<String> orderNoList = new ArrayList<>();
        if(Func.isEmpty(start) || Func.isEmpty(end)){
            return BaseResponse.newFailInstance("common.param.miss",new Object[]{"start","end"});
        }
        //查询MR 时间段内的订单
        List<GeneralOrderPO> generalOrderPOList = devopsMapper.getGeneralOrderList(start,end);
        if(Func.isEmpty(generalOrderPOList)){
            return BaseResponse.newSuccessInstance(orderNoList);
        }
        Set<String> orderIds = generalOrderPOList.stream().map(GeneralOrderPO::getId).collect(Collectors.toSet());
        ProductSampleQueryReq productSampleQueryReq = new ProductSampleQueryReq();
        productSampleQueryReq.setOrderIdList(orderIds);
        List<ProductInstancePO> productInstancePOList = productService.queryOrderProductSample(productSampleQueryReq).getData();
        if(Func.isEmpty(productInstancePOList)){
            return BaseResponse.newSuccessInstance(orderNoList);
        }
        orderNoList = generalOrderPOList.stream().map(GeneralOrderPO::getOrderNo).collect(Collectors.toList());
        productInstancePOList = productInstancePOList.stream().filter(productInstancePO -> Func.isNotEmpty(productInstancePO.getHeaderID())).collect(Collectors.toList());
        for(ProductInstancePO productInstancePO : productInstancePOList){
            String extFields = productInstancePO.getExtFields();
            //判断是否存在这个值
            ProductSampleExtFieldDto productSampleExtFieldDto = new ProductSampleExtFieldDto();
            if(Func.isNotEmpty(extFields)){
                productSampleExtFieldDto = JSON.parseObject(extFields, ProductSampleExtFieldDto.class);
            }
            productSampleExtFieldDto.setSampleDescription(productInstancePO.getOtherSampleInformation());
            productSampleExtFieldDto.setTestSurfaceDirection(productInstancePO.getFirstFPUNo());
            ProductInstancePO newProductSample = new ProductInstancePO();
            newProductSample.setID(productInstancePO.getID());
            newProductSample.setExtFields(JSON.toJSONString(productSampleExtFieldDto));
            if(Func.isAllEmpty(productSampleExtFieldDto.getExternalSampleNo(),productSampleExtFieldDto.getTestSurfaceDirection(),productSampleExtFieldDto.getSampleDescription())){
                continue;
            }
            productService.updateById(newProductSample);
        }
        return BaseResponse.newSuccessInstance(orderNoList);
    }

}
