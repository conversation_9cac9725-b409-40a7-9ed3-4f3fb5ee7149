package com.sgs.gpo.domain.service.preorder.ordertrfrel;

import com.sgs.framework.core.base.BaseIdBO;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.order.trf.TrfBO;
import com.sgs.framework.open.platform.base.service.IBaseService;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.ordertrfrel.OrderTrfRelationshipPO;
import com.sgs.gpo.facade.model.preorder.order.bo.OrderTrfBO;
import com.sgs.gpo.facade.model.preorder.order.req.OrderTrfReq;

import java.util.List;

/**
 * <AUTHOR>
 * @title: IOrderTrfRelationship
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/7/4 11:08
 */
public interface IOrderTrfRelationshipService extends IBaseService<TrfBO,OrderTrfRelationshipPO, BaseIdBO,OrderTrfReq> {
    BaseResponse<List<OrderTrfRelationshipPO>> queryByOrderId(OrderTrfReq orderTrfReq);
    BaseResponse<List<OrderTrfBO>> queryOrderTrf(OrderTrfReq orderTrfReq);
}
