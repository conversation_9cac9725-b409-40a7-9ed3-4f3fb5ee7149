package com.sgs.gpo.domain.service.otsnotes.report.command;

import com.alibaba.fastjson.JSON;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.log.SystemLogHelper;
import com.sgs.framework.log.model.SystemLog;
import com.sgs.framework.model.common.attachment.AttachmentBO;
import com.sgs.framework.model.enums.ReportTestResultStatus;
import com.sgs.framework.model.enums.TestDataHeaderStatus;
import com.sgs.framework.model.report.report.v2.ReportBO;
import com.sgs.framework.model.report.report.v2.ReportIdBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.enums.SystemLogObject;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO;
import com.sgs.gpo.domain.service.otsnotes.report.IReportDomainService;
import com.sgs.gpo.domain.service.otsnotes.report.context.ReportUpdateContext;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportMatrixRelService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportService;
import com.sgs.gpo.facade.model.otsnotes.testdata.dto.TLDataEntryDTO;
import com.sgs.gpo.facade.model.otsnotes.testdata.req.TLDataHeaderQueryReq;
import com.sgs.gpo.facade.model.report.bo.ReportMatrixRelBO;
import com.sgs.gpo.facade.model.report.req.ReportMatrixQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportTestResultStatusUpdateReq;
import com.sgs.gpo.integration.dataentry.DataEntryClient;
import com.sgs.otsnotes.facade.model.enums.ReportFileStatus;
import com.sgs.otsnotes.facade.model.enums.ReportFileType;
import com.sgs.otsnotes.facade.model.enums.SystemLogType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ReportTestResultStatusUpdateCMD extends BaseCommand<ReportUpdateContext<ReportTestResultStatusUpdateReq>> {
    @Autowired
    private IReportDomainService reportDomainService;
    @Autowired
    private IReportMatrixRelService reportMatrixRelService;
    @Autowired
    private IReportService reportService;
    @Autowired
    private DataEntryClient dataEntryClient;
    @Autowired
    private SystemLogHelper systemLogHelper;

    @Override
    public BaseResponse validateParam(ReportUpdateContext<ReportTestResultStatusUpdateReq> context) {
        Assert.isTrue(Func.isNotEmpty(context.getParam()), "common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
        Assert.isTrue(Func.isAnyNotEmpty(context.getParam().getReportIdList(), context.getParam().getMatrixIdList()), "common.param.miss", new Object[]{"reportId"});
//        Assert.isTrue(Func.isNotEmpty(context.getParam().getEntryMode()), "common.param.miss", new Object[]{"EntryNode"});
        return BaseResponse.newSuccessInstance(true);
    }
    @Override
    public BaseResponse execute(ReportUpdateContext<ReportTestResultStatusUpdateReq> context) {
        List<ReportBO> reportBOList = context.getReportBOList();
        List<ReportPO> updateReportPOList = new ArrayList<>();
        String modifiedBy = context.getUserInfo().getRegionAccount();
        Date now = new Date();
        for (ReportBO reportBO : reportBOList) {
            try {
                ReportPO reportPO = new ReportPO();

                // 设置ID，防止空指针
                ReportIdBO id = reportBO.getId();
                if (id == null) {
                    continue; // 或者抛出异常/记录日志
                }
                reportPO.setId(id.getReportId());

                reportPO.setModifiedBy(modifiedBy);
                reportPO.setModifiedDate(now);

                // 获取 TestResult 状态
                ReportTestResultStatus statusForTestResult = calculateTestResultStatus(reportBO.getAttachmentList());

                // 获取 TestLineReport 状态
                ReportTestResultStatus statusForTestLineReport = calculateTestLineReportStatus(context, reportBO.getId().getReportNo());

                // 综合两个状态取最小优先级
                ReportTestResultStatus finalStatus = getFinalStatus(statusForTestResult, statusForTestLineReport);

                reportPO.setTestResultStatus(finalStatus.getCode());
                updateReportPOList.add(reportPO);
            } catch (Exception e) {
                // 记录错误日志，不影响其他 reportBO 处理
                log.error("Error processing reportBO: {}", reportBO.getId(), e);
            }
        }

        if (Func.isNotEmpty(updateReportPOList)) {
            context.setUpdateReportPOList(updateReportPOList);
            reportService.updateBatchById(updateReportPOList);
        }

        return BaseResponse.newSuccessInstance(true);
    }

    private ReportTestResultStatus calculateTestResultStatus(List<AttachmentBO> attachmentList) {
        if (Func.isEmpty(attachmentList)) {
            return ReportTestResultStatus.New;
        }

        boolean existNew = false;
        boolean existEntered = false;
        boolean existCompleted = false;

        for (AttachmentBO item : attachmentList) {
            int fileType = Func.toInt(item.getFileType(), 0);
            String status = item.getStatus();

            if (ReportFileType.check(fileType, ReportFileType.TestResult) && !Func.equalsSafe(status, Func.toStr(ReportFileStatus.CANCELLED.getCode()))) {
                if (Func.equalsSafe(status, Func.toStr(ReportFileStatus.NEW.getCode()))) {
                    existNew = true;
                } else if (Func.equalsSafe(status, Func.toStr(ReportFileStatus.ENTERED.getCode()))) {
                    existEntered = true;
                } else if (Func.equalsSafe(status, Func.toStr(ReportFileStatus.COMPLETED.getCode()))) {
                    existCompleted = true;
                }
            }
        }

        if (existNew) {
            return ReportTestResultStatus.New;
        } else if (existEntered) {
            return ReportTestResultStatus.Entered;
        } else if (existCompleted) {
            return ReportTestResultStatus.Completed;
        } else {
            return ReportTestResultStatus.New;
        }
    }

    private ReportTestResultStatus calculateTestLineReportStatus(ReportUpdateContext<?> context, String reportNo) {
        String labCode = Func.isNoneEmpty(context.getLab()) && Func.isNotEmpty(context.getLab().getLabCode())
                ? context.getLab().getLabCode()
                : SystemContextHolder.getLabCode();
        String token = context.getToken();

        TLDataHeaderQueryReq tlDataHeaderQueryReq = new TLDataHeaderQueryReq();
        tlDataHeaderQueryReq.setReportNo(reportNo);
        tlDataHeaderQueryReq.setLabCode(labCode);
        tlDataHeaderQueryReq.setToken(token);

        try {
            List<TLDataEntryDTO> tlDataEntryDTOList = dataEntryClient.queryTestData(tlDataHeaderQueryReq).getData();

            if (Func.isEmpty(tlDataEntryDTOList)) {
                return null;
            }

            boolean existNew = false;
            boolean existEnteredOrSubmitted = false;
            boolean existCompleted = false;

            for (TLDataEntryDTO item : tlDataEntryDTOList) {
                if (TestDataHeaderStatus.check(item.getStatus(), TestDataHeaderStatus.Cancelled)) {
                    continue;
                }

                if (TestDataHeaderStatus.check(item.getStatus(), TestDataHeaderStatus.New)) {
                    existNew = true;
                } else if (TestDataHeaderStatus.check(item.getStatus(), TestDataHeaderStatus.Entered) ||
                        TestDataHeaderStatus.check(item.getStatus(), TestDataHeaderStatus.Submitted)) {
                    existEnteredOrSubmitted = true;
                } else if (TestDataHeaderStatus.check(item.getStatus(), TestDataHeaderStatus.Completed)) {
                    existCompleted = true;
                }
            }

            if (existNew) {
                return ReportTestResultStatus.New;
            } else if (existEnteredOrSubmitted) {
                return ReportTestResultStatus.Entered;
            } else if (existCompleted) {
                return ReportTestResultStatus.Completed;
            } else {
                return ReportTestResultStatus.New;
            }
        } catch (Exception e) {
            log.error("Error occurred while querying test data for reportNo: {}", reportNo, e);
            return null;
        }
    }

    private ReportTestResultStatus getFinalStatus(ReportTestResultStatus status1, ReportTestResultStatus status2) {
        if (status1 != null && status2 != null) {
            return ReportTestResultStatus.values()[Math.max(status1.ordinal(), status2.ordinal())];
        } else if (status1 != null) {
            return status1;
        } else if (status2 != null) {
            return status2;
        } else {
            return ReportTestResultStatus.New;
        }
    }

    @Override
    public BaseResponse before(ReportUpdateContext<ReportTestResultStatusUpdateReq> context) {
        //查询Report
        ReportTestResultStatusUpdateReq param = context.getParam();
        Set<String> reportIdList = param.getReportIdList();
        Set<String> matrixIdList = param.getMatrixIdList();
        if (Func.isNotEmpty(matrixIdList)) {
            ReportMatrixQueryReq reportMatrixQueryReq = new ReportMatrixQueryReq();
            reportMatrixQueryReq.setMatrixIdList(matrixIdList);
            if(Func.isNotEmpty(reportIdList)){
                reportMatrixQueryReq.setReportIdList(reportIdList);
            }
            List<ReportMatrixRelBO> reportMatrixRelBOList = reportMatrixRelService.queryReportMatrix(reportMatrixQueryReq).getData();
            if (Func.isNotEmpty(reportMatrixRelBOList)) {
                reportIdList = reportMatrixRelBOList.stream().map(ReportMatrixRelBO::getReportId).collect(Collectors.toSet());
            }
        }
        ReportQueryReq reportQueryReq = new ReportQueryReq();
        reportQueryReq.setReportIdList(reportIdList);
        reportQueryReq.setBaseQuery(false);
        List<ReportBO> reportBOList = reportDomainService.queryBO(reportQueryReq).getData();
        Assert.isTrue(Func.isNotEmpty(reportBOList), "common.records.empty", new Object[]{Constants.OBJECT.REPORT.NAME});
        context.setReportBOList(reportBOList);
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse after(ReportUpdateContext<ReportTestResultStatusUpdateReq> context) {
        ReportTestResultStatusUpdateReq param = context.getParam();
        SystemLog resultLog = new SystemLog();
        resultLog.setObjectType(SystemLogObject.REPORT.getType());
        resultLog.setObjectNo(param.getOrderNo());
        resultLog.setProductLineCode(context.getProductLineCode());
        resultLog.setType(SystemLogType.SYSTEM.getType());
        resultLog.setRemark("calculate Report Test Result Status");
        resultLog.setRequest(JSON.toJSONString(context));
        resultLog.setOperationType("UpdateReportTestResultStatus");
        resultLog.setLocationCode(context.getLab().getLocationCode());
        resultLog.setCreateBy(context.getUserInfo().getRegionAccount());
        systemLogHelper.save(resultLog);
        return super.after(context);
    }
}
