package com.sgs.gpo.domain.service.dashboard.command;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.model.enums.TestLineStatus;
import com.sgs.framework.model.trims.labsection.LabSectionBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.enums.ReportStatus;
import com.sgs.gpo.dbstorages.mybatis.mapper.dashboard.DashboardMapper;
import com.sgs.gpo.domain.service.dashboard.context.DashboardReportTaskDetailContext;
import com.sgs.gpo.domain.service.trims.labsection.ILabSectionService;
import com.sgs.gpo.facade.model.dashboard.req.DashBoardQueryRep;
import com.sgs.gpo.facade.model.dashboard.rsp.ReportTaskDetailRsp;
import com.sgs.gpo.facade.model.trims.labsection.req.LabSectionReq;
import com.sgs.gpo.integration.framework.FrameworkClient;
import com.sgs.gpo.integration.framework.req.DataDictionaryReq;
import com.sgs.gpo.integration.framework.rsp.DataDictionaryRsp;
import com.sgs.gpo.integration.framework.rsp.LabRsp;
import com.sgs.trimslocal.facade.model.labsection.req.GetLabSectionBaseInfoReq;
import com.sgs.trimslocal.facade.model.labsection.rsp.GetLabSectionBaseInfoRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
@Scope(value = "prototype")
public class ReportTaskDetailCMD extends BaseCommand<DashboardReportTaskDetailContext> {

    @Autowired
    private DashboardMapper dashboardMapper;

    @Autowired
    private FrameworkClient frameworkClient;

    @Autowired
    private ILabSectionService labSectionService;

    private String YES = "Yes";
    private String NO = "No";
    @Override
    public BaseResponse validateParam(DashboardReportTaskDetailContext context) {
        // VP1、校验入参不能为空
        if(Func.isEmpty(context)){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        // VP2、校验用户不能为空
        UserInfo userInfo = context.getUserInfo();
        if(Func.isEmpty(userInfo)){
            return BaseResponse.newFailInstance(ResponseCode.TokenExpire);
        }
        String labCode= userInfo.getCurrentLabCode();
        context.setLabCode(labCode);
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(DashboardReportTaskDetailContext context) {
        //查询Report信息
        DashBoardQueryRep req = context.getParam();
        req.setStartDate(getDate(req.getCurrentDate(), LocalTime.MIN));
        req.setEndDate(getDate(req.getCurrentDate(), LocalTime.MAX));
        req.setLabCode(context.getLabCode());
        List<ReportTaskDetailRsp> list = dashboardMapper.selectReportTaskList(req);
        log.info("查询reportList结果：{}",list);
        //查询buId
        //TO DO 查询buId 和 locationId
        Integer buId = 0;
        BaseResponse<LabRsp> labRsp = frameworkClient.queryLabByLabCode(context.getLabCode());
        if(!labRsp.isFail() && Func.isNotEmpty(labRsp.getData())){
            LabRsp currentLabRsp = labRsp.getData();
            buId = currentLabRsp.getProductLineID();
        }
        //查询Delay Type
        DataDictionaryReq dataDictionaryReq = new DataDictionaryReq();
        dataDictionaryReq.setSystemID(SgsSystem.GPO.getSgsSystemId());
        dataDictionaryReq.setSysKeyGroup("DelayType");
        dataDictionaryReq.setBUID(buId);
        BaseResponse<Map<String, List<DataDictionaryRsp>>> dataDictionaryRsp = frameworkClient.searchDictionary(Lists.newArrayList(dataDictionaryReq));
        if(dataDictionaryRsp.getStatus() == 200 && Func.isNotEmpty(dataDictionaryRsp.getData())){
            List<DataDictionaryRsp> delayTypeList = dataDictionaryRsp.getData().get("DelayType");
            if(Func.isNotEmpty(delayTypeList)){
                Map<String,String> delayTypeMap = new HashMap<>();
                for(DataDictionaryRsp item : delayTypeList){
                    if(Func.isNotEmpty(item) && Func.isNotEmpty(item.getSysKey()) && Func.isNotEmpty(item.getSysValue()))
                    delayTypeMap.put(item.getSysKey(),item.getSysValue());
                }
                context.setDelayTypeMap(delayTypeMap);
            }
        }
        //labSectionBaseId 调用接口查询labSectionName
        Set<Long> labSectionBaseIds = list.stream().map(e -> e.getLabSectionBaseId()).collect(Collectors.toSet());
        if(Func.isNotEmpty(labSectionBaseIds)){
            LabSectionReq labSectionReq = new LabSectionReq();
            labSectionReq.setLabSectionBaseIdList(labSectionBaseIds);
            Map<Long,LabSectionBO> labSectionBOMap = new HashMap<>();
            BaseResponse<List<LabSectionBO>> labSectionRsp = labSectionService.queryLabSection(labSectionReq);
            if(!labSectionRsp.isFail() && Func.isNotEmpty(labSectionRsp.getData())){
                List<LabSectionBO> labSectionBOList = labSectionRsp.getData();
                for(LabSectionBO item : labSectionBOList){
                    if(Func.isNotEmpty(item.getLabSectionBaseId())) labSectionBOMap.put(item.getLabSectionBaseId(),item);
                }
            }
            context.setLabSectionBOMap(labSectionBOMap);
        }
        context.setReportList(list);
        return super.before(context);
    }

    @Override
    public BaseResponse execute(DashboardReportTaskDetailContext context) {
        Map<String,String> delayTypeMap = context.getDelayTypeMap();
        Map<Long, LabSectionBO> labSectionBOMap = context.getLabSectionBOMap();
        List<ReportTaskDetailRsp> resultList = new ArrayList<>();
        List<ReportTaskDetailRsp> reportList = context.getReportList();
        Map<String,List<ReportTaskDetailRsp>> reportMap = reportList.stream().collect(Collectors.groupingBy(ReportTaskDetailRsp::getReportNo));
        //遍历Map
        for (Map.Entry<String, List<ReportTaskDetailRsp>> entry : reportMap.entrySet()){
            ReportTaskDetailRsp reportTaskDetailRsp = new ReportTaskDetailRsp();
            List<ReportTaskDetailRsp> list = entry.getValue();
            Func.copy(list.get(0),reportTaskDetailRsp);
            //特殊处理
            //SectionScope Test CPL
            Set<String> scope = new HashSet<>();
            boolean sendDraft  = false;
            boolean pending = false;
            boolean beApproved = false;
            Map<String,Integer> testLineMap = new HashMap<>();
            for(ReportTaskDetailRsp item : list){
                String scopeAndToLab = null;
                if(Func.isNotEmpty(item.getLabSectionBaseId())){
                    LabSectionBO labSectionBO = labSectionBOMap.get(item.getLabSectionBaseId());
                    if(Func.isNotEmpty(labSectionBO)) {
                        scopeAndToLab = labSectionBO.getLabSectionCode();
                    }
                }
                if(Func.isNotEmpty(item.getSubContractLabCode())){
                    if(Func.isNotEmpty(scopeAndToLab)){
                        scopeAndToLab = scopeAndToLab + "||" + item.getSubContractLabCode();
                    } else {
                        scopeAndToLab = item.getSubContractLabCode();
                    }
                }
                if(Func.isNotEmpty(scopeAndToLab)){
                    scope.add(scopeAndToLab);
                }
                if(item.getSendDraft().equals("1")){
                    sendDraft = true;
                }
                if(Func.isNotEmpty(item.getTliId()) && Func.isNotEmpty(item.getTestLineStatus())){
                    testLineMap.put(item.getTliId(),item.getTestLineStatus());
                }

                if(Func.isNotEmpty(item.getIsPending())){
                    pending = true;
                }
                if(Func.isNotEmpty(item.getBeenApproved()) && item.getBeenApproved().equals(item.getReportNo())){
                    beApproved = true;
                }
            }
            //SectionScope
            reportTaskDetailRsp.setSectionScope(scope.stream().collect(Collectors.joining(",")));
            if(testLineMap.size() == 0) {
                reportTaskDetailRsp.setSectionScope("无");
            }
            //SendDraft
            if(sendDraft){
                reportTaskDetailRsp.setSendDraft(YES);
            } else {
                reportTaskDetailRsp.setSendDraft(NO);
            }
            //Pending
            if(pending || ReportStatus.check(reportTaskDetailRsp.getReportStatus(),ReportStatus.Pending)){
                reportTaskDetailRsp.setIsPending(1);
            } else{
                reportTaskDetailRsp.setIsPending(0);
            }
            //Test CPL Rate
            //TODO 计算处理
            int countComplete = 0,countNoCancel = 0;
            for(Map.Entry<String,Integer> stringEntry : testLineMap.entrySet()){
                Integer status = stringEntry.getValue();
                if(TestLineStatus.check(status,TestLineStatus.Completed)){
                    countComplete++;
                }
                if(!TestLineStatus.check(status,TestLineStatus.Cancelled)){
                    countNoCancel++;
                }
            }
            if(countNoCancel > 0){
                BigDecimal cplRate = new BigDecimal(countComplete).divide(new BigDecimal(countNoCancel),2,BigDecimal.ROUND_HALF_UP);
                BigDecimal result = cplRate.multiply(new BigDecimal(100)).setScale(0);
                String cpl = result.intValue() + "%";
                reportTaskDetailRsp.setTestCPLRate(cpl);
            }
            if(testLineMap.size() == 0){
                reportTaskDetailRsp.setTestCPLRate("无");
            }
            //Been Approved
            if(beApproved){
                reportTaskDetailRsp.setBeenApproved(YES);
            } else{
                reportTaskDetailRsp.setBeenApproved(NO);
            }
            //处理Delay type
            if(Func.isNotEmpty(reportTaskDetailRsp.getReportDelayType())){
                reportTaskDetailRsp.setReportDelayType(delayTypeMap.get(reportTaskDetailRsp.getReportDelayType()));
            }
            resultList.add(reportTaskDetailRsp);
        }
        resultList = resultList.stream().sorted(Comparator.comparing(ReportTaskDetailRsp::getExtReportNo).reversed()).collect(Collectors.toList());
        Page<ReportTaskDetailRsp> pageInfo = getPageInfo(context.getPage(),context.getRows(),resultList);
        return BaseResponse.newSuccessInstance(pageInfo);
    }

    private Date getDate(Date date, LocalTime localTime){
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()), ZoneId.systemDefault());
        LocalDateTime startOfDay = localDateTime.with(localTime);
        return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    private Page<ReportTaskDetailRsp> getPageInfo(int current, int pageSize, List<ReportTaskDetailRsp> list){
        int total = list.size();
        if(total > pageSize){
            int toIndex = pageSize * current;
            if(toIndex > total) toIndex = total;
            list = list.subList(pageSize * (current -1), toIndex);
        }
        Page<ReportTaskDetailRsp> page = new Page<>(current,pageSize);
        page.setSearchCount(false);
        page.setRecords(list);
        page.setTotal(total);
        page.setCurrent(current);
        page.setSize(pageSize);
        return page;
    }

}
