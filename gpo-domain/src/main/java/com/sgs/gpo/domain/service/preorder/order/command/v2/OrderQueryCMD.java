package com.sgs.gpo.domain.service.preorder.order.command.v2;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.model.common.attachment.AttachmentBO;
import com.sgs.framework.model.common.attachment.AttachmentLanguageBO;
import com.sgs.framework.model.common.contact.ContactPersonBO;
import com.sgs.framework.model.common.customer.CustomerBO;
import com.sgs.framework.model.common.customer.CustomerContactBO;
import com.sgs.framework.model.common.customer.CustomerLanguageBO;
import com.sgs.framework.model.common.lab.LabBO;
import com.sgs.framework.model.common.pending.PendingBO;
import com.sgs.framework.model.common.process.ProcessBO;
import com.sgs.framework.model.common.productsample.CareLabelBO;
import com.sgs.framework.model.common.productsample.ProductSampleBO;
import com.sgs.framework.model.common.productsample.SampleBO;
import com.sgs.framework.model.common.servicerequirement.DeliverBO;
import com.sgs.framework.model.common.servicerequirement.ReportLanguageBO;
import com.sgs.framework.model.common.servicerequirement.ServiceRequirementBO;
import com.sgs.framework.model.common.servicerequirement.ServiceRequirementReportBO;
import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.ReportLanguage;
import com.sgs.framework.model.order.order.OrderFlagBO;
import com.sgs.framework.model.order.order.OrderOthersBO;
import com.sgs.framework.model.order.order.OrderPaymentBO;
import com.sgs.framework.model.order.trf.TrfBO;
import com.sgs.framework.model.order.v2.*;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.StringPool;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.order.GeneralOrderInstancePO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.attachment.OrderAttachmentPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.customer.CustomerPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.*;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.testrequest.TestRequestContactPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.testrequest.TestRequestPO;
import com.sgs.gpo.domain.service.otsnotes.order.IGeneralOrderInstanceService;
import com.sgs.gpo.domain.service.preorder.attachment.IOrderAttachmentService;
import com.sgs.gpo.domain.service.preorder.customer.subdomain.ICustomerService;
import com.sgs.gpo.domain.service.preorder.externalno.IExternalNoDomainService;
import com.sgs.gpo.domain.service.preorder.order.context.v2.OrderContext;
import com.sgs.gpo.domain.service.preorder.order.subdomain.*;
import com.sgs.gpo.domain.service.preorder.ordertrfrel.IOrderTrfRelationshipService;
import com.sgs.gpo.domain.service.preorder.productsample.IProductSampleDomainService;
import com.sgs.gpo.domain.service.preorder.testrequest.ITestRequestContactService;
import com.sgs.gpo.domain.service.preorder.testrequest.ITestRequestService;
import com.sgs.gpo.domain.service.setting.buparam.IBUParam;
import com.sgs.gpo.facade.model.attachment.OrderAttachmentTagDTO;
import com.sgs.gpo.facade.model.otsnotes.order.OrderInstanceQueryReq;
import com.sgs.gpo.facade.model.payment.paiduplist.req.PaidUpQueryReq;
import com.sgs.gpo.facade.model.preorder.externalno.bo.ExternalNoBO;
import com.sgs.gpo.facade.model.preorder.externalno.req.ExternalNoQueryReq;
import com.sgs.gpo.facade.model.preorder.order.req.*;
import com.sgs.gpo.facade.model.preorder.productsample.req.ProductSampleQueryReq;
import com.sgs.gpo.integration.framework.FrameworkClient;
import com.sgs.gpo.integration.usermanagement.UserManagementClient;
import com.sgs.gpo.integration.usermanagement.req.EmpInfoReq;
import com.sgs.gpo.integration.usermanagement.rsp.EmpInfoExtRsp;
import com.sgs.otsnotes.facade.model.enums.GpoCustomerType;
import com.sgs.otsnotes.facade.model.rsp.LabInfo;
import com.sgs.preorder.facade.model.enums.BusinessType;
import com.sgs.preorder.facade.model.enums.ExecType;
import com.sgs.preorder.facade.model.enums.SamplePhotoFileTypeEnums;
import com.sgs.testdatabiz.facade.model.enums.ContactUsage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/5 15:33
 */
@Service("OrderQueryCMDV2")
@Primary
public class OrderQueryCMD extends BaseCommand<OrderContext<OrderQueryReq>> {

    @Autowired
    private IGeneralOrderService generalOrderService;
    @Autowired
    private ILabInstanceService labInstanceService;
    @Autowired
    private ISLOrderService slOrderService;
    @Autowired
    private IGeneralOrderInstanceService generalOrderInstanceService;
    @Autowired
    private IOrderPersonService orderPersonService;
    @Autowired
    private ICustomerService customerService;
    @Autowired
    private ITestRequestService testRequestService;
    @Autowired
    private IOrderReportReceiverService orderReportReceiverService;
    @Autowired
    private IOrderAttachmentService orderAttachmentService;
    @Autowired
    private IOrderCrossLabService orderCrossLabService;
    @Autowired
    private IExternalNoDomainService externalNoDomainService;
    @Autowired
    private ICareLabelService careLabelService;
    @Autowired
    private IOrderTrfRelationshipService orderTrfRelationshipService;
    @Autowired
    private UserManagementClient userManagementClient;
    @Autowired
    private IPaidUpService paidUpService;
    @Autowired
    private IOrderExtService orderExtService;
    @Autowired
    private IBUParam buParam;
    @Autowired
    private FrameworkClient frameworkClient;
    @Autowired
    private IProductSampleDomainService productSampleDomainService;
    @Autowired
    private ITestRequestContactService testRequestContactService;
    @Autowired
    private IOrderParcelService orderParcelService;


    @Override
    public BaseResponse validateParam(OrderContext<OrderQueryReq> context) {
        if (Func.isEmpty(context) || Func.isEmpty(context.getParam())) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse<List<OrderBO>> buildDomain(OrderContext<OrderQueryReq> context) {
        List<OrderBO> orderList = Lists.newArrayList();
        List<LabInstancePO> labList = context.getLabList();
        List<TrfBO> trfBOList = context.getTrfList();
        List<OrderExtPO> orderExtPOList = context.getOrderExtPOList();
        List<ProductSampleBO> productSampleBOList = context.getProductSampleBOList();
        if (Func.isEmpty(labList)) {
            labList = Lists.newArrayList();
        }
        List<GeneralOrderPO> generalOrderList = context.getGeneralOrderList();
        if (Func.isEmpty(generalOrderList)) {
            return BaseResponse.newSuccessInstance(Lists.newArrayList());
        }
        List<ExternalNoBO> externalNoBOList = context.getExternalNoBOList();
        for (GeneralOrderPO generalOrderPO : generalOrderList) {
            OrderBO order = new OrderBO();
            order.setActiveIndicator(generalOrderPO.getActiveIndicator());
            // ID
            OrderIdBO id = new OrderIdBO();
            id.setOrderId(generalOrderPO.getId());
            id.setOrderNo(generalOrderPO.getOrderNo());
            id.setRootOrderNo(generalOrderPO.getRootOrderNo());
            id.setParentOrderNo(generalOrderPO.getOldOrderNo());
            order.setId(id);
            // Header
            OrderHeaderBO orderHeader = Func.copy(generalOrderPO, OrderHeaderBO.class);
            orderHeader.setOrderId(generalOrderPO.getId());
            orderHeader.setCreateBy(generalOrderPO.getCreatedBy());
            orderHeader.setCreateDate(generalOrderPO.getCreatedDate());
            orderHeader.setOrderExpectDueDate(generalOrderPO.getExpectedOrderDueDate());
            orderHeader.setReportExpectDueDate(generalOrderPO.getReportExpectDueDate());
            orderHeader.setExternalOrderNo(generalOrderPO.getOrderNo());
            orderHeader.setSampleReceiveDate(generalOrderPO.getSampleReceiveDate());
            if (Func.isNotEmpty(generalOrderPO.getServiceLevel())) {
                orderHeader.setServiceType(generalOrderPO.getServiceLevel().toString());
            }
            if (Func.isNotEmpty(externalNoBOList)) {
                ExternalNoBO externalNoBO = externalNoBOList.parallelStream().filter(item -> Func.equalsSafe(generalOrderPO.getId(), item.getObjectId())).findAny().orElse(null);
                if (Func.isNotEmpty(externalNoBO)) {
                    String externalOrderNo = externalNoBO.getExternalNo();
                    orderHeader.setExternalOrderNo(externalOrderNo);
                }
            }
            order.setHeader(orderHeader);

            OrderFlagBO orderFlagBO = Func.copy(generalOrderPO, OrderFlagBO.class);
            if(Func.isEmpty(generalOrderPO.getSelfTestFlag())){
                orderFlagBO.setSelfTestFlag(0);
            }
            orderFlagBO.setEnableDeliveryFlag(generalOrderPO.getEnableDelivery());
            order.setFlags(orderFlagBO);

            OrderPaymentBO orderPaymentBO = Func.copy(generalOrderPO, OrderPaymentBO.class);
            orderPaymentBO.setCurrency(generalOrderPO.getCurrencyID());
            orderPaymentBO.setTotalAmount(generalOrderPO.getTotalPrice());
            orderPaymentBO.setPaymentStatus(generalOrderPO.getPayStatus());
            orderPaymentBO.setMainCurrencyTotalAmount(generalOrderPO.getMainCurrencyTotalPrice());
            orderPaymentBO.setActualFee(generalOrderPO.getActualFee());
            orderPaymentBO.setActualFeeCurrency(generalOrderPO.getActualFeeCurrency());
            order.setPayment(orderPaymentBO);

            LabInstancePO labInstancePO = labList.stream().filter(lab -> orderHeader.getOrderId().equals(lab.getOrderId())).findFirst().orElse(null);
            if (Func.isNotEmpty(labInstancePO)) {
                LabBO labBO = Func.copy(labInstancePO, LabBO.class);
                labBO.setBuCode(labInstancePO.getProductLineCode());
                order.setLab(labBO);
            }
            buildSlOrderInfo(order, context.getSlOrderList());
            buildGpnOrderInfo(order, context.getGeneralOrderInstanceList());
            buildContactPerson(order, context.getSlOrderList(), context.getOrderPersonList());
            buildCustomerInfo(order, context.getCustomerList());
            buildOrderServiceRequirement(order, context.getTestRequestList(), context.getTestRequestContactList(), context.getOrderReportReceiverList());
            buildOrderAttachment(order, context.getOrderAttachmentList(),context.getBuLanguageId());
            buildOrderCrossLab(order, context.getOrderCrossLabList());
            buildCareLabels(order,context.getCareLabelList());
            buildPaidUpInfo(order,context.getPaidUpVOList());
            buildParcelInfo(order,context.getOrderParcelList());
            // Relationship
            OrderRelationshipBO orderRelationshipBO = new OrderRelationshipBO();
            OrderParentBO parent = new OrderParentBO();
            if(Func.isNotEmpty(trfBOList)){
                List<TrfBO> trfList = trfBOList.stream().filter(e -> Func.equalsSafe(e.getRefObjectId(),generalOrderPO.getId())).collect(Collectors.toList());
                if (Func.isNotEmpty(trfList)) {
                    parent.setTrfList(trfList);
                }
            }
            EnquiryRelBO enquiryRelBO = new EnquiryRelBO();
            enquiryRelBO.setEnquiryNo(generalOrderPO.getEnquiryNo());
            parent.setEnquiry(enquiryRelBO);
            orderRelationshipBO.setParent(parent);
            order.setRelationship(orderRelationshipBO);

            // others
            OrderOthersBO others = new OrderOthersBO();
            others.setOrderRemark(generalOrderPO.getRemark());
            others.setDepartmentCode(generalOrderPO.getKaCustomerDeptCode());
            PendingBO pending = new PendingBO();
            pending.setPendingFlag(generalOrderPO.getPendingFlag());
            others.setPending(pending);
            // ext_fields
            if(Func.isNotEmpty(orderExtPOList)){
                OrderExtPO orderExtPO = orderExtPOList.stream().filter(e -> Func.equalsSafe(e.getOrderId(),generalOrderPO.getId())).findAny().orElse(new OrderExtPO());
                others.setExtFields(orderExtPO.getExtFields());
            }
            order.setOthers(others);
            // process
            List<ProcessBO> processList = Lists.newArrayList();
            if (Func.isNotEmpty(order.getHeader().getCreateDate())) {
                ProcessBO createPoint = new ProcessBO();
                createPoint.setNodePoint(Constants.OBJECT.ORDER.ACTION.create.name());
                createPoint.setOperator(order.getHeader().getCreateBy());
                createPoint.setCompletedDateTime(order.getHeader().getCreateDate());
                processList.add(createPoint);
            }
            if(Func.isNotEmpty(order.getHeader().getOrderConfirmDate())){
                ProcessBO createPoint = new ProcessBO();
                createPoint.setNodePoint(Constants.OBJECT.ORDER.ACTION.confirm.name());
                createPoint.setOperator(order.getHeader().getCreateBy());
                createPoint.setCompletedDateTime(order.getHeader().getOrderConfirmDate());
                processList.add(createPoint);
            }
            if (Func.isNotEmpty(order.getHeader().getSoftCopyDeliveryDate())) {
                ProcessBO softCopy = new ProcessBO();
                softCopy.setNodePoint(Constants.OBJECT.ORDER.ACTION.delivery.name());
                softCopy.setCompletedDateTime(order.getHeader().getSoftCopyDeliveryDate());
                processList.add(softCopy);
            }
            if(Func.isNotEmpty(order.getHeader().getSampleReceiveDate())){
                ProcessBO softCopy = new ProcessBO();
                softCopy.setNodePoint(Constants.OBJECT.ORDER.ACTION.sampleReceive.name());
                softCopy.setCompletedDateTime(order.getHeader().getSampleReceiveDate());
                processList.add(softCopy);
            }
            order.setProcessList(processList);
            // product&sample
            if(Func.isNotEmpty(productSampleBOList)){
                ProductSampleBO productSampleBO = productSampleBOList.stream().filter(e -> Func.equalsSafe(e.getOrderId(),generalOrderPO.getId())).findAny().orElse(null);
                if (Func.isNotEmpty(productSampleBO)) {
                    if (Func.isNotEmpty(productSampleBO.getProduct())) {
                        order.setProduct(productSampleBO.getProduct());
                    }
                    if (Func.isNotEmpty(productSampleBO.getSampleList())) {
                        List<SampleBO> sampleList = productSampleBO.getSampleList();
                        // 中英文Qty不一致时以主语言的数量为主
                        sampleList.stream().forEach(sample -> {
                            //基于productItemNo生成样品seq
                            if(Func.isNotEmpty(sample.getProductItemNo())){
                                sample.setSampleSeq(convertSampleSeq(sample.getProductItemNo()));
                            }
                            if (Func.isEmpty(sample.getSampleAttrList())) {
                                return;
                            }
                            sample.getSampleAttrList().stream().forEach(attr -> {
                                if (!Func.equalsSafe(attr.getLabelCode(), "NoOfSample")) {
                                    return;
                                }
                                if (Func.isEmpty(attr.getValue())) {
                                    return;
                                }
                                if (Func.isEmpty(attr.getLanguageList())) {
                                    return;
                                }
                                Integer sampleQty = Integer.valueOf(attr.getValue().toString());
                                attr.getLanguageList().stream().forEach(language -> {
                                    if (Func.isNotEmpty(language.getValue())) {
                                        language.setValue(sampleQty);
                                    }
                                });
                            });
                        });
                        // SampleList按照ProductItemNo排序 解决AA样品号排序规则
                        sampleList.sort(Comparator.comparing(SampleBO::getSampleSeq,Comparator.nullsLast(Double::compareTo)));
                        order.setSampleList(sampleList);
                    }
                }
            }
            orderList.add(order);
        }
        context.setOrderList(orderList);
        return BaseResponse.newSuccessInstance(orderList);
    }

    /**
     * productItemNo 固定规则,截取SupplierCode _后的数据
     * SupplierCode_296
     * @param productItemNo
     * @return
     */
    private Double convertSampleSeq(String productItemNo){
        String[] supplierCodeArray = productItemNo.split(StringPool.UNDERSCORE);
        if(supplierCodeArray.length>1){
            return Double.valueOf(supplierCodeArray[1]);
        }
        return null;
    }

    private void buildOrderAttachment(OrderBO order, List<OrderAttachmentPO> orderAttachmentList, Integer buLanguageId) {
        if (Func.isNotEmpty(orderAttachmentList)) {
            List<AttachmentBO> attachmentList = Lists.newArrayList();
            //过滤有效的Attachment
            orderAttachmentList = orderAttachmentList.stream().filter(item-> Func.equalsSafe(item.getActiveIndicator(),ActiveType.Enable.getStatus())).collect(Collectors.toList());
            orderAttachmentList.stream().filter(attachment -> Func.equalsSafe(attachment.getGeneralOrderID(), order.getHeader().getOrderId()))
                    .forEach(attachment -> {
                        AttachmentBO rdAttachment = new AttachmentBO();
                        rdAttachment.setId(attachment.getId());
                        rdAttachment.setCloudId(attachment.getCloudID());
                        rdAttachment.setFileName(resetFileName(attachment, LanguageType.English.getLanguageId()));
                        rdAttachment.setPhotoType(attachment.getPhotoType());
                        rdAttachment.setBusinessType(attachment.getBusinessType());
                        rdAttachment.setFileType(attachment.getFileType());
                        rdAttachment.setDisplaySeq(attachment.getSequence());
                        rdAttachment.setSampleNo(attachment.getSampleNo());
                        rdAttachment.setObjectId(attachment.getObjectID());
                        rdAttachment.setToCp(attachment.getToCp());
                        rdAttachment.setClientPhoto(attachment.getClientPhoto());
                        rdAttachment.setReportNo(attachment.getReportNo());
                        rdAttachment.setDisplayInReport(Func.equalsSafe(attachment.getShowInReport(),1));
                        String cnDesc = null, enDesc = null;
                        if(Func.isNotEmpty(attachment.getTag())){
                            OrderAttachmentTagDTO orderAttachmentTagDTO = JSON.parseObject(attachment.getTag(),OrderAttachmentTagDTO.class);
                            if(Func.isNotEmpty(orderAttachmentTagDTO) && Func.isNotEmpty(orderAttachmentTagDTO.getLanguageList())){
                                List<OrderAttachmentTagDTO.OrderAttachmentTagLanguageDTO> languageList = orderAttachmentTagDTO.getLanguageList();
                                OrderAttachmentTagDTO.OrderAttachmentTagLanguageDTO cn = languageList.stream().filter(e -> Func.equalsSafe(e.getLanguageId(),LanguageType.Chinese.getLanguageId())).findAny().orElse(null);
                                OrderAttachmentTagDTO.OrderAttachmentTagLanguageDTO en = languageList.stream().filter(e -> Func.equalsSafe(e.getLanguageId(),LanguageType.English.getLanguageId())).findAny().orElse(null);
                                if(Func.isNotEmpty(en)){
                                    enDesc = en.getPhotoDescription();
                                }
                                if(Func.isNotEmpty(cn)){
                                    cnDesc = en.getPhotoDescription();
                                }
                            }
                        }
                        //根据Bu主语言设置Desc
                        String desc = LanguageType.check(buLanguageId,LanguageType.English) && Func.isNotEmpty(enDesc) ? enDesc : cnDesc;
                        rdAttachment.setDescription(desc);
                        rdAttachment.setSource(attachment.getSource());
                        // SamplePhoto多语言设置
                        if(Func.equalsSafe(attachment.getBusinessType(),BusinessType.SamplePhoto.getCode())
                                ||Func.equalsSafe(attachment.getBusinessType(),BusinessType.TestedSamplePhoto.getCode())){
                            AttachmentLanguageBO attachmentCn = new AttachmentLanguageBO();
                            attachmentCn.setCloudId(attachment.getCloudID());
                            attachmentCn.setFileName(resetFileName(attachment,LanguageType.Chinese.getLanguageId()));
                            attachmentCn.setLanguageId(LanguageType.Chinese.getLanguageId());
                            attachmentCn.setDescription(cnDesc);
                            AttachmentLanguageBO attachmentEn = new AttachmentLanguageBO();
                            attachmentEn.setCloudId(attachment.getCloudID());
                            attachmentEn.setFileName(resetFileName(attachment,LanguageType.English.getLanguageId()));
                            attachmentEn.setLanguageId(LanguageType.English.getLanguageId());
                            attachmentEn.setDescription(enDesc);
                            List<AttachmentLanguageBO> languageList = Lists.newArrayList();
                            languageList.add(attachmentCn);
                            languageList.add(attachmentEn);
                            rdAttachment.setLanguageList(languageList);
                         }
                        attachmentList.add(rdAttachment);
                    });
            // TODO 排序怎么排
            order.setAttachmentList(attachmentList);
        }
    }

    private String resetFileName(OrderAttachmentPO attachment, Integer languageId) {
        if ((Func.equalsSafe(attachment.getBusinessType(), BusinessType.SamplePhoto.getCode())|| Func.equalsSafe(attachment.getBusinessType(), BusinessType.TestedSamplePhoto.getCode()))
                && Func.isNotEmpty(attachment.getObjectID())) {
            String photoType;
            String separator;
            if (LanguageType.check(languageId, LanguageType.Chinese)) {
                photoType = SamplePhotoFileTypeEnums.getTypeCn(attachment.getPhotoType());
                separator = "-样品 ";
            } else {
                photoType = SamplePhotoFileTypeEnums.getTypeEn(attachment.getPhotoType());
                separator = "-Sample ";
            }
            String tag = attachment.getTag();
            String photoDescription = "";
            if(Func.isNotEmpty(tag)){
                if(Func.isNotEmpty(tag)){
                    try {
                        OrderAttachmentTagDTO orderAttachmentTagDTO = JSONObject.parseObject(tag, OrderAttachmentTagDTO.class);
                        List<OrderAttachmentTagDTO.OrderAttachmentTagLanguageDTO> languageList = orderAttachmentTagDTO.getLanguageList();
                        if(Func.isNotEmpty(languageList)){
                            OrderAttachmentTagDTO.OrderAttachmentTagLanguageDTO orderAttachmentTagLanguageDTO = languageList.parallelStream().filter(item -> Func.equalsSafe(item.getLanguageId(), languageId)).findAny().orElse(null);
                            if(Func.isNotEmpty(orderAttachmentTagLanguageDTO) && Func.isNotEmpty(orderAttachmentTagLanguageDTO.getPhotoDescription())){
                                photoDescription = orderAttachmentTagLanguageDTO.getPhotoDescription();
                            }
                        }
                    } catch (Exception e) {

                    }

                }

            }
            String resultName = photoType + separator + attachment.getSampleNo();
            if(Func.isNotEmpty(photoDescription)){
                resultName += "-";
                resultName += photoDescription;
            }
            return resultName;
        }
        return attachment.getAttachmentName();
    }


    private void buildOrderCrossLab(OrderBO order, List<OrderCrossLabPO> orderCrossLabPOList) {
        if (Func.isNotEmpty(orderCrossLabPOList)) {
            orderCrossLabPOList = orderCrossLabPOList.stream().filter(item -> Func.equalsSafe(item.getOrderNo(), order.getHeader().getOrderNo()) && ExecType.check(item.getExecType(),ExecType.AllSection))
                    .collect(Collectors.toList());
            if (Func.isNotEmpty(orderCrossLabPOList)) {
                List<OrderCrossLabBO> tops = Lists.newArrayList();
                orderCrossLabPOList.stream().forEach(item->{
                    OrderCrossLabBO orderCrossLab = Func.copy(item,OrderCrossLabBO.class);
                    if(Func.isNotEmpty(orderCrossLab.getToLab())){
                        LabInfo labInfoByLabCode = frameworkClient.getLabInfoByLabCode(orderCrossLab.getToLab());
                        if(Func.isNotEmpty(labInfoByLabCode)){
                            orderCrossLab.setToLabId(labInfoByLabCode.getLaboratoryID());
                        }
                    }
                    tops.add(orderCrossLab);
                });
                order.setTops(tops);
            }
        }

    }

    private void buildCareLabels(OrderBO order, List<CareLabelBO> careLabelList) {
        if (Func.isEmpty(careLabelList)) {
            return;
        }
        careLabelList = careLabelList.stream().filter(item -> Func.equalsSafe(item.getGeneralOrderId(), order.getHeader().getOrderId()))
                .collect(Collectors.toList());
        if (Func.isEmpty(careLabelList)) {
            return;
        }
        order.setCareLabels(careLabelList);
    }

    private void buildOrderServiceRequirement(OrderBO order, List<TestRequestPO> testRequestList, List<TestRequestContactPO> testRequestContactList, List<OrderReportReceiverPO> orderReportReceiverList) {
        ServiceRequirementBO serviceRequirement = new ServiceRequirementBO();
        ServiceRequirementReportBO orderReportRequirement = new ServiceRequirementReportBO();
        List<ReportLanguageBO> languageList = Lists.newArrayList();
        List<DeliverBO> delivers = Lists.newArrayList();
        String reportLanguage = null;
        if (Func.isNotEmpty(testRequestList)) {
            TestRequestPO orderTestRequest = testRequestList.stream().filter(item -> Func.equals(item.getGeneralOrderId(), order.getHeader().getOrderId())).findAny().orElse(null);
            if (Func.isNotEmpty(orderTestRequest)) {
                Func.copy(orderTestRequest, orderReportRequirement);
                Func.copy(orderTestRequest, serviceRequirement);
                // 名称不一致的字段手动Copy
                orderReportRequirement.setNeedAccreditation(orderTestRequest.getReportAccreditationNeededFlag());
                orderReportRequirement.setNeedDraft(orderTestRequest.getDraftReportRequired());
                orderReportRequirement.setNeedPhoto(orderTestRequest.getTakePhotoFlag());
                if(Func.isNotEmpty(orderTestRequest.getReportLanguage())){
                    orderReportRequirement.setReportLanguage(Integer.valueOf(orderTestRequest.getReportLanguage()));
                    orderReportRequirement.setReportLanguageName(Func.isNotEmpty(ReportLanguage.findName(orderTestRequest.getReportLanguage()))?
                            ReportLanguage.findName(orderTestRequest.getReportLanguage()).getName():null);
                }
                orderReportRequirement.setHardcopyRequired(orderTestRequest.getHardCopyFlag());
                serviceRequirement.setOtherRequestRemark(orderTestRequest.getOtherRequirements());
                serviceRequirement.setReturnTestSampleFlag(orderTestRequest.getReturnTestedSampleFlag());
                serviceRequirement.setReturnTestSampleRemark(orderTestRequest.getReturnTestedSampleRemark());
                reportLanguage = orderTestRequest.getReportLanguage();
            }
        }
        if(Func.isNotEmpty(testRequestContactList)){
            List<TestRequestContactPO> testRequestContactPOList = testRequestContactList.stream().filter(item -> Func.equals(item.getOrderId(), order.getHeader().getOrderId())).collect(Collectors.toList());
            if (Func.isNotEmpty(testRequestContactPOList)){
                testRequestContactPOList.forEach(item->{
                    DeliverBO deliver = new DeliverBO();
                    deliver.setId(item.getId());
                    deliver.setContactsType(item.getContactsType());
                    String deliverTo = item.getDeliverTo();
                    if (Func.isNotEmpty(deliverTo)) {
                        List<Integer> deliverToList = Arrays.stream(deliverTo.split(StringPool.COMMA)).map(String::trim).filter(Func::isNotEmpty).map(Integer::parseInt).collect(Collectors.toList());
                        deliver.setDeliveryTo(deliverToList);
                    }
                    deliver.setDeliveryOthers(item.getDeliverToOthers());
                    delivers.add(deliver);
                });
            }
        }
        if (Func.isNotEmpty(orderReportReceiverList)) {

            OrderReportReceiverPO reportReceiver = orderReportReceiverList.stream().filter(item -> Func.equals(item.getGeneralOrderId(), order.getHeader().getOrderId())).findAny().orElse(null);
            if (Func.isNotEmpty(reportReceiver)) {
                String reportHeader = reportReceiver.getReportHeader();
                String reportDeliveredTo = reportReceiver.getReportDeliveredTo();
                if (ReportLanguage.checkLanguage(reportLanguage,ReportLanguage.EnglishReportOnly)) {
                    orderReportRequirement.setReportHeader(reportHeader);
                    orderReportRequirement.setReportAddress(reportDeliveredTo);
                    ReportLanguageBO reportLanguageBO = new ReportLanguageBO();
                    reportLanguageBO.setLanguageId(LanguageType.English.getLanguageId());
                    reportLanguageBO.setReportAddress(reportDeliveredTo);
                    reportLanguageBO.setReportHeader(reportHeader);
                    languageList.add(reportLanguageBO);
                }
                if (ReportLanguage.checkLanguage(reportLanguage,ReportLanguage.ChineseReportOnly)) {
                    orderReportRequirement.setReportHeader(reportHeader);
                    orderReportRequirement.setReportAddress(reportDeliveredTo);
                    ReportLanguageBO reportLanguageBO = new ReportLanguageBO();
                    reportLanguageBO.setLanguageId(LanguageType.Chinese.getLanguageId());
                    reportLanguageBO.setReportAddress(reportDeliveredTo);
                    reportLanguageBO.setReportHeader(reportHeader);
                    languageList.add(reportLanguageBO);
                }
                if (ReportLanguage.checkLanguage(reportLanguage,ReportLanguage.EnglishAndChineseReport,ReportLanguage.MultilingualReport)) {
                    List<String> reportHeaderList;
                    List<String> reportDeliveredToList;
                    ReportLanguageBO reportLanguageCN = new ReportLanguageBO();
                    reportLanguageCN.setLanguageId(LanguageType.Chinese.getLanguageId());
                    ReportLanguageBO reportLanguageEN = new ReportLanguageBO();
                    reportLanguageEN.setLanguageId(LanguageType.English.getLanguageId());
                    if (Func.isNotEmpty(reportHeader)) {
                        reportHeaderList = Arrays.stream(reportHeader.split("\\|\\|\\|")).collect(Collectors.toList());
                        if(reportHeaderList.size()>0){
                            orderReportRequirement.setReportHeader(reportHeaderList.get(0));
                            reportLanguageCN.setReportHeader(reportHeaderList.get(0));
                            if (reportHeaderList.size() > 1) {
                                reportLanguageEN.setReportHeader(reportHeaderList.get(1));
                            }
                        }
                    }
                    if (Func.isNotEmpty(reportDeliveredTo)) {
                        reportDeliveredToList = Arrays.stream(reportDeliveredTo.split("\\|\\|\\|")).collect(Collectors.toList());
                        if(reportDeliveredToList.size()>0){
                            orderReportRequirement.setReportAddress(reportDeliveredToList.get(0));
                            reportLanguageCN.setReportAddress(reportDeliveredToList.get(0));
                            if (reportDeliveredToList.size() > 1) {
                                reportLanguageEN.setReportAddress(reportDeliveredToList.get(1));
                            }
                        }
                    }
                    languageList.add(reportLanguageCN);
                    languageList.add(reportLanguageEN);
                    orderReportRequirement.setLanguageList(languageList);
                }
            }
        }
        serviceRequirement.setDelivers(delivers);
        serviceRequirement.setReport(orderReportRequirement);
        order.setServiceRequirement(serviceRequirement);
    }

    private void buildCustomerInfo(OrderBO order, List<CustomerPO> customerList) {
        if (Func.isNotEmpty(customerList)) {
            customerList = customerList.stream().filter(e -> Func.equalsSafe(e.getGeneralOrderId(),order.getId().getOrderId())).collect(Collectors.toList());
            List<CustomerBO> customerBOList = Lists.newArrayList();
            customerList.stream().forEach(customer -> {
                customerBOList.add(convertCustomer(customer));
            });
            order.setCustomerList(customerBOList);
        }
    }

    private CustomerBO convertCustomer(CustomerPO customerInstance) {
        CustomerBO customer = new CustomerBO();
        customer.setId(customerInstance.getId());
        customer.setCustomerUsage(GpoCustomerType.enumOf(customerInstance.getCustomerUsage()).getStatus());
        customer.setBossNo(customerInstance.getBossNumber());
        customer.setCustomerGroupCode(customerInstance.getBuyerGroup());
        customer.setCustomerGroupName(customerInstance.getBuyerGroupName());
        customer.setCustomerName(customerInstance.getCustomerNameEn());
        if(Func.isEmpty(customer.getCustomerName())){
            customer.setCustomerName(customerInstance.getCustomerNameCn());
        }
        customer.setCustomerAddress(customerInstance.getCustomerAddressEn());
        customer.setCustomerId(customerInstance.getCustomerId());
        customer.setPaymentTerm(customerInstance.getPaymentTermName());
        customer.setAccountId(customerInstance.getAccountId());
        customer.setActiveIndicator(customerInstance.getActiveIndicator());
        List<CustomerLanguageBO> languageList = Lists.newArrayList();
        CustomerLanguageBO customerLanguageDTO = new CustomerLanguageBO();
        customerLanguageDTO.setLanguageId(LanguageType.Chinese.getLanguageId());
        customerLanguageDTO.setCustomerAddress(customerInstance.getCustomerAddressCn());
        customerLanguageDTO.setCustomerName(customerInstance.getCustomerNameCn());
        languageList.add(customerLanguageDTO);
        CustomerLanguageBO customerLanguageEnDTO = new CustomerLanguageBO();
        customerLanguageEnDTO.setLanguageId(LanguageType.English.getLanguageId());
        customerLanguageEnDTO.setCustomerAddress(customerInstance.getCustomerAddressEn());
        customerLanguageEnDTO.setCustomerName(customerInstance.getCustomerNameEn());
        languageList.add(customerLanguageEnDTO);
        customer.setLanguageList(languageList);
        List<CustomerContactBO> customerContactList = Lists.newArrayList();
        CustomerContactBO customerContactBO = new CustomerContactBO();
        customerContactBO.setContactEmail(customerInstance.getContactPersonEmail());
        customerContactBO.setContactName(customerInstance.getContactPersonName());
        customerContactBO.setBossContactId(customerInstance.getBossContactId());
        customerContactBO.setContactFAX(customerInstance.getContactPersonFax());
        customerContactBO.setContactMobile(customerInstance.getContactPersonPhone2());
        customerContactBO.setContactTelephone(customerInstance.getContactPersonPhone1());
        customerContactBO.setBossSiteUseId(customerInstance.getBossSiteUseId());
        customerContactBO.setContactSgsMartAccount(customerInstance.getSgsMartAccount());
        customerContactBO.setContactSgsMartUserId(customerInstance.getSgsMartUserId());
        customerContactList.add(customerContactBO);
        customer.setCustomerContactList(customerContactList);
        return customer;
    }

    private void buildSlOrderInfo(OrderBO order, List<SLOrderPO> slOrderList) {
        if (Func.isNotEmpty(order) && Func.isNotEmpty(order.getHeader()) && Func.isNotEmpty(slOrderList)) {
            SLOrderPO slOrderPO = slOrderList.stream().filter(item -> Func.equals(item.getGeneralOrderID(), order.getHeader().getOrderId())).findAny().orElse(null);
            if (Func.isNotEmpty(slOrderPO)) {
                Func.copy(slOrderPO, order.getHeader());
                order.getHeader().setCsName(slOrderPO.getCSName());
                order.getHeader().setCsEmail(slOrderPO.getCSEmail());
                order.getHeader().setCsContact(slOrderPO.getCSContact());
                order.getHeader().setCuttingExpectDueDate(slOrderPO.getCuttingExpectDueDate());
                order.getHeader().setJobExpectDueDate(slOrderPO.getJobExpectDueDate());
                order.getHeader().setSampleConfirmDate(slOrderPO.getSampleConfirmDate());
                order.getHeader().setCustomerRefNo(slOrderPO.getCustomerRefNo());
                order.getHeader().setSampleResubmissionDate(slOrderPO.getSampleResubmissionDate());
                order.getHeader().setCaseType(slOrderPO.getCaseType());
                order.getPayment().setQuoteCurrencyId(slOrderPO.getQuoteCurrencyID());
                order.getFlags().setDateEditFlag(slOrderPO.getDateEditFlag());
                order.getFlags().setSetDueDateFlag(slOrderPO.getSetDueDateFlag());
                order.getFlags().setSameAsApplicantFlag(slOrderPO.getSameAsApplicantFlag());
//                order.getId().setGpnOrderId(slOrderPO.getId());
            }
        }
    }

    private void buildGpnOrderInfo(OrderBO order, List<GeneralOrderInstancePO> gpnOrderList) {
        if (Func.isNotEmpty(order) && Func.isNotEmpty(order.getHeader()) && Func.isNotEmpty(gpnOrderList)) {
            GeneralOrderInstancePO gpnOrder = gpnOrderList.stream().filter(item -> Func.equals(item.getOrderNo(), order.getHeader().getOrderNo())).findAny().orElse(null);
            if (Func.isNotEmpty(gpnOrder)) {
                order.getId().setGpnOrderId(gpnOrder.getId());
            }
        }
    }

    private void buildParcelInfo(OrderBO order,List<OrderParcelPO> orderParcelPOList){
        if(Func.isEmpty(orderParcelPOList)){
            return;
        }
        List<OrderParcelPO> thisOrderParcelPOList = orderParcelPOList.stream().filter(item -> Func.equals(item.getGeneralOrderId(), order.getId().getOrderId())).collect(Collectors.toList());
        if(Func.isEmpty(thisOrderParcelPOList)){
            return;
        }
        List<ParcelBO> parcelBOList = Lists.newArrayList();
        thisOrderParcelPOList.stream().forEach(orderParcelPO -> {
            ParcelBO parcelBO = new ParcelBO();
            parcelBO.setParcelDescription(orderParcelPO.getParcelDescription());
            parcelBO.setParcelNo(orderParcelPO.getParcelNo());
            parcelBO.setStatus(orderParcelPO.getStatus());
            parcelBOList.add(parcelBO);
        });
        order.setParcelList(parcelBOList);
    }

    private void buildPaidUpInfo(OrderBO order, List<PaidUpVO> paidUpVOList){
        List<OrderPaidUpBO> orderPaidUpBOList = Lists.newArrayList();
        if(Func.isNotEmpty(paidUpVOList)){
            paidUpVOList = paidUpVOList.stream().filter(e -> Func.equalsSafe(e.getOrderId(),order.getId().getOrderId())).collect(Collectors.toList());
            if(Func.isNotEmpty(paidUpVOList)){
                for(PaidUpVO paidUpVO : paidUpVOList){
                    OrderPaidUpBO orderPaidUpBO = new OrderPaidUpBO();
                    PaidUpIdBO paidUpIdBO = Func.copy(paidUpVO,PaidUpIdBO.class);
                    PaidUpHeaderBO paidUpHeaderBO = Func.copy(paidUpVO, PaidUpHeaderBO.class);
                    orderPaidUpBO.setId(paidUpIdBO);
                    orderPaidUpBO.setHeader(paidUpHeaderBO);
                    orderPaidUpBO.setActiveIndicator(paidUpVO.getActiveIndicator());
                    orderPaidUpBO.setCreateDate(paidUpVO.getCreatedDate());
                    orderPaidUpBO.setModifiedBy(paidUpVO.getModifiedBy());
                    orderPaidUpBO.setModifiedDate(paidUpVO.getModifiedDate());
                    orderPaidUpBOList.add(orderPaidUpBO);
                }
            }
        }
        order.setPaidUpList(orderPaidUpBOList);
    }
    // 组装联系人信息
    private void buildContactPerson(OrderBO order, List<SLOrderPO> slOrderList, List<OrderPersonPO> orderPersonList) {
        if (Func.isNotEmpty(slOrderList)) {
            SLOrderPO slOrderPO = slOrderList.stream().filter(item -> Func.equals(item.getGeneralOrderID(), order.getHeader().getOrderId())).findAny().orElse(null);
            List<ContactPersonBO> contactPersonList = Lists.newArrayList();
            // CS
            if (Func.isNotEmpty(slOrderPO.getCSName())) {
                ContactPersonBO csPerson = new ContactPersonBO();
                csPerson.setContactUsage(ContactUsage.CS.getCode());
                csPerson.setContactName(slOrderPO.getCSName());
                csPerson.setContactEmail(slOrderPO.getCSEmail());
                csPerson.setRegionAccount(slOrderPO.getCSName());
                csPerson.setResponsibleTeamCode(slOrderPO.getResponsibleTeamCode());
                csPerson.setContactTelephone(slOrderPO.getCSContact());
                contactPersonList.add(csPerson);
            }
            if (Func.isNotEmpty(slOrderPO.getOr())) {
                ContactPersonBO csPerson = new ContactPersonBO();
                csPerson.setContactUsage("OR");
                csPerson.setContactName(slOrderPO.getOr());
                csPerson.setRegionAccount(slOrderPO.getOr());
                EmpInfoReq empInfoReq = new EmpInfoReq();
                empInfoReq.setLabCode(order.getLab().getLabCode());
                empInfoReq.setRegionAccount(slOrderPO.getOr());
                BaseResponse<EmpInfoExtRsp> empInfoExtRsp = userManagementClient.getEmpInfo(empInfoReq);
                if (empInfoExtRsp.isSuccess() && Func.isNotEmpty(empInfoExtRsp.getData())) {
                    csPerson.setContactEmail(empInfoExtRsp.getData().getEmail());
                    csPerson.setContactTelephone(empInfoExtRsp.getData().getTelephone());
                }
                contactPersonList.add(csPerson);
            }
            if (Func.isNotEmpty(slOrderPO.getCr())) {
                ContactPersonBO csPerson = new ContactPersonBO();
                csPerson.setContactUsage("CR");
                csPerson.setContactName(slOrderPO.getCr());
                csPerson.setRegionAccount(slOrderPO.getCr());
                EmpInfoReq empInfoReq = new EmpInfoReq();
                empInfoReq.setLabCode(order.getLab().getLabCode());
                empInfoReq.setRegionAccount(slOrderPO.getCr());
                BaseResponse<EmpInfoExtRsp> empInfoExtRsp = userManagementClient.getEmpInfo(empInfoReq);
                if (empInfoExtRsp.isSuccess() && Func.isNotEmpty(empInfoExtRsp.getData())) {
                    csPerson.setContactEmail(empInfoExtRsp.getData().getEmail());
                    csPerson.setContactTelephone(empInfoExtRsp.getData().getTelephone());
                }
                contactPersonList.add(csPerson);
            }
            //
            if (Func.isNotEmpty(orderPersonList)) {
                orderPersonList =  orderPersonList.stream().filter(item->Func.equalsSafe(item.getGeneralOrderId(),order.getId().getOrderId())).collect(Collectors.toList());
                orderPersonList.stream().forEach(orderPerson -> {
                    if(Func.isEmpty(orderPerson.getRegionAccount())){
                        return;
                    }
                    ContactPersonBO contactPerson = new ContactPersonBO();
                    contactPerson.setId(orderPerson.getId());
                    contactPerson.setContactEmail(orderPerson.getEmail());
                    contactPerson.setContactUsage(orderPerson.getPersonType());
                    contactPerson.setRegionAccount(orderPerson.getRegionAccount());
                    contactPersonList.add(contactPerson);
                });
            }
            order.setContactPersonList(contactPersonList);
        }
    }

    @Override
    public BaseResponse before(OrderContext<OrderQueryReq> context) {
        //查询BU主语言
        BaseResponse<LanguageType> languageTypeRsp = buParam.getPrimaryLanguage(context.getProductLineCode());
        if(languageTypeRsp.isSuccess() && Func.isNotEmpty(languageTypeRsp.getData())){
            context.setBuLanguageId(languageTypeRsp.getData().getLanguageId());
        }
        OrderQueryReq orderQueryReq = context.getParam();
        orderQueryReq.setProductLineCode(context.getProductLineCode());
        BaseResponse<List<GeneralOrderPO>> generalOrderResponse = generalOrderService.query2(orderQueryReq);
        List<GeneralOrderPO> generalOrderList = generalOrderResponse.getData();
        Boolean baseQuery = orderQueryReq.getBaseQuery();
        if (Func.isNotEmpty(generalOrderList)) {
            context.setGeneralOrderList(generalOrderList);
            OrderIdReq orderIdReq = new OrderIdReq();
            orderIdReq.setOrderIdList(generalOrderList.stream().map(GeneralOrderPO::getId).collect(Collectors.toSet()));
            orderIdReq.setOrderNoList(generalOrderList.stream().map(GeneralOrderPO::getOrderNo).collect(Collectors.toSet()));
            BaseResponse<List<LabInstancePO>> labResponse = labInstanceService.select(orderIdReq);
            if (labResponse.isSuccess()) {
                context.setLabList(labResponse.getData());
            }
            BaseResponse<List<SLOrderPO>> slOrderResponse = slOrderService.select(orderIdReq);
            if (slOrderResponse.isSuccess()) {
                context.setSlOrderList(slOrderResponse.getData());
            }
            OrderInstanceQueryReq orderInstanceQueryReq = new OrderInstanceQueryReq();
            orderInstanceQueryReq.setOrderNoList(generalOrderList.stream().map(GeneralOrderPO::getOrderNo).collect(Collectors.toSet()));
            BaseResponse<List<GeneralOrderInstancePO>> gpnOrderListRes = generalOrderInstanceService.select(orderInstanceQueryReq);
            if (gpnOrderListRes.isSuccess()) {
                context.setGeneralOrderInstanceList(gpnOrderListRes.getData());
            }
            ExternalNoQueryReq externalNoQueryReq = new ExternalNoQueryReq();
            externalNoQueryReq.setObjectType(Constants.OBJECT.ORDER.OBJECT_CODE);
            externalNoQueryReq.setObjectIdList(generalOrderList.stream().map(GeneralOrderPO::getId).collect(Collectors.toSet()));
            BaseResponse<List<ExternalNoBO>> externalNoBOListRsp = externalNoDomainService.queryBO(externalNoQueryReq);
            if(externalNoBOListRsp.isSuccess() && Func.isNotEmpty(externalNoBOListRsp.getData())){
                context.setExternalNoBOList(externalNoBOListRsp.getData());
            }
            BaseResponse<List<CustomerPO>> customerResponse = customerService.select(orderIdReq);
            if (customerResponse.isSuccess()) {
                context.setCustomerList(customerResponse.getData());
            }
            BaseResponse<List<TestRequestPO>> testRequestResponse = testRequestService.select(orderIdReq);
            if (testRequestResponse.isSuccess()) {
                context.setTestRequestList(testRequestResponse.getData());
            }
            BaseResponse<List<TestRequestContactPO>> testRequestContactResponse = testRequestContactService.select(orderIdReq);
            if (testRequestContactResponse.isSuccess()){
                context.setTestRequestContactList(testRequestContactResponse.getData());
            }
            BaseResponse<List<OrderReportReceiverPO>> reportReceiverResponse = orderReportReceiverService.select(orderIdReq);
            if (reportReceiverResponse.isSuccess()) {
                context.setOrderReportReceiverList(reportReceiverResponse.getData());
            }
            if(!baseQuery){
                BaseResponse<List<OrderPersonPO>> orderPersonResponse = orderPersonService.select(orderIdReq);
                if (orderPersonResponse.isSuccess()) {
                    context.setOrderPersonList(orderPersonResponse.getData());
                }
                OrderAttachmentQueryReq orderAttachmentQueryReq = new OrderAttachmentQueryReq();
                orderAttachmentQueryReq.setOrderIdList(generalOrderList.stream().map(GeneralOrderPO::getId).collect(Collectors.toSet()));
                BaseResponse<List<OrderAttachmentPO>> attachmentResponse = orderAttachmentService.select(orderAttachmentQueryReq);
                if (attachmentResponse.isSuccess()) {
                    context.setOrderAttachmentList(attachmentResponse.getData());
                }
                BaseResponse<List<OrderCrossLabPO>> orderCrossLabResponse = orderCrossLabService.select(orderIdReq);
                if (orderCrossLabResponse.isSuccess()) {
                    context.setOrderCrossLabList(orderCrossLabResponse.getData());
                }
                OrderTrfReq orderTrfReq = new OrderTrfReq();
                orderTrfReq.setOrderIdList(Sets.newHashSet(generalOrderList.stream().map(GeneralOrderPO::getId).collect(Collectors.toSet())));
                List<TrfBO> trfList = orderTrfRelationshipService.queryBO(orderTrfReq);
                if(Func.isNotEmpty(trfList)){
                    context.setTrfList(trfList);
                }
                List<OrderExtPO> orderExtPOList = orderExtService.select(orderIdReq);
                if(Func.isNotEmpty(orderExtPOList)){
                    context.setOrderExtPOList(orderExtPOList);
                }
                ProductSampleQueryReq productSampleQueryReq = new ProductSampleQueryReq();
                productSampleQueryReq.setOrderIdList(Sets.newHashSet(generalOrderList.stream().map(GeneralOrderPO::getId).collect(Collectors.toSet())));
                BaseResponse<List<ProductSampleBO>> productSampleRsp = productSampleDomainService.queryOrderProductSample(productSampleQueryReq);
                if(productSampleRsp.isSuccess() && Func.isNotEmpty(productSampleRsp.getData())){
                    List<ProductSampleBO> productSampleBOList = productSampleRsp.getData();
                    context.setProductSampleBOList(productSampleBOList);
                }
                CareLabelReq careLabelReq = new CareLabelReq();
                careLabelReq.setOrderIds(orderIdReq.getOrderIdList());
                BaseResponse<List<CareLabelBO>> careLabelRes = careLabelService.queryCareLabelList(careLabelReq);
                if (careLabelRes.isSuccess()) {
                    context.setCareLabelList(careLabelRes.getData());
                }
                PaidUpQueryReq paidUpQueryReq = new PaidUpQueryReq();
                paidUpQueryReq.setOrderIdList(orderIdReq.getOrderIdList());
                List<PaidUpVO> paidUpPOList = paidUpService.select(paidUpQueryReq);
                context.setPaidUpVOList(paidUpPOList);
                OrderParcelReq parcelReq = new OrderParcelReq();
                parcelReq.setOrderIdList(orderIdReq.getOrderIdList());
                List<OrderParcelPO> orderParcelList =orderParcelService.query(parcelReq);
                if(Func.isNotEmpty(orderParcelList)){
                    context.setOrderParcelList(orderParcelList);
                }
            }
        }
        return super.before(context);
    }

    @Override
    public BaseResponse<List<OrderBO>> execute(OrderContext<OrderQueryReq> context) {
        return buildDomain(context);
    }

    @Override
    public BaseResponse after(OrderContext<OrderQueryReq> context) {
        return BaseResponse.newSuccessInstance(context.getOrderList());
    }
}
