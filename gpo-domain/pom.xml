<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>gpo-micro-service</artifactId>
		<groupId>com.sgs.gpo</groupId>
		<version>0.1.83</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>
	<version>${gpo-micro-service.version}</version>
	<artifactId>gpo-domain</artifactId>

	<dependencies>
		<dependency>
			<groupId>com.sgs.gpo</groupId>
			<artifactId>gpo-integration</artifactId>
		</dependency>
		<dependency>
			<groupId>com.sgs.gpo</groupId>
			<artifactId>gpo-dbstorages</artifactId>
		</dependency>
		<dependency>
			<groupId>com.sgs.gpo</groupId>
			<artifactId>gpo-domain-common</artifactId>
		</dependency>
		<dependency>
			<groupId>com.sgs.gpo</groupId>
			<artifactId>gpo-facade</artifactId>
		</dependency>
		<dependency>
			<groupId>com.sgs.gpo</groupId>
			<artifactId>gpo-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.sgs.framework</groupId>
			<artifactId>sgs-framework-log</artifactId>
		</dependency>
		<dependency>
			<groupId>com.sgs.framework</groupId>
			<artifactId>sgs-framework-open-platform</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
			<version>2.6</version>
		</dependency>
		<dependency>
			<groupId>org.freemarker</groupId>
			<artifactId>freemarker</artifactId>
			<version>2.3.28</version>
		</dependency>
		<dependency>
			<groupId>net.sf.ezmorph</groupId>
			<artifactId>ezmorph</artifactId>
			<version>1.0.6</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba.cola</groupId>
			<artifactId>cola-component-extension-starter</artifactId>
			<version>4.3.1</version>
		</dependency>
		<dependency>
			<groupId>com.sgs.gpo</groupId>
			<artifactId>preorder-facade-model</artifactId>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<configuration>
					<skip>true</skip>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>