<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.sgs.gpo</groupId>
    <artifactId>gpo-micro-service</artifactId>
    <packaging>pom</packaging>
    <version>0.1.83</version>

    <modules>
        <module>gpo-domain</module>
        <module>gpo-web</module>
        <module>gpo-facade</module>
        <module>gpo-facade-impl</module>
        <module>gpo-integration</module>
        <module>gpo-test</module>
        <module>gpo-dbstorages</module>
        <module>gpo-facade-model</module>
        <module>gpo-core</module>
        <module>gpo-biz</module>
        <module>gpo-domain-pptestline</module>
        <module>gpo-domain-testmatrix</module>
        <module>gpo-domain-testsample</module>
        <module>gpo-domain-common</module>
        <module>gpo-domain-order</module>
        <module>gpo-domain-testdata</module>
        <module>gpo-domain-report</module>
        <module>gpo-domain-payment</module>
    </modules>

    <properties>
        <java.version>1.8</java.version>
        <maven-deploy-plugin.version>2.8.2</maven-deploy-plugin.version>

        <!-- 文件拷贝时的编码 -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <!-- 编译时的编码 -->
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.test.skip>true</maven.test.skip>

        <spring.boot.version>2.3.9.RELEASE</spring.boot.version>
        <spring.cloud.version>Greenwich.SR3</spring.cloud.version>
        <spring.loaded.version>1.2.8.RELEASE</spring.loaded.version>
        <spring.boot.hateoas.version>0.25.1</spring.boot.hateoas.version>
        <redisson.version>3.9.0</redisson.version>
        <mysql.version>8.0.26</mysql.version>
        <druid.version>1.2.8</druid.version>

        <httpclient.version>4.5.2</httpclient.version>
        <httpcore.version>4.4.4</httpcore.version>

        <dozer.version>5.5.1</dozer.version>

        <validation-api.version>2.0.1.Final</validation-api.version>
        <hibernate-validator.version>6.0.15.Final</hibernate-validator.version>

        <commons-lang3.version>3.4</commons-lang3.version>
        <commons-io.version>2.4</commons-io.version>
        <commons-fileupload.version>1.3.2</commons-fileupload.version>
        <lombok.version>1.16.18</lombok.version>

        <log4j-over-slf4j.version>1.7.25</log4j-over-slf4j.version>
        <fastjson.version>1.2.4</fastjson.version>
        <poi.version>3.17</poi.version>
        <jboss-jaxrs-api.version>1.0.0.Final</jboss-jaxrs-api.version>

        <dubbo.version>2.8.4</dubbo.version>
        <junit.version>4.12</junit.version>

        <sgs.infrastructure.version>0.0.16-SNAPSHOT</sgs.infrastructure.version>
        <sgs.framework.unpack.version>1.1.41-SNAPSHOT</sgs.framework.unpack.version>

        <zkclient.version>0.1</zkclient.version>
        <javassist.version>3.20.0-GA</javassist.version>
        <joda-time.verson>2.10</joda-time.verson>
        <cxf.version>3.1.6</cxf.version>
        <asm.version>5.0.4</asm.version>

        <gpo-micro-service.version>0.1.83</gpo-micro-service.version>
        <axis.version>1.4</axis.version>
        <jaxrpc.version>1.1</jaxrpc.version>
        <commons.discovery.verion>0.2</commons.discovery.verion>

        <jsoup>1.8.3</jsoup>

        <mybatis.plus.version>3.4.3.4</mybatis.plus.version>
        <sgs.framework.version>1.0.963-beta</sgs.framework.version>

        <sgs.framework.facade.version>1.0.37</sgs.framework.facade.version>
        <sgs.trimslocal.version>1.2.33</sgs.trimslocal.version>
        <gpp.version>1.2.146</gpp.version>
        <gpn.version>1.1.459</gpn.version>
        <gpo.version>1.1.553</gpo.version>
        <user.version>1.0.10</user.version>
        <grus-core.version>1.1.0</grus-core.version>
        <customer.version>1.0.12</customer.version>
        <bizlog.version>1.2.19</bizlog.version>
        <grus.async.version>1.2.11</grus.async.version>
        <kafkaclient.version>1.0.31</kafkaclient.version>
        <gpo-core-api.version>1.0.45</gpo-core-api.version>
        <jboss-jaxrs-api.version>1.0.0.Final</jboss-jaxrs-api.version>
        <testdatabiz.version>1.3.46-SNAPSHOT</testdatabiz.version>

    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- spring-boot配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda-time.verson}</version>
            </dependency>

            <dependency>
                <groupId>com.github.sgroschupf</groupId>
                <artifactId>zkclient</artifactId>
                <version>${zkclient.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <dependency>
                <groupId>ru.yandex.clickhouse</groupId>
                <artifactId>clickhouse-jdbc</artifactId>
                <version>0.1.53</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore</artifactId>
                <version>${httpcore.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons-fileupload.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>3.4.8</version>
                <exclusions>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>dubbo</artifactId>
                <version>${dubbo.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>log4j-over-slf4j</artifactId>
                <version>${log4j-over-slf4j.version}</version>
            </dependency>


            <!-- self -->
            <dependency>
                <groupId>com.sgs.gpo</groupId>
                <artifactId>gpo-biz</artifactId>
                <version>${gpo-micro-service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.gpo</groupId>
                <artifactId>gpo-web</artifactId>
                <version>${gpo-micro-service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.gpo</groupId>
                <artifactId>gpo-domain</artifactId>
                <version>${gpo-micro-service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.gpo</groupId>
                <artifactId>gpo-domain-common</artifactId>
                <version>${gpo-micro-service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.gpo</groupId>
                <artifactId>gpo-domain-pptestline</artifactId>
                <version>${gpo-micro-service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.gpo</groupId>
                <artifactId>gpo-domain-order</artifactId>
                <version>${gpo-micro-service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.gpo</groupId>
                <artifactId>gpo-domain-testsample</artifactId>
                <version>${gpo-micro-service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.gpo</groupId>
                <artifactId>gpo-domain-testmatrix</artifactId>
                <version>${gpo-micro-service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.gpo</groupId>
                <artifactId>gpo-domain-report</artifactId>
                <version>${gpo-micro-service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.gpo</groupId>
                <artifactId>gpo-domain-payment</artifactId>
                <version>${gpo-micro-service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.gpo</groupId>
                <artifactId>gpo-facade</artifactId>
                <version>${gpo-micro-service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.gpo</groupId>
                <artifactId>gpo-facade-impl</artifactId>
                <version>${gpo-micro-service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.gpo</groupId>
                <artifactId>gpo-core</artifactId>
                <version>${gpo-micro-service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.gpo</groupId>
                <artifactId>gpo-integration</artifactId>
                <version>${gpo-micro-service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.gpo</groupId>
                <artifactId>gpo-test</artifactId>
                <version>${gpo-micro-service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.gpo</groupId>
                <artifactId>gpo-dbstorages</artifactId>
                <version>${gpo-micro-service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.gpo</groupId>
                <artifactId>gpo-facade-model</artifactId>
                <version>${gpo-micro-service.version}</version>
            </dependency>

            <dependency>
                <artifactId>liquibase-core</artifactId>
                <groupId>org.liquibase</groupId>
                <version>4.5.0</version>
            </dependency>

            <dependency>
                <groupId>com.sgs.framework</groupId>
                <artifactId>FrameWorkFacadeService</artifactId>
                <version>${sgs.framework.facade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.customer</groupId>
                <artifactId>CustomerFacadeService</artifactId>
                <version>${customer.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.springfox</groupId>
                        <artifactId>springfox-boot-starter</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sgs.framework</groupId>
                <artifactId>sgs-framework-context</artifactId>
                <version>${sgs.framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.framework</groupId>
                <artifactId>sgs-framework-context-extension</artifactId>
                <version>${sgs.framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.framework</groupId>
                <artifactId>sgs-framework-log</artifactId>
                <version>${sgs.framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.framework</groupId>
                <artifactId>sgs-framework-launch</artifactId>
                <version>${sgs.framework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sgs.framework</groupId>
                <artifactId>sgs-framework-open-platform</artifactId>
                <version>${sgs.framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.trimslocal</groupId>
                <artifactId>trimslocal-facade</artifactId>
                <version>${sgs.trimslocal.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.springfox</groupId>
                        <artifactId>springfox-boot-starter</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.sgs.pse</groupId>
                <artifactId>general-preorder-priceengine-facade</artifactId>
                <version>${gpp.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.sgs.gpo</groupId>
                        <artifactId>preorder-facade</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sgs.pse</groupId>
                <artifactId>general-preorder-priceengine-facade-model</artifactId>
                <version>${gpp.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.sgs.gpo</groupId>
                        <artifactId>preorder-facade</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sgs.gpn</groupId>
                <artifactId>otsnotes-facade</artifactId>
                <version>${gpn.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.sgs.gpo</groupId>
                        <artifactId>preorder-facade-model</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sgs.gpn</groupId>
                <artifactId>otsnotes-facade-model</artifactId>
                <version>${gpn.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.gpo</groupId>
                <artifactId>preorder-facade</artifactId>
                <version>${gpo.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.gpo</groupId>
                <artifactId>preorder-facade-model</artifactId>
                <version>${gpo.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.grus</groupId>
                <artifactId>sgs-grus-bizlog</artifactId>
                <version>${bizlog.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.grus</groupId>
                <artifactId>sgs-grus-async</artifactId>
                <version>${grus.async.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.preorder</groupId>
                <artifactId>gpo-core-api</artifactId>
                <version>${gpo-core-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cola</groupId>
                <artifactId>cola-component-extension-starter</artifactId>
                <version>4.3.1</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.spec.javax.ws.rs</groupId>
                <artifactId>jboss-jaxrs-api_2.0_spec</artifactId>
                <version>${jboss-jaxrs-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.testdatabiz</groupId>
                <artifactId>testdatabiz-facade-model</artifactId>
                <version>${testdatabiz.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <repositories>
        <repository>
            <id>nexus</id>
            <name>nexus</name>
            <url>https://cnmaven.sgs.net/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>sgs-plugin</id>
            <url>https://cnmaven.sgs.net/nexus/content/groups/public/</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>


    <distributionManagement>
        <repository>
            <id>my-deploy-release</id>
            <url>https://cnmaven.sgs.net/nexus/content/repositories/releases/</url>
        </repository>

        <snapshotRepository>
            <id>my-deploy-snapshot</id>
            <url>https://cnmaven.sgs.net/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <!--<plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <dependencies>
                    <dependency>
                        <groupId>org.springframework</groupId>
                        <artifactId>springloaded</artifactId>
                        <version>${spring.loaded.version}</version>
                    </dependency>
                </dependencies>
            </plugin>-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>