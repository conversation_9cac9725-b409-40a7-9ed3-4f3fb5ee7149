package com.sgs.gpo.core.constants;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/15 16:41
 */
public class Constants {
    public static final String SYSTEM = "system";
    public static final String BU_CODE = "productLineCode";
    public static final String LOCATION_CODE = "locationCode";
    public static final String LAB_CODE = "labCode";
    public static final String GENERAL_BU_CODE = "general";
    public static final String LOG_PREFIX = "logPrefix";
    public static final String NEW_LINE = "<br/>";
    /**
     * 水单编号后缀
     */
    public static final String PAID_UP_SUFFIX = "P";
    public static final String PAID_UP_ADD = "paid up add";

    /**
     * 非结构化testLine digitalFlag 值
     */
    public static final String GPO_DIGITAL_FLAG = "N";

    public interface DEFAULT {
        Integer PAGE = 1;
        Integer ROWS = 10;
        Integer MAXROWS = 50;
    }

    public static final Integer QUERY_MAX_SIZE = 100;

    public static final String GENERATE_REPORT_SUFFIXES = "docx";
    public static final String GENERATE_REPORT_FILE_SUFFIXES = ".docx";
    public static final String REPORT_FILE_NAME_CN_SUFFIXES = "_CN";

    public interface FILE_TYPE {
        String WORD = "docx";
        String PDF = "PDF";
    }

    public enum TERM {

        SYSTEM_ID("systemId", "系统标识"),
        OBJECT("object", "对象"),
        ACTION("action", "操作"),
        LAB("lab", "实验室"),
        REQUEST("request", "请求");

        String code;
        String name;

        TERM(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

    public interface SWAGGER {

        interface OBJECT {
            String OBJECT_TEMPLATE = "Object Template";
            String OBJECT_ATTRIBUTE = "Object Attribute";
            String OBJECT_ACTION = "Object Action";
            String OBJECT_PROCESS = "Object Process";
            String OBJECT_BUSINESS_RULE = "Object Business Rule";
        }

        interface MODULAR {
            //主要模块
            String TRF = "01-申请单";
            String ENQUIRY = "02-询价单";
            String ORDER = "03-订单";
            String QUOTATION = "04-报价单";
            String JOB = "05-自测任务";
            String SUBCONTRACT = "06-分包任务";
            String TESTLINE = "07-测试项";
            String REPORT = "08-报告";
            String PAYMENT = "09-支付";

            //System
            String TODO = "50.待办";
            String SETTING = "51.配置";
            String BARCODE = "52.条码";
            String TAG = "53.标签";
            String FILTER = "54.自定义查询条件";
            String WORKFLOW = "55.工作流";
            String SYSTEM = "56.系统";
            String EMAIL = "57.Email审批";
            String TASK = "58.定时任务";
            String DEVOPS = "59.Devops";

            String EXTERNAL = "80.对外接口";
        }
    }

    public interface OBJECT {
        String TRF = "trf";
        String TABLE_CODE = "Table";
        String ENQUIRY_LIST = "EnquiryList";
        String ENQUIRY = "enquiry";

        interface OBJECT_ENQUIRY{
            String OBJECT_CODE = "Enquiry";
        }

        interface ORDER {
            String OBJECT_CODE = "Order";

            enum ACTION {
                create, sampleReceive, confirm, delivery, ToTesting;
            }

            interface SECTION {
                interface HEADER {
                    String CODE = "heads";
                    String salesPerson = "salesPerson";
                }

                interface PRODUCT_SAMPLE {
                    String CODE = "productSample";
                    String PRODUCT_LIST = "productList";
                }
            }
        }

        String QUOTATION = "quotation";
        //String SUBCONTRACT = "subcontract";
        String SUB_REPORT = "subReport";
        String MATRIX = "matrix";

        interface LAB {

            String name = "Lab";

            enum TYPE {
                SGS(0), THIRD(1);

                Integer type;

                TYPE(Integer type) {
                    this.type = type;
                }

                public Integer getType() {
                    return type;
                }
            }
        }

        interface JOB {
            String OBJECT_CODE = "Job";

            interface TESTLINE_GROUP_MODE {
                String ORDER = "Order";
                String LAB_SECTION = "LabSection";
            }

            enum ACTION {
                LabIn, LabOut;
            }
        }

        interface TEST_LINE {
            String OBJECT_CODE = "testLine";

            enum ACTION {
                LabIn;
            }
        }

        interface  PP_TEST_LINE {
            String OBJECT_CODE = "PPTestLine";
        }

        interface TEST_SCHEME {
            String OBJECT_CODE = "TestScheme";
        }

        interface TEST_MATRIX {
            String OBJECT_CODE = "testMatrix";

            enum ACTION {


            }
        }

        interface TEST_DATA_HEADER {
            String OBJECT_CODE = "testDataHeader";

            enum ACTION {


            }
        }

        interface REPORT {
            String OBJECT_CODE = "Report";
            String NAME = "Report";

            interface STATUS_CATEGORY {
                String EDIT = "Edit";
                String INACTIVE = "Inactive";
                String LOCK = "Lock";
                String CLOSED = "Closed";
                String BLOCK = "Block";
            }

            enum ACTION {
                CREATE, APPROVE, SOFTCOPY_DELIVERY
            }
        }

        interface SUBCONTRACT {
            String NAME = "Subcontract";

            enum ACTION {
                CREATE
            }
        }

        interface NOTICE {
            interface NOTICE_STATUS {
                String READ = "Read";
                String UN_READ = "Un Read";
            }
        }
    }

    public interface DB {
        String GPN = "gpn";
        String GPO = "gpo";
        String COMMON = "common";
        String CLICKHOUSE = "clickhouse";
    }

    public interface Tag {
        String KA = "KA";
    }

    public interface ACTION {
        String CALLRD = "callRD";
        String GENERATEREPORT = "generateReport";
    }

    public interface COMMON {
        Integer GENERAL = 0;
        String DECIMAL_FORMAT = ",###,###.00";
        String DECIMAL_DEFAULT = "0.00";
        String UNABLE_TO_CALCULATE = "Unable to calculate";
    }

    public interface SYS_KEY_GROUP {
        String CERTIFICATE_TYPE = "CertificateType";
    }

    public interface BU_PARAM {
        interface GROUP {
            String TEST = "test";
        }

        interface CODE {
            String LAB_SECTION_REQUIRED = "labSectionRequired";
            String PRINT_MASTER_LIST = "PrintMaterList";
        }

        interface QUOTATION {
            String GROUP = "Quotation";

            interface DISCOUNT_PERSION {
                String CODE = "DiscountPerson";

                interface MODEL {
                    String CUSTOMER_HOSTING = "CustomerHosting";
                    String ORDER_OWNER = "OrderOwner";
                    String CUSTOMER_HOSTING_EDIT = "CustomerHostingEdit";
                }

                interface CUSTOMER_ROLE {
                    String DEFAULT = "Applicant";
                    String APPLICANT = "Applicant";
                    String BUYER = "Buyer";
                }

            }

            ;

            interface MAIN_CURRENCY {
                String CODE = "MainCurrency";
            }

            ;

            interface PUSH_INVOICE {
                String PUSH_RULE_CODE = "PushInvoice";
                String PUSH_COUNT_LIMIT = "PushOrderLimit";
            }
        };
        interface TRF{
            String GROUP_CODE = "TRF";
            interface TRF_OPERATION_CONFIG {
                String CODE = "TrfOperationConfig";
            }
        }

        interface DISPLAY {
            String GROUP = "Display";

            interface PARAM_CODE {
                String DATA_LANGUAGE = "DataLanguage";
            }

            ;
        }

        interface ORDER {
            String GROUP = "order";

            interface TEST_LINE_PRINT_LEVEL {
                String CODE = "testLinePrintLevel";

                interface LEVEL {
                    String PP = "pp_tl";
                    String TL = "tl";
                }
            }

            interface BIND_TRF_EVENT_LIST {
                String CODE = "BindTrfEventList";
            }

            interface SHOW_ORDER_EXTERNAL_NO {
                String CODE = "ShowOrderExternalNo";

                interface VALUE {
                    String TRUE = "true";
                    String FALSE = "false";
                }
            }
        }

        interface REPORT {
            String GROUP = "Report";

            interface WORK_FLOW {
                String CODE = "Workflow";
            }

            ;

            interface REPORT_DELIVERY_PAY_MENT_RULE {
                String CODE = "ReportDeliveryWithPaymentStatus";
            }

            ;
        }

        interface JOB {
            String GROUP_CODE = "Job";

            interface CREATE_JOB_GROUP_BY {
                String CODE = "CreateJobGroupBy";

                interface VALUE {
                    String LAB_SECTION = "LabSection";
                    String ORDER = "Order";
                }

                ;
            }

            ;

            interface AUTO_CLOSED {
                String CODE = "AutoClosed";
            }

        }

        interface MESSAGE {
            String GROUP = "Message";

            interface PushDataAfterReportRevise {
                String CODE = "PushDataAfterReportRevise";
            }
        }

        interface BACKLIST {
            String GROUP = "backList";

            interface BACKLISTCREATEORDER {
                String CODE = "backListCreateOrder";
            }

            ;
        }

        ;

        interface SUBCONTRACT {
            String GROUP = "Subcontract";

            interface SUBCONTRACT_MODE {
                String CODE = "SubcontractMode";

                interface VALUES {

                }
            }

            ;
        }

        ;

        interface SUB_REPORT {
            String GROUP = "subReport";

            interface SEQUENCE {
                String CODE = "reportSequence";
            }

            ;
        }

        ;

        interface Notification{
            String GROUP = "Notification";
            interface CODE {
                String Pending_Notify = "PendingNotify";
                String Prelim_Result = "PrelimResult";
            }
            Integer BEFORE_DAYS= 3;
        }
    }

    public interface STATUS_CATEGORY {
        String EDIT = "edit";
        String LOCK = "lock";
        String SYNC = "sync";
        String INVALID = "invalid";
    }

    public interface OBJECT_ATTRIBUTE {
        interface COST_LIST {
            String OBJECT_CODE = "CostList";
            String OBJECT_CODE_SEARCH = "orderSearch";
//            String OBJECT_CODE_TABLE = "";
        }

        interface SUBCONTRACT_LIST {
            String OBJECT_CODE = "subcontractList";
            String OBJECT_CODE_SEARCH = "subcontractSearch";
        }

        interface JOB_LIST {
            String OBJECT_CODE = "jobList";
            String OBJECT_CODE_SEARCH = "jobSearch";
        }

        interface DATA_ENTRY_LIST {
            String OBJECT_CODE = "dataEntryList";
            String OBJECT_CODE_SEARCH = "dataEntrySearch";

            interface SUB_SECTION_CODE {
                String TL_DATAENTRY_TAB = "tlDataEntryTab";
            }
        }
    }

    public interface COUNTRY_CODE {
        String VN = "VN";
        String CN = "CN";
    }

    public interface TOPIC {
        String SYSTEM_LOG = "com.sgs.preorder.common.system.log";
        String DATA_ENTRY = "com.sgs.event.dataentry";
        String DATABASE_GPO = "com.sgs.database.gpo";
        String DATABASE_TW = "com.sgs.database.gpo.tw";
        String TOPIC_TODO = "com.sgs.preorder.kafka.todolist";
    }

    public static final String CLOUD_PREFIX = "cloud:";
    public static final Integer ActiveIndicator = 1;

    public static final int BATCH_SIZE = 50;

    // 特殊的Report Unit
    public static final String TEXT_ONLY = "Text only";
    public static final String NO_UNIT = "No Unit";

    // TODO 后续需要迁移

    /**
     * 发票同步定参
     */
    public interface PAYMENT_SYNC {
        interface PARAM {
            String[] F_CODE_LIST = {"AnyFcode"};
            String BU_SUFFIX = " LAB";
            String NET_WORK_TYPE = "1";
            Integer UPDATE_STATUS_SUCCESS = 3;
            Integer UPDATE_STATUS_FAIL = 4;
        }

        interface TYPE {
            interface INVOICE {
                String CODE = "1";
                String TYPE = "InvoiceLevel";
            }

            interface PAYMENT {
                String CODE = "2";
                String TYPE = "Payment";
            }

            interface FA_PIAO {
                String CODE = "3";
                String TYPE = "Fapiao";
            }

            interface AWB {
                String CODE = "4";
                String TYPE = "AWB";
            }
        }
        interface URL{
            String  QUERY_BACK_OFFICE_INTERFACE_FILE = "/backoffice/get.backOfficeInterfaceFile.htm";
            String  FILE_DOWNLOAD_BY_CLOUD_ID = "/FrameWorkApi/file/downloadByCloudID";
            String  UPDATE_BACK_OFFICE_RESULT_STATE = "/backoffice/get.backOfficeUpdateState.htm";
            String QUERY_INVOICE_PDF = "/backoffice/backOfficeApi/queryInvoicePDFList.htm";

        }
    }

    public static final String DIGITAL_REPORT_CALL_BACK_URL = "/gpn-api/digital-report/async/return/";

    public interface BIZ_LOG {
        interface OP_TYPE {
            String ENQUIRY_CANCEL = "Enquiry Cancel";
            String ENQUIRY_GENERATE_ORDER = "Enquiry Generate Order";
            String ENQUIRY_COMPLETE = "Enquiry Complete";

            String ORDER_CONFIRM = "Confirm Order";
            String ORDER_TESTING = "Order Testing";
            String ORDER_REPORTING = "Order Reporting";
            String ORDER_COMPLETED = "Order Completed";
            String ORDER_PENDING = "Order Pending";
            String ORDER_UN_PENDING = "Order UnPending";
            String ORDER_CLOSED = "Order Closed";
            String ORDER_CLOSED_FROM_AUTOCLOSEDORDER = "Order Closed(Auto)";
            String ORDER_CANCEL = "Order Cancel";
            String ORDER_PAID = "Order Paid";
        }
    }

    public interface EMAIL {
        interface PUBLIC_EMAIL{
            String NO_REPLY = "<EMAIL>";
            String PRE_ORDER = "<EMAIL>";
        }
        interface TEMPLATE_TYPE {
            String JOB = "Job";
            String SUBCONTRACT = "Subcontract";
            String PEND_ORDER = "PendOrder";
            String RETURN_SAMPLE_NOTIFICATION = "ReturnSampleNotification";
            String TRF = "TRF";
            String PRELIM_RESULT = "PrelimResult";
        }

        interface TEMPLATE_CODE {
            String RETURN_SAMPLE_NOTIFY = "GPO_SendReturnSampleNotification";
            String TRF_UPDATE = "TrfUpdate";
            String PRELIM_RESULT_CUSTOMER_CONFIRM = "PrelimResult_CustomerConfirm";
            String PRELIM_RESULT_ISSUE_REPORT = "PrelimResult_IssueReport";
        }
        interface TEMPLATE_Name {
            String TRF_UPDATE_ALERT = "TrfUpdateAlert";
        }
        interface SEND_BY {
            String SGS = "SGS";
        }
    }
    public interface TRF{
        interface UPDATE{
            interface SOURCE{
                String TRF_UPDATE = "TrfUpdated";
                String GENERATE_REPORT = "forceUpdate";
            }
        }
        interface ACTION{
            String UPDATE = "TrfUpdated";
        }

    }
    public static final String TW_SL = "TP SL";
    public static final String GPO_Support = "<EMAIL>";

    /**
     * DFF 配置的字段编码
     */
    public interface DFF {
        interface FILED {
            String ITEM_NO = "ItemNo";
            String OTHER_SAMPLE_INFORMATION = "OtherSampleInformation";
            String FIBER_WEIGHT = "FiberWeight";
            String END_USE = "EndUse1";
            String PO_NO = "PoNo";
            String CONSTRUCTION = "Construction";
            String PRODUCT_COLOR = "ProductColor";
            String THREAD_COUNT = "ThreadCount";
            String FIBER_COMPOSITION = "FiberComposition";
            String NO_OF_SAMPLE = "NoOfSample";
            String PRODUCT_DESCRIPTION = "ProductDescription";
            String COUNTRY_OF_DESTINATION = "CountryOfDestination";
        }

        String[] IGNORE_FIELD = {"id", "Refsampleid", "Orderid", "dffformid", "othersampleinformation", "externalSampleId",
                "headerid", "sampleid", "productitemno", "createdby", "createddate", "modifiedby", "modifieddate", "languageid", "cancelflag"};
    }
    public interface THYMELEAF_PATH {
        String VUE = "/public/vue/2.5.2/vue.min.js";
        String AXIOS = "/public/axios/0.17.1/axios.min.js";
        String ELEMENT = "/public/element-ui/2.15.6/index.js";
        String JQUERY = "/public/jquery/jquery-1.12.2.min.js";
        String THEME = "/public/element-ui/2.15.6/theme-chalk/index.css";
    }

    public interface SCI {
        interface REPORT_STATUS {
            String Confirmed = "Confirmed";
            String NoRequired = "NoRequired";
            String Pending = "Pending";
        }
        interface URL{
            String  CAN_REVISE_REPORT = "/customerbiz/api/v2/trf/canReviseReport";
        }
    }

    public interface BUSINESS_TYPE {
        String JOB = "Job";
        String REPORT = "Report";
    }

    public interface REPORT_AUTO_SOFTCOPY {
        interface DraftOrFinal {
            String DRAFT = "Draft";
            String FINAL = "Final";
            List<String> TYPE = Lists.newArrayList(DRAFT, FINAL);
        }
    }

    public interface DATA_DICT{
        interface SYS_KEY_GROUP{
            String DEFAULT_SAMPLE_STORAGE_PERIOD = "DefaultSampleStoragePeriod";
        }

    }
}
