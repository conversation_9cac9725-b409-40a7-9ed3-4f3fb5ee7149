package com.sgs.gpo.core.enums;

public enum OperationTypeEnums {
    PendReport("10", "PendReport"),
    UnPendReport("15", "UnPendReport"),
    CancelReport("11", "CancelReport"),
    RejectReport("12", "RejectReport"),
    ReworkReport("13", "ReworkReport"),
    PendOrder("20", "PendOrder"),
    CancelOrder("21", "CancelOrder"),
    DelayOrder("22", "DelayOrder"),
    PendJob("30", "PendJob"),
    UnPendJob("35", "PendJob"),
    CancelJob("31", "CancelJob"),
    PendSubcontract("40", "PendSubcontract"),
    UnPendSubcontract("45", "UnPendSubcontract"),
    CancelSubcontract("41", "CancelSubcontract"),
    PendTestLine("42", "PendTestLine"),
    UnPendTestLine("44", "UnPendTestLine"),
    UnPendOrder("23", "UnPendOrder"),
    ChangeOrderDueDate("24", "ChangeOrderDueDate"),
    CancelToDo("25", "CancelToDo"),
    PendToDo("26", "PendToDo"),
    UnPendToDo("27", "UnPendToDo"),
    Received("28", "ReceivedSample"),
    CompleteToDo("29", "CompleteToDo"),
    BatchClone("50", "BatchClone"),
    GenerateReport("51", "GenerateReport"),
    GeneratePrelimReport("52", "GeneratePrelimReport");
    private String status;
    private String value;

    OperationTypeEnums(String status, String value) {
        this.status = status;
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public String getStatus() {
        return status;
    }
}
