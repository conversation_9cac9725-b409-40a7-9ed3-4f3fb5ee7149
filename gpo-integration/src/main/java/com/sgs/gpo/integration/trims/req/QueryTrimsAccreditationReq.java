package com.sgs.gpo.integration.trims.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.Set;


@Data
public class QueryTrimsAccreditationReq extends BaseRequest {

    private String caller ="GPO";
    private Set<Integer> labIds;
    private Set<Integer> productLineIds;
    private Set<Integer> ppVersionIdentifiers;
    private Set<Integer> tlVersionIdentifiers;
    private String token="8345&2@#Gpo3";

}
