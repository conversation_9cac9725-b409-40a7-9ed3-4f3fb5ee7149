package com.sgs.gpo.integration.framework;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.facade.domain.req.BuParamReq;
import com.sgs.framework.facade.domain.rsp.BossLegalEntityRsp;
import com.sgs.framework.facade.domain.rsp.BuParamValueRsp;
import com.sgs.framework.facade.facadeService.IFrameworkFacadeService;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.security.utils.SecurityUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.HttpClientUtil;
import com.sgs.gpo.core.config.InterfaceConfig;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.integration.framework.req.*;
import com.sgs.gpo.integration.framework.rsp.*;
import com.sgs.gpo.integration.framework.rsp.FileInfoRsp;
import com.sgs.otsnotes.facade.model.common.DataLanguageRsp;
import com.sgs.otsnotes.facade.model.enums.UserInfoDefaultLanguageCodeEnums;
import com.sgs.otsnotes.facade.model.req.gpn.GpnLabListReq;
import com.sgs.otsnotes.facade.model.rsp.*;
import com.sgs.preorder.config.buparam.DiscountPersonConfig;
import com.sgs.preorder.facade.model.dto.holiday.HolidayDTO;
import com.sgs.preorder.facade.model.dto.order.NumberGenerateRuleDTO;
import com.sgs.preorder.facade.model.info.DataDictInfo;
import com.sgs.preorder.facade.model.req.DataDictReq;
import com.sgs.user.facade.domain.dimention.*;
import com.sgs.user.facade.facadeService.IUserFacadeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/14 19:02
 */
@Service
@Slf4j
public class FrameworkClient {

    final String ROWS = "rows";
    final String DATA = "data";
    final String ERROR_MESSAGE = "errorMessage";
    final String MESSAGE = "message";
    final String IS_SUCCESS = "isSuccess";
    final String SUCCESS = "success";
    final String FRAMEWORK_URL = "FrameWorkApi";

    @Autowired
    private InterfaceConfig interfaceConfig;
    @Autowired
    private IFrameworkFacadeService frameworkFacadeService;

    @Autowired
    private IUserFacadeService userFacadeService;


    /**
     * 查询数据字典项
     *
     * @param dataDictionaryReqs
     * @return
     */
    public BaseResponse<Map<String, List<DataDictionaryRsp>>> searchDictionary(List<DataDictionaryReq> dataDictionaryReqs) {
        if (Func.isEmpty(dataDictionaryReqs)) {
            return BaseResponse.newFailInstance(ResponseCode.PARAM_MISS);
        }
        try {
            log.info("searchDictionary from FrameWork params:{}", JSON.toJSONString(dataDictionaryReqs));
            String frameWorkApi = String.format("%s/FrameWorkApi/dataDictionary/api/v1/get/dataDictionaryBatch", interfaceConfig.getBaseUrl());
            String result = HttpClientUtil.postJson(frameWorkApi, dataDictionaryReqs);
            log.info("searchDictionary from FrameWork result:{}", result);
            if (Func.isEmpty(result)) {
                log.error("searchDictionary from FrameWork result:{}", "null");
                return BaseResponse.newFailInstance("返回结果为空！");
            }
            Boolean isSuccess = true;
            JSONObject jsonObject = JSONObject.parseObject(result);
            if (jsonObject.containsKey("success")) {
                isSuccess = jsonObject.getBoolean("success");
            }
            if (!isSuccess) {
                log.error("searchDictionary from FrameWork result:{}", "null");
                return BaseResponse.newFailInstance(jsonObject.getString("errorMessage"));
            }
            Map<String, List<DataDictionaryRsp>> map = JSON.parseObject(result, new TypeReference<Map<String, List<DataDictionaryRsp>>>() {
            }, new Feature[0]);
            return BaseResponse.newSuccessInstance(map);
        } catch (Exception ex) {
            log.error("FrameWorkClient.getDataDictList 信息异常：{}.", ex);
            return BaseResponse.newFailInstance(ex.getMessage());
        }
    }

    /**
     * 查询实验室列表
     *
     * @param labReq
     * @return
     */
    public BaseResponse<List<LabRsp>> searchLabList(LabReq labReq) {
        List<LabRsp> labRspList = new ArrayList<>();
        if (Func.isEmpty(labReq)) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        Map<String, Object> trimsParams = Func.toMap(labReq);
        try {
            log.debug("call queryLabList req:{}" + trimsParams);
            String frameWorkApi = String.format("%s/FrameWorkApi/trims/api/v1/queryLabList", interfaceConfig.getBaseUrl());
            String jsonStr = HttpClientUtil.post(frameWorkApi, trimsParams);
            log.debug("call queryLabList rsp:{}" + jsonStr);
            if (StringUtils.isEmpty(jsonStr)) {
                return BaseResponse.newFailInstance("返回结果为空！");
            }
            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            if (jsonObject == null || !jsonObject.getBooleanValue("isSuccess")) {
                return BaseResponse.newFailInstance("返回结果为空！");
            }
            String labListJSON = jsonObject.getString("rows");
            if (Func.isEmpty(labListJSON)) {
                return BaseResponse.newFailInstance("返回结果为空！");
            }
            labRspList = JSONObject.parseArray(jsonObject.getString("rows"), LabRsp.class);
        } catch (Exception ex) {
            log.error("FrameWorkClient.queryLabList 信息异常：{}.", ex);
        }
        return BaseResponse.newSuccessInstance(labRspList);
    }

    /**
     * 查询业务线列表
     *
     * @param buReq
     * @return
     */
    public BaseResponse<List<BURsp>> searchBUList(BUReq buReq) {
        List<BURsp> buRspList = new ArrayList<>();
        try {
            Map<String, Object> params = new HashMap<>();
            if (Func.isNotEmpty(buReq)) {
                params = Func.toMap(buReq);
            }
            String frameWorkApi = String.format("%s/%s/trims/api/v1/queryBuList", interfaceConfig.getBaseUrl(), FRAMEWORK_URL);
            String jsonStr = HttpClientUtil.post(frameWorkApi, params);
            log.debug("call queryBUList rsp:{}" + jsonStr);
            if (StringUtils.isEmpty(jsonStr)) {
                return BaseResponse.newFailInstance("返回结果为空！");
            }
            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            if (!jsonObject.getBooleanValue(IS_SUCCESS)) {
                return BaseResponse.newFailInstance(jsonObject.getString(ERROR_MESSAGE));
            }
            String labListJSON = jsonObject.getString(ROWS);
            if (Func.isEmpty(labListJSON)) {
                return BaseResponse.newFailInstance("返回结果为空！");
            }
            buRspList = JSONObject.parseArray(jsonObject.getString(ROWS), BURsp.class);
        } catch (Exception ex) {
            log.error("FrameWorkClient.queryBUList 信息异常：{}.", ex);
        }
        return BaseResponse.newSuccessInstance(buRspList);
    }

    /**
     * 查询国家列表
     *
     * @param countryReq
     * @return
     */
    public BaseResponse<List<CountryRsp>> searchCountryList(CountryReq countryReq) {
        List<CountryRsp> buRspList = new ArrayList<>();
        try {
            String frameWorkApi = String.format("%s/%s/trims/api/queryTrimsCountryByTools", interfaceConfig.getBaseUrl(), FRAMEWORK_URL);
            String jsonStr = HttpClientUtil.post(frameWorkApi, new HashMap<>());
            log.debug("call queryCountryList rsp:{}" + jsonStr);
            if (StringUtils.isEmpty(jsonStr)) {
                return BaseResponse.newFailInstance("返回结果为空！");
            }
            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            if (!jsonObject.getBooleanValue(SUCCESS)) {
                return BaseResponse.newFailInstance(jsonObject.getString(MESSAGE));
            }
            String labListJSON = jsonObject.getString(ROWS);
            if (Func.isEmpty(labListJSON)) {
                return BaseResponse.newFailInstance("返回结果为空！");
            }
            buRspList = JSONObject.parseArray(jsonObject.getString(DATA), CountryRsp.class);
        } catch (Exception ex) {
            log.error("FrameWorkClient.queryCountryList 信息异常：{}.", ex);
        }
        return BaseResponse.newSuccessInstance(buRspList);
    }

    /**
     * 查询BU配置
     *
     * @param buParamReq
     * @return
     */
    public BaseResponse<List<BuParamValueRsp>> searchBUParam(BuParamReq buParamReq) {
        List<BuParamValueRsp> list = frameworkFacadeService.getBuParam(buParamReq).getData();
        return BaseResponse.newSuccessInstance(list);
    }

    /**
     * 批量生产单号
     *
     * @param locationId
     * @param numberRuleCode
     * @param postfix
     * @param prefix
     * @param buId
     * @param transactionNo
     * @param quantity
     * @return
     */
    public BaseResponse<List<String>> getNumberBatch(Integer locationId, String numberRuleCode, String postfix, String prefix, Integer buId, String transactionNo, Integer quantity) {
        Map<String, Object> param = Maps.newHashMap();
        HashMap<String, String> map = Maps.newHashMap();
        param.put("buId", buId);
        param.put("locationId", locationId);
        param.put("systemId", SgsSystem.GPO.getSgsSystemId() + "");
        param.put("numberRuleCode", numberRuleCode);
        if (Func.isEmpty(quantity)) {
            quantity = 1;
        }
        param.put("quantity", quantity);
        map.put("prefix", prefix);
        map.put("postfix", postfix);
        map.put("transactionNo", transactionNo);
        param.put("numberSegment", map);
        log.info("批量获取 No 请求数据：{}", JSON.toJSONString(param));
        String frameWorkApi = String.format("%s/FrameWorkApi/numberRule/api/v1/get/batch/number", interfaceConfig.getBaseUrl());
        List<String> noList = Lists.newArrayList();
        try {
            String resultJson = HttpClientUtil.postJson(frameWorkApi, JSON.toJSONString(param));
            log.info("批量获取 No 返回数据：{}-{}", transactionNo, resultJson);
            if (Func.isNotEmpty(resultJson)) {
                String noListStr = (JSONObject.parseObject(resultJson)).getString("data");
                Integer status = (JSONObject.parseObject(resultJson)).getInteger("status");
                if (Func.isNotEmpty(status) && ResponseCode.UNKNOWN.getCode() == status.intValue()) {
                    return BaseResponse.newFailInstance((JSONObject.parseObject(resultJson)).getString("message"));
                } else if (Func.isNotEmpty(noListStr)) {
                    noList = JSONObject.parseArray(noListStr, String.class).stream().filter(item -> Func.isNotEmpty(item)).collect(Collectors.toList());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return BaseResponse.newFailInstance(e.getMessage());
        }
        return BaseResponse.newSuccessInstance(noList);
    }

    public BuParamValueRsp getBuParam(BuParamReq buParamReq) {
        List<BuParamValueRsp> buParamValueRspList = this.getBuParams(buParamReq);
        return Func.isNotEmpty(buParamValueRspList) ? buParamValueRspList.get(0) : null;
    }

    public List<BuParamValueRsp> getBuParams(BuParamReq buParamReq) {
        String frameWorkApi = String.format("%s/FrameWorkApi/busetting/get", interfaceConfig.getBaseUrl());
        String paramResult = null;
        try {
            paramResult = HttpClientUtil.postJson(frameWorkApi, JSON.toJSONString(buParamReq));
        } catch (Exception e) {
            return null;
        }
        log.info("getBuParams Result: " + paramResult == null ? "" : Func.toJson(paramResult));
        List<BuParamValueRsp> paramValueDTOS = null;
        try {
            CSResult<BuParamValueRsp> buParamValueDTOCSResult = JSON.parseObject(paramResult, new TypeReference<CSResult<BuParamValueRsp>>() {
            });
            paramValueDTOS = buParamValueDTOCSResult.getRows();
        } catch (Exception e) {
            log.error("getBuParams error:" + e.getMessage(), e);
            return null;
        }
        return paramValueDTOS;
    }

    public String getPrimaryLanguageCode(String productLineCode) {
        BuParamReq buParamReq = new BuParamReq();
        buParamReq.setProductLineCode(productLineCode);
        buParamReq.setParamCode("DataLanguage");
        buParamReq.setGroupCode("Display");
        BuParamValueRsp buParam = this.getBuParam(buParamReq);
        if (Func.isEmpty(buParam)) {
            return UserInfoDefaultLanguageCodeEnums.en_us.getCode();
        } else {
            String paramValue = buParam.getParamValue();
            try {
                DataLanguageRsp dataLanguageRsp = JSONObject.parseObject(paramValue, DataLanguageRsp.class);
                if (Func.isEmpty(dataLanguageRsp) || Func.isEmpty(dataLanguageRsp.getLanguages())) {
                    log.info("getPrimaryLanguageCode return empty");
                    return UserInfoDefaultLanguageCodeEnums.en_us.getCode();
                } else {
                    List<DataLanguageRsp.Languages> languages = dataLanguageRsp.getLanguages();
                    if (Func.isNotEmpty(languages)) {
                        DataLanguageRsp.Languages primaryLanguage = languages.stream().filter(DataLanguageRsp.Languages::getPrimary).findFirst().orElse(null);
                        if (Func.isNotEmpty(primaryLanguage)) {
                            return primaryLanguage.getLanguageCode();
                        } else {
                            return UserInfoDefaultLanguageCodeEnums.en_us.getCode();
                        }
                    } else {
                        return UserInfoDefaultLanguageCodeEnums.en_us.getCode();
                    }
                }
            } catch (Exception e) {
                log.error("parse byBuParam Json error:{}", e);
                return UserInfoDefaultLanguageCodeEnums.en_us.getCode();
            }
        }
    }

    public AccreditationRsp getAccreditationInfo(String labCode, String fileCloudKey) {
        String[] labCodes = labCode.split(" ");
        if (labCodes.length < 2) {
            return null;
        }
        labCode = labCodes[0] + "%20" + labCodes[1];
        String url = interfaceConfig.getBaseUrl() + "/FrameWorkApi/certificateImage/api/v1/queryCertificateImageByLabCode/" + labCode;
        if (Func.isNotEmpty(fileCloudKey)) {
            url += "?fileCloudKey=";
            String fileCloudKeyFinal = fileCloudKey.replace(" ", "%20");
            url += fileCloudKeyFinal;
        }
        String jsonStr = null;
        try {
            jsonStr = HttpClientUtil.sendGet(url, new HashMap<>());
        } catch (Exception e) {
            log.info("查询AccreditationInfo：{}", e.getMessage());
            e.printStackTrace();
        }
        AccreditationRsp info = JSONObject.parseObject(jsonStr, AccreditationRsp.class);
        return info;
    }

    public String getLabClaimer(String labCode, Integer languageId) {
        String url;

        if (LanguageType.check(languageId, LanguageType.English)) {
            url = interfaceConfig.getBaseUrl() + "/FrameWorkApi/dataDictionary/api/v1/get/dataDictionary?sysKeyGroup=LabDisclaimer&systemID=" + SgsSystem.GPO.getSgsSystemId();
        } else if (LanguageType.check(languageId, LanguageType.Chinese)) {
            url = interfaceConfig.getBaseUrl() + "/FrameWorkApi/dataDictionary/api/v1/get/dataDictionary?sysKeyGroup=LabDisclaimerForCN&systemID=" + SgsSystem.GPO.getSgsSystemId();
        } else {
            return null;
        }
        String jsonStr = null;
        try {
            log.info("查询报告页脚图片：{}", url);
            jsonStr = HttpClientUtil.sendGet(url, new HashMap<>());
            //log.info("查询报告页脚图片result：{}", jsonStr);
        } catch (Exception e) {
            log.info("查询报告页脚图片errorMessage：{}", e.getMessage());
            e.printStackTrace();
        }

        if (StringUtils.isEmpty(jsonStr)) {
            return null;
        }
        JSONArray jsonArray = JSONArray.parseArray(jsonStr);
        if (jsonArray == null || jsonArray.isEmpty()) {
            return null;
        }
        String sysKey = "";
        for (int jsonIndex = 0; jsonIndex < jsonArray.size(); jsonIndex++) {
            JSONObject json = jsonArray.getJSONObject(jsonIndex);
            sysKey = json.getString("sysKey");
            if (Func.equals(sysKey, labCode)) {
                return json.getString("sysValue");
            }
        }
        return null;
    }

    public LabClaimer getLabGBClaimer(Integer languageId, Integer buId, Integer locationID) {
        LabClaimer labClaimer = new LabClaimer();
        String languageCode;

        if (LanguageType.check(languageId, LanguageType.English)) {
            languageCode = "EN";
        } else if (LanguageType.check(languageId, LanguageType.Chinese)) {
            languageCode = "CN";
        } else {
            return labClaimer;
        }
        String url = interfaceConfig.getBaseUrl() + "/FrameWorkApi/dataDictionary/api/v1/get/getGBReportImage?bUID=" + buId + "&systemID=" + SgsSystem.GPO.getSgsSystemId() + "&locationID=" + locationID + "&languageCode=" + languageCode;
        String jsonStr = null;
        try {
            log.info("查询报告页脚图片[getLabGBClaimer]：{}", url);
            jsonStr = HttpClientUtil.sendGet(url, new HashMap<>());
            //log.info("查询报告页脚图片[getLabGBClaimer]result：{}", jsonStr);
        } catch (Exception e) {
            log.info("查询报告页脚图片[getLabGBClaimer]errorMessage：{}", e.getMessage());
            e.printStackTrace();
        }

        if (StringUtils.isEmpty(jsonStr)) {
            return labClaimer;
        }
        JSONObject jsonObject = JSONObject.parseObject(jsonStr);
        if (Func.isNotEmpty(jsonObject) && Func.isNotEmpty(jsonObject.get("data"))) {
            String gBReportImageStr = JSONObject.parseObject(jsonObject.getString("data")).getString("gBReportImage");
            if (Func.isEmpty(gBReportImageStr)) {
                return labClaimer;
            }
            JSONArray jsonArray = JSONArray.parseArray(gBReportImageStr);
            if (jsonArray == null || jsonArray.isEmpty()) {
                return labClaimer;
            }
            String sysKey = "";
            for (int jsonIndex = 0; jsonIndex < jsonArray.size(); jsonIndex++) {
                JSONObject json = jsonArray.getJSONObject(jsonIndex);
                sysKey = json.getString("sysKey");
                if (Func.equals(sysKey, "GB company chop")) {
                    labClaimer.setGbCompanyChop(json.getString("sysValue"));
                }
                if (Func.equals(sysKey, "GB company footer")) {
                    labClaimer.setLabGBClaimer(json.getString("sysValue"));
                }
            }
        }
        return labClaimer;
    }

    public BaseResponse queryLabByLabCode(String labCode) {
        BaseResponse response = new BaseResponse();
        try {
            HashMap<String, Object> reqParams = new HashMap<>();
            reqParams.put("labCode", labCode);
            reqParams.put("isAccurateQuery", 1);
            String frameWorkApi = String.format("%s/FrameWorkApi/trims/api/v1/queryLabList", interfaceConfig.getBaseUrl());
            String jsonStr = HttpClientUtil.post(frameWorkApi, reqParams);
            log.info("call queryLabByLabCode :{}" + jsonStr);
            if (StringUtils.isEmpty(jsonStr)) {
                response.setStatus(400);
                response.setMessage("参数返回值为空！");
                return response;
            }
            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            if (jsonObject == null || !jsonObject.getBooleanValue("isSuccess")) {
                response.setStatus(400);
                response.setMessage("字符串转换错误");
                return response;
            }
            String rowsStr = jsonObject.getString("rows");
            List<LabRsp> labDTOS = JSON.parseArray(rowsStr, LabRsp.class);
            if (labDTOS == null || labDTOS.isEmpty()) {
                return response;
            }
            response.setData(labDTOS.stream().filter(e -> e.getLaboratoryCode().equals(labCode)).findFirst().orElse(null));
            return response;
        } catch (Exception ex) {
            log.error("FrameWorkClient.queryLabList 信息异常：{}.", ex);
        }
        return response;
    }

    public BigDecimal getCurrencyExchangeRate(String fromCurrency, String toCurrency, String conversionDate) {
        Map<String, Object> params = new HashMap<>();
        params.put("fromCurrency", fromCurrency);
        params.put("toCurrency", toCurrency);
        params.put("conversionDate", conversionDate);
        try {
            String getCurrencyExchangeRateUrl = String.format("%s/FrameWorkApi/currencyExchangeRate/api/v1/get/list", interfaceConfig.getBaseUrl());
            String jsonStr = HttpClientUtil.post(getCurrencyExchangeRateUrl, params);
            JSONArray jsonArray = JSONArray.parseArray(jsonStr);
            if (Func.isEmpty(jsonArray) || jsonArray.size() == 0) {
                throw new BizException("没有获取到汇率");
            }
            return (BigDecimal) jsonArray.getJSONObject(0).get("conversionRate");
        } catch (Exception e) {
            log.error("获取汇率异常:{}", e);
            throw new RuntimeException(e.getMessage());
        }
    }

    public UserLabRsp getUserLabBuInfo(String token) {
        UserDimentionDTO userDimention = null;
        try {
            userDimention = userFacadeService.getUserDimention(token);
        } catch (Exception ex) {
            log.error("UserClient.getUserDimention 请求异常：", ex);
        }
        if (userDimention == null) {
            return null;
        }
        UserLabRsp lab = new UserLabRsp();
        Laboratory userLab = userDimention.getLaboratory();
        if (userLab != null) {
            lab.setLabId(userLab.getLabId().longValue());
            lab.setLabCode(userLab.getLabCode());
            lab.setLabName(userLab.getLabName());
            lab.setLabAddress(userLab.getLabAddress());
            lab.setOtherCode(userLab.getOtherCode());
        }
        Location location = userDimention.getLocation();
        if (location != null) {
            lab.setLocationId(location.getLocationId());
            lab.setLocationCode(location.getLocationCode());
            lab.setLocationName(location.getLocationName());
        }
        ProductLine productLine = userDimention.getProductLine();
        if (productLine != null) {
            lab.setProductLineId(productLine.getProductLineID());
            lab.setProductLineCode(productLine.getProductLineCode());
            lab.setProductLineName(productLine.getProductLineName());
            lab.setShortCode(productLine.getShortCode());
        }
        UserTeam team = userDimention.getTeam();
        if (team != null) {
            lab.setTeamCode(team.getTeamCode());
            lab.setTeamName(team.getTeamName());

            lab.setProductTypeCode(team.getProductTypeCode());
            if (!StringUtils.isBlank(team.getProductTypePostfix())) {
                lab.setPostfix(team.getProductTypePostfix().split(";"));
            }
        }
        LegalEntity legalEntity = userDimention.getBossLegalEntity();
        if (legalEntity != null) {
            lab.setLegalEntityCode(legalEntity.getLegalEntityCode());
            //lab.setLegalEntityName(legalEntity.getLegalEntityName());
            lab.setOrganizationID(legalEntity.getOrganizationID());
            lab.setOrganizationName(legalEntity.getOrganizationName());
            //lab.setCountryCode(legalEntity.getCountryCode());
        }
        return lab;
    }

    public BaseResponse<String> getPaidUpNo(Integer buId, Integer locationId, String labCode) {
        if (Func.isEmpty(buId) || Func.isEmpty(locationId) || Func.isEmpty(labCode)) {
            return BaseResponse.newFailInstance("getPaidUpNo, buId or locationId or labCode is null");
        }
        NumberGenerateRuleDTO objNumberGenerateRuleDTO = new NumberGenerateRuleDTO();
        objNumberGenerateRuleDTO.setBuId(buId);
        objNumberGenerateRuleDTO.setLocationId(locationId);
        objNumberGenerateRuleDTO.setSystemId(SgsSystem.GPO.getSgsSystemId());
        objNumberGenerateRuleDTO.setNumberRuleCode("GPOPaidupNo");
        Map numberSegment = new HashMap();
        numberSegment.put("prefix", labCode);
        numberSegment.put("postfix", Constants.PAID_UP_SUFFIX);
        objNumberGenerateRuleDTO.setNumberSegment(numberSegment);
        String frameWorkApi = String.format("%s/FrameWorkApi/numberRule/api/v1/get/number", interfaceConfig.getBaseUrl());
        String orderNumber = null;
        try {
            orderNumber = HttpClientUtil.postJson(frameWorkApi, objNumberGenerateRuleDTO);
        } catch (Exception e) {
            return BaseResponse.newFailInstance("Paid Up NO ,Paid Up NO is error");
        }
        return BaseResponse.newSuccessInstance(orderNumber);
    }

    public BaseResponse<String> generateNumber(GenerateNumberReq generateNumberReq) {
        if (Func.isEmpty(generateNumberReq) || Func.isEmpty(generateNumberReq.getBuId()) || Func.isEmpty(generateNumberReq.getLocationId())) {
            return BaseResponse.newFailInstance("common.param.miss", new Object[]{"buId or locationId"});
        }
        NumberGenerateRuleDTO numberGenerateRule = new NumberGenerateRuleDTO();
        numberGenerateRule.setBuId(generateNumberReq.getBuId());
        numberGenerateRule.setLocationId(generateNumberReq.getLocationId());
        numberGenerateRule.setSystemId(SgsSystem.GPO.getSgsSystemId());
        numberGenerateRule.setNumberRuleCode(generateNumberReq.getNumberRuleCode());
        Map numberSegment = new HashMap();
        numberSegment.put("prefix", Func.toStr(generateNumberReq.getPrefix()));
        numberSegment.put("postfix", Func.toStr(generateNumberReq.getPostfix()));
        numberGenerateRule.setNumberSegment(numberSegment);
        String frameWorkApi = String.format("%s/FrameWorkApi/numberRule/api/v1/get/number", interfaceConfig.getBaseUrl());
        String enquiryNumber;
        try {
            enquiryNumber = HttpClientUtil.postJson(frameWorkApi, numberGenerateRule);
        } catch (Exception e) {
            return BaseResponse.newFailInstance("Generate Order NO is error");
        }
        return BaseResponse.newSuccessInstance(enquiryNumber);
    }


    /**
     * generate report 获取公司名称
     *
     * @param param
     * @return
     */
    public BossLegalEntityRsp queryBossLegalEntity(Map<String, String> param) {
        BossLegalEntityRsp bossLegalEntityRsp = new BossLegalEntityRsp();
        String url = String.format("%s/FrameWorkApi/bossLegalEntity/api/v1/queryBossLegalEntity", interfaceConfig.getBaseUrl());
        String result = null;
        try {
//            log.info("获取公司名称：{}", url);
//            log.info("获取公司名称：{}", JSON.toJSONString(param));
//            if(Func.isNotEmpty(param)){
//                String urlWithParams = param.entrySet().stream()
//                        .map(entry -> entry.getKey() + "=" + entry.getValue())
//                        .collect(Collectors.joining("&"));
//                if(Func.isNotEmpty(urlWithParams)){
//                    url = url.concat("?").concat(urlWithParams);
//                }
//            }
            result = HttpClientUtil.postJson(url,JSON.toJSONString(param));
            //log.info("获取公司名称result：{}", result);
        } catch (Exception e) {
            log.info("获取公司名称error Massage：{}", e.getMessage());
            // TODO Auto-generated catch block
            log.error(e.getMessage(), e);
        }
        if (Func.isEmpty(result)) {
            return bossLegalEntityRsp;
        }
        JSONObject jsonObject = JSONObject.parseObject(result);
        JSONArray jsonArray = jsonObject.getJSONArray("rows");
        if (Func.isEmpty(jsonArray)) {
            return bossLegalEntityRsp;
        }

        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject row = jsonArray.getJSONObject(i);
            String locationCode = row.getString("locationCode");
            Integer defaultLegalEntityIndicator = row.getInteger("defaultLegalEntityIndicator");
            if (Func.equalsSafe(defaultLegalEntityIndicator,1) && Func.isNotEmpty(locationCode) && Func.equals(locationCode, param.get("locationCode"))) {
                bossLegalEntityRsp.setBranchNameEN(row.getString("branchNameEN"));
                bossLegalEntityRsp.setBranchNameCN(row.getString("branchNameCN"));
            }
        }
        return bossLegalEntityRsp;
    }


    public List<UserSignatureRsp> queryUserSignature(UserSignatureReq request) {
        String url = String.format("%s/UserManagementApi/employee/querySignatureEmployeeInfo", interfaceConfig.getBaseUrl());
        try {
            return HttpClientUtil.sendPost(url, request, new TypeReference<List<UserSignatureRsp>>() {
            });
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            log.error("queryUserSinature 信息异常：{}", e);
        }
        return null;
    }

    /**
     * 获取货币数据
     *
     * @return
     */
    public BaseResponse<String> getCurrency() {
        String result = null;
        String url = String.format("%s/FrameWorkApi/dataDictionary/api/v1/get/dataDictionary?bUID=1&systemID=0&SysKeyGroup=currency", interfaceConfig.getBaseUrl());
        try {
            result = HttpClientUtil.sendGet(url, new HashMap<>());
        } catch (Exception e) {
            log.info(e.getMessage());
            return BaseResponse.newFailInstance("get Currency fail");
        }
        log.info(url);
        log.info(result);
        return BaseResponse.newSuccessInstance(result);
    }

    /**
     * 获取支付方式
     *
     * @return
     */
    public BaseResponse<String> getPaymentMethod() {
        String result = null;
        String url = String.format("%s/FrameWorkApi/dataDictionary/api/v1/get/dataDictionary?sysKeyGroup=PaymentMethod&systemID=15", interfaceConfig.getBaseUrl());
        try {
            result = HttpClientUtil.sendGet(url, new HashMap<>());
        } catch (Exception e) {
            log.info(e.getMessage());
            return BaseResponse.newFailInstance("get Payment Method  fail");
        }
        log.info(url);
        log.info(result);
        return BaseResponse.newSuccessInstance(result);
    }

    public boolean isVnLab(String labCode) {
        boolean isVnLab = false;
        if (Func.isEmpty(labCode)) {
            labCode = SecurityUtil.getLabCode();
        }
        if (Func.isNotEmpty(labCode)) {
            BaseResponse<LabRsp> labRsp = this.queryLabByLabCode(labCode);
            if (labRsp.isSuccess() && Func.isNotEmpty(labRsp.getData())) {
                LabRsp lab = labRsp.getData();
                if (Func.isNotEmpty(lab) && Func.equalsSafe(lab.getCountryCode2(), Constants.COUNTRY_CODE.VN)) {
                    isVnLab = true;
                }
            }
        }
        return isVnLab;
    }


    public BaseResponse<List<DownLoadFileRsp>> downloadByCloudIDList(DownloadFileReq downloadFileReq) {
        List<DownLoadFileRsp> resultList = new ArrayList<>();
        if (Func.isEmpty(downloadFileReq)) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        if (Func.isEmpty(downloadFileReq.getCloudIdList())) {
            return BaseResponse.newFailInstance("common.param.miss", new Object[]{"CloudIdList"});
        }
        Map<String, Object> params = Func.toMap(downloadFileReq);
        try {
            String frameWorkApi = String.format("%s/FrameWorkApi/file/downloadByCloudIDList", interfaceConfig.getBaseUrl());
            String result = HttpClientUtil.postJson(frameWorkApi, downloadFileReq);
            if (StringUtils.isEmpty(result)) {
                return BaseResponse.newFailInstance("返回结果为空！");
            }
            JSONObject jsonObject = JSONObject.parseObject(result);
            if (jsonObject == null || !jsonObject.getBooleanValue("isSuccess")) {
                return BaseResponse.newFailInstance("返回结果为空！");
            }
            String listJSON = jsonObject.getString("rows");
            if (Func.isEmpty(listJSON)) {
                return BaseResponse.newFailInstance("返回结果为空！");
            }
            resultList = JSONObject.parseArray(jsonObject.getString("rows"), DownLoadFileRsp.class);
        } catch (Exception ex) {
            log.error("FrameWorkClient.downloadByCloudIDList 信息异常：{}.", ex);
        }
        return BaseResponse.newSuccessInstance(resultList);
    }

    public PageInfoList getLabInfo(GpnLabListReq gpnLabListReq) {
        String url = interfaceConfig.getBaseUrl() + "/FrameWorkApi/trims/api/v1/queryLabListForPage";
        Map<String, Object> params = new HashMap<>();
        params.put("page", gpnLabListReq.getPage());
        params.put("rows", gpnLabListReq.getRows());
        params.put("labCode", gpnLabListReq.getLabCode());
        params.put("labName", gpnLabListReq.getLabName());
        String data = HttpClientUtil.post(url, params);
        PageInfoList info = JSONObject.parseObject(data, PageInfoList.class);
        return info;
    }

    public LabInfo getLabInfoByLabCode(String labCode) {
        GpnLabListReq gpnLabListReq = new GpnLabListReq();
        gpnLabListReq.setPage("1");
        gpnLabListReq.setRows("1");
        gpnLabListReq.setLabCode(labCode);
        PageInfoList pageInfoList = this.getLabInfo(gpnLabListReq);
        if (pageInfoList == null || Func.isEmpty(pageInfoList.getRows())) {
            log.error("找不到lab信息,labCode:" + labCode);
            throw new BizException("找不到lab信息,labCode:" + labCode);
        }
        return pageInfoList.getRows().get(0);
    }

    public FileInfoRsp copyFile(FileCopyReq fileCopyReq) {
        Map<String, Object> params = new HashMap<>();
        FileInfoRsp attachmentRsp = new FileInfoRsp();

        if (Func.isNotEmpty(fileCopyReq.getFileId())) {
            params.put("id", fileCopyReq.getFileId());
        }
        if (Func.isNotEmpty(fileCopyReq.getCloudId())) {
            params.put("cloudID", fileCopyReq.getCloudId());
        }
        if (Func.isNotEmpty(fileCopyReq.getObjectId())) {
            params.put("generalOrderID", fileCopyReq.getObjectId());
            params.put("objectID", fileCopyReq.getObjectId());
        }
        // 调http接口
        if(Func.isEmpty(params)){
            return attachmentRsp;
        }
        String result;
        try {
            String url = String.format("%s/FrameWorkApi/file/copyTbfile", interfaceConfig.getBaseUrl());
            result = HttpClientUtil.post(url, params);
            log.info("重新获取cloudId===============>>>>>>>>>>>>>>>>>>>>>>>>>>" + result);
            String attachmentResult = JSONObject.parseObject(result).getString("result");
            if (Func.isEmpty(attachmentResult)) {
                return attachmentRsp;
            }
            attachmentRsp = JSON.parseObject(attachmentResult, FileInfoRsp.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return attachmentRsp;
    }
    /**
     * 用户名获取邮箱
     * @param regionAccount
     * @param labCode
     * @return
     */
    public String getEmailByRegionAccount(String regionAccount,String labCode){
        String email = null;
        Map<String, Object> params = Maps.newHashMap();
        params.put("page",1);
        params.put("rows",10);
        params.put("regionAccount",regionAccount);
//        params.put("labCode",labCode);
        params.put("accurateFlag",1);
        String url = String.format("%s/UserManagementApi/employee/queryUserInfoList", interfaceConfig.getBaseUrl());
        String result = HttpClientUtil.post(url, params);
        log.info("getEmailByRegionAccount->/employee/queryUserInfoList返回值：{}",result);
        if(Func.isNotEmpty(result)){
            JSONObject jsonObject = JSON.parseObject(result);
            JSONArray jsonArray = JSONArray.parseArray(jsonObject.get("list").toString());
            if (Func.isNotEmpty(jsonArray)){
                JSONObject json = jsonArray.getJSONObject(0);
                email = json.getString("email");
            }
        }
        return email;
    }
    public DiscountPersonConfig getDiscountPersonConfig(String productLineCode, String locationCode){
        BuParamReq buParamReq = new BuParamReq();
        buParamReq.setProductLineCode(productLineCode);
        buParamReq.setLocationCode(locationCode);
        buParamReq.setGroupCode(Constants.BU_PARAM.QUOTATION.GROUP);
        buParamReq.setParamCode(Constants.BU_PARAM.QUOTATION.DISCOUNT_PERSION.CODE);
        List<BuParamValueRsp> buParamValueDTOS = this.getBuParams(buParamReq);
        if(Func.isNotEmpty(buParamValueDTOS)){
            BuParamValueRsp buParamValue = buParamValueDTOS.get(0);
            DiscountPersonConfig discountPersonConfig = JSON.parseObject(buParamValue.getParamValue(),new TypeReference<DiscountPersonConfig>(){});
            if(Func.isNotEmpty(discountPersonConfig)) {
                discountPersonConfig.setBuParamValue(buParamValue);
            }
            if(Func.isEmpty(discountPersonConfig.getCustomerRole())){
                discountPersonConfig.setCustomerRole(Constants.BU_PARAM.QUOTATION.DISCOUNT_PERSION.CUSTOMER_ROLE.DEFAULT);
            }
            return discountPersonConfig;
        }else{
            return null;
        }
    }
    public List<DataDictInfo> getDataDictList(String  keyGroup,Integer buId){
        try {
            HashMap<String, String> reqParams = new HashMap<>();
            reqParams.put("bUID", Func.toStr(buId));
            reqParams.put("systemID", String.valueOf(SgsSystem.GPO.getSgsSystemId()));
            reqParams.put("SysKeyGroup", keyGroup);

            String frameWorkApi = String.format("%s/FrameWorkApi/dataDictionary/api/v1/get/dataDictionary",
                    interfaceConfig.getBaseUrl());
            String jsonStr = HttpClientUtil.sendGet(frameWorkApi,reqParams);
            if (StringUtils.isEmpty(jsonStr)){
                return null;
            }
            return JSONArray.parseArray(jsonStr, DataDictInfo.class);
        }catch (Exception ex){
            log.error("FrameWorkClient.getDataDictList 信息异常：{}.", ex);
        }
        return null;
    }

    /**
     *
     * @param dataDictReq
     * @return
     */
    public List<DataDictInfo> getDataDictList(DataDictReq dataDictReq){
        try {
            HashMap<String, String> reqParams = new HashMap<>();
            reqParams.put("SysKeyGroup", dataDictReq.getSysKeyGroup());
            if (Func.isNotEmpty(dataDictReq.getSystemId())){
                reqParams.put("systemID", Func.toStr(dataDictReq.getSystemId()));
            }
            if(Func.isNotEmpty(dataDictReq.getBuId())){
                reqParams.put("bUID", Func.toStr(dataDictReq.getBuId()));
            }
            if(Func.isNotEmpty(dataDictReq.getSysKey())){
                reqParams.put("SysKey", dataDictReq.getSysKey());
            }
            String frameWorkApi = String.format("%s/FrameWorkApi/dataDictionary/api/v1/get/dataDictionary", interfaceConfig.getBaseUrl());
            String jsonStr = HttpClientUtil.sendGet(frameWorkApi,reqParams);
            if (StringUtils.isEmpty(jsonStr)){
                return null;
            }
            return JSONArray.parseArray(jsonStr, DataDictInfo.class);
        }catch (Exception ex){
            log.error("FrameWorkClient.getDataDictList 信息异常：{}.", ex);
        }
        return null;
    }

    public QueryBuCodeRsp queryBuCodeBySubcontractNo(String subcontractNo) {
        String url = String.format("%s%s", interfaceConfig.getBaseUrl(), "/FrameWorkApi/numberRule/api/v1/get/objectNoParse");
        QueryBuCodeReq queryBuCodeReq = new QueryBuCodeReq();
        queryBuCodeReq.setObjectNo(subcontractNo);
        queryBuCodeReq.setRuleCode("SubcontractNo");
        queryBuCodeReq.setSystemId("3");
        try{
            BaseResponse result = HttpClientUtil.doPost(url, queryBuCodeReq, BaseResponse.class);
            if (result.getStatus() != com.sgs.otsnotes.facade.model.common.ResponseCode.SUCCESS.getCode()) {
                log.error("FrameWorkClient.queryBuCodeBySubcontractNo 信息失败：{}", result.getMessage());
                return null;
            }
            QueryBuCodeRsp queryBuCodeRsp = JSONObject.parseObject(result.getData().toString(), QueryBuCodeRsp.class);

            return queryBuCodeRsp;
        }catch (Exception e){
            log.error("FrameWorkClient.queryBuCodeBySubcontractNo 信息异常：{}", e);
        }
        return null;
    }

    public BaseResponse<String> batchDownloadByCloudIds(List<String> cloudIds) {
        String result = null;
        if (Func.isEmpty(cloudIds)) {
            return BaseResponse.newFailInstance("common.param.miss", new Object[]{"CloudIdList"});
        }
        try {
            String frameWorkApi = String.format("/FrameWorkApi/file/v2/batchDownloadByCloudID", interfaceConfig.getBaseUrl());
            HashMap<String, String> reqParams = new HashMap<>();
            reqParams.put("systemID", String.valueOf(SgsSystem.GPO.getSgsSystemId()));
            reqParams.put("cloudId", JSON.toJSONString(cloudIds));
            result = HttpClientUtil.postJson(frameWorkApi, reqParams);
        } catch (Exception ex) {
            log.error("FrameWorkClient.v2.batchDownloadByCloudID 信息异常：{}.", ex);
        }
        return BaseResponse.newSuccessInstance(result);
    }

    public BaseResponse<String> queryHolidayId(String buId, String locationId) {
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("buId", buId);
        paramsMap.put("locationId", locationId);
        try {
            String holidayId = HttpClientUtil.post(interfaceConfig.getBaseUrl() + "/FrameWorkApi/holiday/api/v1/queryHolidayById",paramsMap);
            log.info("queryHolidayId {}-{}返回值：{}",buId,locationId,holidayId);
            return BaseResponse.newSuccessInstance(holidayId);
        } catch(Exception e) {
            log.error( "queryHolidayId error:{}", e.getMessage(), e);
            return BaseResponse.newFailInstance("FrameWork queryHolidayId error");
        }
    }

    public BaseResponse<HolidayDTO> queryHolidayDetail(String holidayId) {
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("holidayId", holidayId);
        String holidayDetail = null;
        try {
            holidayDetail = HttpClientUtil.post(interfaceConfig.getBaseUrl() + "/FrameWorkApi/holiday/api/v1/get", paramsMap);
            //log.info("queryHolidayDetail {}返回值：{}",holidayId,holidayDetail);
        } catch(Exception e) {
            log.error( "queryHolidayDetail error:{}", e.getMessage(), e);
            return BaseResponse.newFailInstance("FrameWork queryHolidayDetail error");
        }
        if(Func.isEmpty(holidayDetail)) {
            return BaseResponse.newFailInstance("FrameWork queryHolidayDetail error");
        }
        HolidayDTO holidayDTO = null;
        try {
            holidayDTO = JSONObject.parseObject(holidayDetail,HolidayDTO.class);
        } catch (Exception e) {
            log.error("queryHolidayDetail error:" + e.getMessage(), e);
            return BaseResponse.newFailInstance("FrameWork queryHolidayDetail error");
        }
        return BaseResponse.newSuccessInstance(holidayDTO);
    }

    public BaseResponse<List<CertificateTypeRsp>> queryCertificateType(Integer locationId,Integer buId){
        List<CertificateTypeRsp> certificateTypeRspList = new ArrayList<>();
        try {
            HashMap<String, String> reqParams = new HashMap<>();
            reqParams.put("bUID", Func.toStr(buId));
            reqParams.put("systemID", String.valueOf(SgsSystem.GPO.getSgsSystemId()));
            reqParams.put("locationID", Func.toStr(locationId));

            String frameWorkApi = String.format("%s/FrameWorkApi/dataDictionary/api/v1/queryCertificateType",
                    interfaceConfig.getBaseUrl());
            String jsonStr = HttpClientUtil.sendGet(frameWorkApi,reqParams);
            if(Func.isEmpty(jsonStr)){
                return BaseResponse.newFailInstance("FrameWork queryCertificateType result empty");
            }
            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            if (Func.isEmpty(jsonObject) || Func.isEmpty(jsonObject.get("data"))){
                return BaseResponse.newFailInstance("FrameWork queryCertificateType result empty");
            }
            certificateTypeRspList = JSONArray.parseArray(jsonObject.get("data").toString(), CertificateTypeRsp.class);

        }catch (Exception ex){
            log.error("FrameWorkClient.queryCertificateType 信息异常：{}.", ex);
            return BaseResponse.newFailInstance("FrameWork queryCertificateType error:" + ex.getMessage());
        }
        return BaseResponse.newSuccessInstance(certificateTypeRspList);
    }

}
