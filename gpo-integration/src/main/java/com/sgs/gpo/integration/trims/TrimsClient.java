package com.sgs.gpo.integration.trims;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.test.conclusion.v2.ConclusionExtBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.HttpClientUtil;
import com.sgs.gpo.core.config.InterfaceConfig;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.integration.trims.req.QueryTrimsAccreditationReq;
import com.sgs.gpo.integration.trims.rsp.TrimsAccreditationRsp;
import com.sgs.trimslocal.facade.*;
import com.sgs.trimslocal.facade.model.analyte.req.QueryTestLineAnalyteReq;
import com.sgs.trimslocal.facade.model.analyte.rsp.TestLineAnalyteRsp;
import com.sgs.trimslocal.facade.model.artifact.req.PpArtifactInfoReq;
import com.sgs.trimslocal.facade.model.artifact.req.PpArtifactRelListReq;
import com.sgs.trimslocal.facade.model.artifact.rsp.PpArtifactInfoRsp;
import com.sgs.trimslocal.facade.model.artifact.rsp.PpArtifactRsp;
import com.sgs.trimslocal.facade.model.citation.req.GetCitationListByTestLineVersionIdReq;
import com.sgs.trimslocal.facade.model.citation.rsp.CitationListRsp;
import com.sgs.trimslocal.facade.model.condition.req.GetTestLineConditionListReq;
import com.sgs.trimslocal.facade.model.condition.rsp.TestConditionRsp;
import com.sgs.trimslocal.facade.model.labsection.req.GetLabSectionBaseInfoReq;
import com.sgs.trimslocal.facade.model.labsection.req.GetLabSectionListByTestLineVersionIdsReq;
import com.sgs.trimslocal.facade.model.labsection.req.LabSectionByLabIdReq;
import com.sgs.trimslocal.facade.model.labsection.rsp.GetLabSectionBaseInfoRsp;
import com.sgs.trimslocal.facade.model.pp.req.GetPpInfoReq;
import com.sgs.trimslocal.facade.model.pp.req.SearchPpInfoReq;
import com.sgs.trimslocal.facade.model.pp.rsp.GetPpBaseInfoRsp;
import com.sgs.trimslocal.facade.model.pp.rsp.SearchPpInfoRsp;
import com.sgs.trimslocal.facade.model.testline.req.*;
import com.sgs.trimslocal.facade.model.testline.rsp.*;
import com.sgs.trimslocal.facade.model.workinginstruction.req.WorkingInstructionReq;
import com.sgs.trimslocal.facade.model.workinginstruction.rsp.WorkingInstructionRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/6/29 16:25
 */
@Service
@Slf4j
public class TrimsClient {

    @Autowired
    private ITestLineFacade testLineFacade;
    @Autowired
    private ILabSectionFacade labSectionFacade;
    @Autowired
    private IPpFacade localPpFacade;
    @Autowired
    private IConditionFacade conditionFacade;
    @Autowired
    private IAnalyteFacade analyteFacade;
    @Autowired
    private IWorkingInstructionFacade workingInstructionFacade;
    @Autowired
    private ICitationFacade citationFacade;
    @Autowired
    private IPpArtifactRelFacade ppArtifactRelFacade;
    @Autowired
    private InterfaceConfig interfaceConfig;

    public BaseResponse<List<CitationListRsp>> getTestLineCitationList(GetCitationListByTestLineVersionIdReq getTestLineCitationInfoListReq){
        if(Func.isEmpty(getTestLineCitationInfoListReq)){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        com.sgs.trimslocal.facade.model.common.BaseResponse<List<CitationListRsp>> testLineCitationInfoList =citationFacade.getCitationListByTestLineVersionId(getTestLineCitationInfoListReq);
        if(Func.isNotEmpty(testLineCitationInfoList.getData())){
            return BaseResponse.newSuccessInstance(testLineCitationInfoList.getData());
        }else{
            BaseResponse error = new BaseResponse();
            error.setStatus(testLineCitationInfoList.getStatus());
            error.setData(testLineCitationInfoList.getData());
            error.setMessage(testLineCitationInfoList.getMessage());
            return error;
        }
    }

    public BaseResponse<List<TestConditionRsp>> getTestLineConditionList(GetTestLineConditionListReq getTestLineConditionListReq){
        if(Func.isEmpty(getTestLineConditionListReq)){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        com.sgs.trimslocal.facade.model.common.BaseResponse<List<TestConditionRsp>> testConditionRsp = conditionFacade.getConditionList(getTestLineConditionListReq);
        if(Func.isNotEmpty(testConditionRsp.getData())){
            return BaseResponse.newSuccessInstance(testConditionRsp.getData());
        }else{
            BaseResponse error = new BaseResponse();
            error.setStatus(testConditionRsp.getStatus());
            error.setData(testConditionRsp.getData());
            error.setMessage(testConditionRsp.getMessage());
            return error;
        }
    }

    public BaseResponse<List<WorkingInstructionRsp>> getWorkingInstructionList(WorkingInstructionReq workingInstructionReq){
        if(Func.isEmpty(workingInstructionReq)){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        com.sgs.trimslocal.facade.model.common.BaseResponse<List<WorkingInstructionRsp>> response = workingInstructionFacade.getWorkingInstructionList(workingInstructionReq);
        if(Func.isNotEmpty(response.getData())){
            return BaseResponse.newSuccessInstance(response.getData());
        }else{
            BaseResponse error = new BaseResponse();
            error.setStatus(response.getStatus());
            error.setData(response.getData());
            error.setMessage(response.getMessage());
            return error;
        }
    }

    public BaseResponse<List<TestLineAnalyteRsp>> getTestLineAnalyteList(QueryTestLineAnalyteReq queryTestLineAnalyteReq) {
        if (Func.isEmpty(queryTestLineAnalyteReq)) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        com.sgs.trimslocal.facade.model.common.BaseResponse<List<TestLineAnalyteRsp>> response = analyteFacade.getTestLineAnalyteList(queryTestLineAnalyteReq);
        if (Func.isNotEmpty(response.getData())) {
            return BaseResponse.newSuccessInstance(response.getData());
        } else {
            BaseResponse error = new BaseResponse();
            error.setStatus(response.getStatus());
            error.setData(response.getData());
            error.setMessage(response.getMessage());
            return error;
        }
    }


    public BaseResponse<List<TestLineSimplifyInfoRsp>> getTestLineSimplifyInfoList(TestLineSimplifyInfoReq testLineSimplifyInfoReq){
        if(Func.isEmpty(testLineSimplifyInfoReq)){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        log.info("getTestLineSimplifyInfoList req:{}",Func.toJson(testLineSimplifyInfoReq));
        com.sgs.trimslocal.facade.model.common.BaseResponse<List<TestLineSimplifyInfoRsp>> response = testLineFacade.getTestLineSimplifyInfoList(testLineSimplifyInfoReq);
        //log.info("getTestLineSimplifyInfoList rsp:{}",Func.toJson(response));
        if(Func.isNotEmpty(response.getData())){
            return BaseResponse.newSuccessInstance(response.getData());
        }else{
            BaseResponse error = new BaseResponse();
            error.setStatus(response.getStatus());
            error.setData(response.getData());
            error.setMessage(response.getMessage());
            return error;
        }
    }

    /**
     * 查询LabSection相关信息
     * @param getLabSectionBaseInfoReq
     * @return
     */
    public BaseResponse<List<GetLabSectionBaseInfoRsp>> getLabSectionBaseInfoList(GetLabSectionBaseInfoReq getLabSectionBaseInfoReq){
        if(Func.isEmpty(getLabSectionBaseInfoReq)){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        log.info("getLabSectionBaseInfoList req:{}",Func.toJson(getLabSectionBaseInfoReq));
        com.sgs.trimslocal.facade.model.common.BaseResponse<List<GetLabSectionBaseInfoRsp>> response = labSectionFacade.getLabSectionBaseInfoList(getLabSectionBaseInfoReq);
        log.info("getLabSectionBaseInfoList rsp:{}",Func.toJson(response));
        if(response.getStatus()!=200 && Func.isNotEmpty(response.getMessage())){
            return BaseResponse.newFailInstance(response.getMessage());
        }else if(Func.isNotEmpty(response.getData())){
            return BaseResponse.newSuccessInstance(response.getData());
        }else{
            BaseResponse baseResponse = new BaseResponse();
            baseResponse.setStatus(response.getStatus());
            baseResponse.setData(response.getData());
            baseResponse.setMessage(response.getMessage());
            return baseResponse;
        }
    }

    /**
     * 查询TestLine基本信息
     * @return
     */
    public List<GetTestLineBaseInfoRsp> getTestLineBaseInfo(GetTestLineBaseInfoReq reqObject) {
        List<GetTestLineBaseInfoRsp> getTestLineBaseInfoRsps = Lists.newArrayList();
        if (Func.isEmpty(reqObject)|| Func.isEmpty(reqObject.getTestLineIds())) {
            return getTestLineBaseInfoRsps;
        }
        try {
            log.info("ITestLineFacade.getTestLineBaseInfo req:{}", JSON.toJSONString(reqObject));
            com.sgs.trimslocal.facade.model.common.BaseResponse<List<GetTestLineBaseInfoRsp>> testLineBaseInfo = testLineFacade.getTestLineBaseInfoList(reqObject);
            //log.info("ITestLineFacade.getTestLineBaseInfo res:{}",JSON.toJSONString(testLineBaseInfo));
            getTestLineBaseInfoRsps = testLineBaseInfo.getData();
        } catch (Exception e) {
            log.error("TrimsLocal.getTestLineBaseInfo 获取信息失败:{}", e);
        }
        return getTestLineBaseInfoRsps;
    }


    /**
     * 查询LabSection相关信息
     * @param getLabSectionListByTestLineVersionIdsReq
     * @return
     */
    public BaseResponse<List<GetLabSectionBaseInfoRsp>> getLabSectionListByTestLineVersionIds(GetLabSectionListByTestLineVersionIdsReq getLabSectionListByTestLineVersionIdsReq){
        if(Func.isEmpty(getLabSectionListByTestLineVersionIdsReq)){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        log.info("getLabSectionListByTestLineVersionIdsReq req:{}",Func.toJson(getLabSectionListByTestLineVersionIdsReq));
        com.sgs.trimslocal.facade.model.common.BaseResponse<List<GetLabSectionBaseInfoRsp>> response = labSectionFacade.getLabSectionListByTestLineVersionIds(getLabSectionListByTestLineVersionIdsReq);
        log.info("getLabSectionListByTestLineVersionIdsReq rsp:{}",Func.toJson(response));
        if(response.getStatus()!=200 && Func.isNotEmpty(response.getMessage())){
            return BaseResponse.newFailInstance(response.getMessage());
        }else if(Func.isNotEmpty(response.getData())){
            return BaseResponse.newSuccessInstance(response.getData());
        }else{
            BaseResponse baseResponse = new BaseResponse();
            baseResponse.setStatus(response.getStatus());
            baseResponse.setData(response.getData());
            baseResponse.setMessage(response.getMessage());
            return baseResponse;
        }
    }

    public BaseResponse<List<GetPpBaseInfoRsp>> getPpBaseInfo(GetPpInfoReq reqObject){
        List<GetPpBaseInfoRsp> ppBaseInfoRspList = new ArrayList<>();
        if(Func.isEmpty(reqObject) || Func.isEmpty(reqObject.getPpBaseIds())){
            return BaseResponse.newFailInstance("common.param.miss",null);        }
        try {
            ppBaseInfoRspList = localPpFacade.getPpBaseInfo(reqObject).getData();
        } catch (Exception e) {
            log.error("TrimsLocal.getPpBaseInfo 获取信息失败", e);
        }
        return BaseResponse.newSuccessInstance(ppBaseInfoRspList);
    }

    public BaseResponse<List<GetLabSectionBaseInfoRsp>> getLabSectionByLabId(Set<Integer> labIds){
        BaseResponse<List<GetLabSectionBaseInfoRsp>> response = new BaseResponse<>();
        LabSectionByLabIdReq labSectionByLabIdReq = new LabSectionByLabIdReq();
        labSectionByLabIdReq.setLabIds(labIds);
        com.sgs.trimslocal.facade.model.common.BaseResponse<List<GetLabSectionBaseInfoRsp>> trimsLabRes =  labSectionFacade.getLabSectionByLabId(labSectionByLabIdReq);
        if(Func.isEmpty(trimsLabRes) || Func.isEmpty(trimsLabRes.getData())){
            return response;
        }
        response.setMessage(trimsLabRes.getMessage());
        response.setStatus(trimsLabRes.getStatus());
        response.setData(trimsLabRes.getData());


        return response;
    }

    public BaseResponse<List<CitationListRsp>> getCitationInfoByCitationVersionId(GetCitationListByTestLineVersionIdReq getCitationListByTestLineVersionIdReq){
        if(Func.isEmpty(getCitationListByTestLineVersionIdReq) || Func.isEmpty(getCitationListByTestLineVersionIdReq.getTestLineVersionId())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        BaseResponse<List<CitationListRsp>> response = new BaseResponse<>();
        com.sgs.trimslocal.facade.model.common.BaseResponse<List<CitationListRsp>> trimsRes = citationFacade.getCitationListByTestLineVersionId(getCitationListByTestLineVersionIdReq);
        if(Func.isEmpty(trimsRes) || Func.isEmpty(trimsRes.getData())){
            return response;
        }


        response.setMessage(trimsRes.getMessage());
        response.setStatus(trimsRes.getStatus());
        response.setData(trimsRes.getData());
        return response;
    }


    public BaseResponse<List<GetCitationBaseInfoRsp>> getCitationBaseInfoList(GetCitationBaseInfoReq getCitationBaseInfoReq){
        if(Func.isEmpty(getCitationBaseInfoReq) || Func.isEmpty(getCitationBaseInfoReq.getCitationBaseIds())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        BaseResponse<List<GetCitationBaseInfoRsp>> response = new BaseResponse<>();
        com.sgs.trimslocal.facade.model.common.BaseResponse<List<GetCitationBaseInfoRsp>> trimsRes = citationFacade.getCitationBaseInfoList(getCitationBaseInfoReq);
        if(Func.isEmpty(trimsRes) || Func.isEmpty(trimsRes.getData())){
            return response;
        }
        response.setMessage(trimsRes.getMessage());
        response.setStatus(trimsRes.getStatus());
        response.setData(trimsRes.getData());
        return response;
    }

    public List<QueryPpTestLineRsp> getPpTestLineList(QueryPpTestLineReq searchPpInfoReq) {
        List<QueryPpTestLineRsp> getPpTestLineInfoRsp = Lists.newArrayList();
        if (searchPpInfoReq == null) {
            return getPpTestLineInfoRsp;
        }
        try {
            com.sgs.trimslocal.facade.model.common.BaseResponse<PageInfo<QueryPpTestLineRsp>> ppTestLineList = testLineFacade.getPpTestLineList(searchPpInfoReq);
            if(Func.isNotEmpty(ppTestLineList) && ppTestLineList.getStatus() == 200){
                getPpTestLineInfoRsp = ppTestLineList.getData().getList();
                log.info("TrimsLocal.getPpTestLineList:{}",getPpTestLineInfoRsp);
            }
        } catch (Exception e) {
            log.error("TrimsLocal.getPpTestLineList 获取信息失败", e);
        }
        return getPpTestLineInfoRsp;
    }

    public List<PpArtifactInfoRsp> getPpArtifactBaseInfoList(PpArtifactInfoReq ppArtifactInfoReq) {
        List<PpArtifactInfoRsp> ppArtifactInfoRsps = Lists.newArrayList();
        if (ppArtifactInfoReq == null) {
            return ppArtifactInfoRsps;
        }
        try {
            if(Func.isEmpty(ppArtifactInfoReq.getLanguageIds())){
                List<Integer> languageIds = Lists.newArrayList();
                languageIds.add(LanguageType.English.getLanguageId());
                languageIds.add(LanguageType.Chinese.getLanguageId());
                ppArtifactInfoReq.setLanguageIds(languageIds);
            }
            com.sgs.trimslocal.facade.model.common.BaseResponse<List<PpArtifactInfoRsp>> ppArtifactRes = ppArtifactRelFacade.getPpArtifactBaseInfoList(ppArtifactInfoReq);
            if(Func.isNotEmpty(ppArtifactRes) && ppArtifactRes.getStatus() == 200){
                ppArtifactInfoRsps = ppArtifactRes.getData();
                log.info("TrimsLocal.getPpArtifactBaseInfoList:{}",ppArtifactInfoRsps);
            }
        } catch (Exception e) {
            log.error("TrimsLocal.getPpArtifactBaseInfoList 获取信息失败", e);
        }
        return ppArtifactInfoRsps;
    }


    public List<PpArtifactRsp>  getPpArtifactRelListByArtifactIds(PpArtifactRelListReq ppArtifactRelListReq){
        List<PpArtifactRsp> ppArtifactList = Lists.newArrayList();
        if(Func.isEmpty(ppArtifactRelListReq) || Func.isEmpty(ppArtifactRelListReq.getArtifactIds())){
            return ppArtifactList;
        }
        try {
            com.sgs.trimslocal.facade.model.common.BaseResponse<List<PpArtifactRsp>>  ppArtifactRsp = ppArtifactRelFacade.getPpArtifactRelListByArtifactIds(ppArtifactRelListReq);
            if(Func.isNotEmpty(ppArtifactRsp) && Func.isNotEmpty(ppArtifactRsp.getData())){
                ppArtifactList = ppArtifactRsp.getData();
            }
        }catch (Exception e){
            log.error("TrimsLocal.getPpArtifactRelListByArtifactIds 获取信息失败", e);
        }

        return ppArtifactList;
    }

    public List<QueryTestLineRsp> getTestLineList(QueryTestLineReq queryTestLineReq) {
        List<QueryTestLineRsp> getTestLineRsps = Lists.newArrayList();
        if (queryTestLineReq == null) {
            return getTestLineRsps;
        }
        try {
            log.info("getTestLineList req:{}", JSON.toJSONString(queryTestLineReq));
            com.sgs.trimslocal.facade.model.common.BaseResponse<List<QueryTestLineRsp>> testLineList = testLineFacade.getTestLineList(queryTestLineReq);
//            getTestLineRsps = testLineList.getData();
            //log.info("getTestLineList res:{}", JSON.toJSONString(testLineList));
            if(Func.isNotEmpty(testLineList.getData())) {
                PageInfo pageInfo = new PageInfo();
                pageInfo = (PageInfo) testLineList.getData();
                getTestLineRsps = pageInfo.getList();
            }
        } catch (Exception e) {
            log.error("TrimsLocal.getTestLineBaseInfo 获取信息失败:{}", e);
        }
        return getTestLineRsps;
    }


    public List<SearchPpInfoRsp> getPpList(SearchPpInfoReq searchPpInfoReq) {
        List<SearchPpInfoRsp> getPpInfoRsp = Lists.newArrayList();
        if (searchPpInfoReq == null) {
            return getPpInfoRsp;
        }
        try {
            com.sgs.trimslocal.facade.model.common.BaseResponse ppList = localPpFacade.getPpList(searchPpInfoReq);
            PageInfo pageInfo = new PageInfo();
            pageInfo = (PageInfo) ppList.getData();
            getPpInfoRsp = pageInfo.getList();
        } catch (Exception e) {
            log.error("TrimsLocal.getPpList 获取信息失败", e);
        }
        return getPpInfoRsp;
    }

    /**
     * 基于PP/TL versionID查询PP/TL的资质
     * @param trimsAccreditationReq
     * @return
     */
    public BaseResponse<List<TrimsAccreditationRsp>> getConfigAccreditation(QueryTrimsAccreditationReq trimsAccreditationReq){
        if(Func.isEmpty(trimsAccreditationReq)){
            return BaseResponse.newFailInstance("common.param.miss",new Object[]{Constants.TERM.REQUEST.getCode()});
        }
        if(Func.isEmpty(trimsAccreditationReq.getProductLineIds()) && Func.isEmpty(trimsAccreditationReq.getLabIds())){
            return BaseResponse.newFailInstance("common.param.miss",new Object[]{Constants.TERM.LAB.getCode()});
        }
        if(Func.isEmpty(trimsAccreditationReq.getTlVersionIdentifiers()) && Func.isEmpty(trimsAccreditationReq.getPpVersionIdentifiers())){
            return BaseResponse.newFailInstance("common.param.miss",new Object[]{"PPVersion/TLVersion"});
        }
        BaseResponse<List<TrimsAccreditationRsp>> accreditationRsp = new BaseResponse();
        String url = interfaceConfig.getBaseUrl() + "/trimslocalapi/api/capacc/getConfigAccreditation";
        try {
            BaseResponse result = HttpClientUtil.doPost(url, trimsAccreditationReq, BaseResponse.class);
            if (Func.isEmpty(result)) {
                accreditationRsp.setMessage("调用getConfigAccreditation接口返回为空");
                accreditationRsp.setStatus(ResponseCode.INTERNAL_SERVER_ERROR.getCode());
                return accreditationRsp;
            }
            if (result.isFail()) {
                return result;
            }
            accreditationRsp.setMessage(result.getMessage());
            accreditationRsp.setStatus(result.getStatus());
            if (Func.isNotEmpty(result.getData())) {
                accreditationRsp.setData(JSON.parseArray(result.getData().toString(), TrimsAccreditationRsp.class));
            }
        } catch (Exception e) {
            accreditationRsp.setMessage(e.getMessage());
            accreditationRsp.setStatus(ResponseCode.INTERNAL_SERVER_ERROR.getCode());
        }
        return accreditationRsp;
    }

}
