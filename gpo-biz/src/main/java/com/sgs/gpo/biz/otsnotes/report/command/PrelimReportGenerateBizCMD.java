package com.sgs.gpo.biz.otsnotes.report.command;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.log.SystemLogHelper;
import com.sgs.framework.log.enums.SystemLogType;
import com.sgs.framework.log.model.SystemLog;
import com.sgs.framework.model.common.print.DigitalReportDataSourceBO;
import com.sgs.framework.model.common.print.OutPutDataBO;
import com.sgs.framework.model.enums.ReportLanguage;
import com.sgs.framework.model.order.order.OrderBO;
import com.sgs.framework.model.report.report.v2.ReportBO;
import com.sgs.framework.model.report.report.v2.ReportMatrixRelBO;
import com.sgs.framework.tool.jackson.JsonUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.biz.output.context.AssembleDataContext;
import com.sgs.gpo.biz.output.service.OutputFactory;
import com.sgs.gpo.core.config.InterfaceConfig;
import com.sgs.gpo.core.constants.BizLogConstant;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.enums.*;
import com.sgs.gpo.core.util.DateUtils;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.attachment.ObjectAttachmentPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.PrelimResultPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportLogPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testmatrix.TestMatrixPO;
import com.sgs.gpo.domain.service.otsnotes.attachment.IObjectAttachmentService;
import com.sgs.gpo.domain.service.otsnotes.report.IReportDomainService;
import com.sgs.gpo.domain.service.otsnotes.report.context.PrelimReportGenerateContext;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IPrelimReportService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportLogService;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain.ITestMatrixService;
import com.sgs.gpo.domain.service.preorder.order.IOrderDomainService;
import com.sgs.gpo.facade.model.digital.dto.GenerateReportTemplateDTO;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixQueryReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.gpo.facade.model.report.req.PrelimReportGenerateReq;
import com.sgs.gpo.facade.model.report.req.ReportQueryReq;
import com.sgs.gpo.integration.dff.DFFClient;
import com.sgs.gpo.integration.digital.DigitalReportClient;
import com.sgs.gpo.integration.digital.req.CallbackSetting;
import com.sgs.gpo.integration.digital.req.DigitalReportInfoBO;
import com.sgs.gpo.integration.digital.req.v2.DigitalReportReq;
import com.sgs.grus.bizlog.BizLogClient;
import com.sgs.grus.bizlog.info.BizLogInfo;
import com.sgs.otsnotes.facade.model.enums.LanguageTypeDigitalReport;
import com.sgs.preorder.facade.model.req.UnpivotProductReq;
import com.sgs.priceengine.facade.model.enums.DigitalReportLanguageType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PrelimReportGenerateBizCMD extends BaseCommand<PrelimReportGenerateContext<PrelimReportGenerateReq>> {

    @Autowired
    private IPrelimReportService prelimReportService;
    @Autowired
    private DFFClient dffClient;
    @Autowired
    private OutputFactory outputFactory;
    @Autowired
    private IReportDomainService reportDomainService;
    @Autowired
    private ITestMatrixService testMatrixService;
    @Autowired
    private InterfaceConfig interfaceConfig;
    @Autowired
    private DigitalReportClient digitalReportClient;
    @Autowired
    private SystemLogHelper systemLogHelper;
    @Autowired
    private BizLogClient bizLogClient;
    @Autowired
    private IObjectAttachmentService objectAttachmentService;
    @Autowired
    private IOrderDomainService orderDomainService;
    @Autowired
    private IReportLogService reportLogService;


    @Override
    public BaseResponse validateParam(PrelimReportGenerateContext<PrelimReportGenerateReq> context) {
        log.info("generate prelim report start... req:{}", context);
        // 校验用户信息不能为空
        UserInfo userInfo = context.getUserInfo();
        if (Func.isEmpty(userInfo)) {
            return BaseResponse.newFailInstance("common.miss", new Object[]{"user"});
        }
        // 校验入参不能为空
        if (Func.isEmpty(context.getParam()) || Func.isEmpty(context.getParam().getPrelimResultId())) {
            return BaseResponse.newFailInstance("common.param.miss", new Object[]{"prelimResultId"});
        }
        // 查询Prelim信息
        PrelimResultPO prelimResult = prelimReportService.getById(context.getParam().getPrelimResultId());
        if (Func.isEmpty(prelimResult)) {
            return BaseResponse.newFailInstance("common.param.invalid", new Object[]{"prelimResultId"});
        }
        //校验报告状态
        if (PrelimResultStatusEnum.check(prelimResult.getPrelimResultStatus(), PrelimResultStatusEnum.REVIEW,PrelimResultStatusEnum.DELIVERED,PrelimResultStatusEnum.CANCEL)) {
            return BaseResponse.newFailInstance("prelim.generate.status.not.match", new Object[]{prelimResult.getPrelimResultNo()});
        }
        //校验模板
        String templateJson = prelimResult.getTemplateJson();
        if (Func.isEmpty(templateJson)) {
            return BaseResponse.newFailInstance("common.param.invalid", new Object[]{"prelimResultId"});
        }
        List<GenerateReportTemplateDTO> templateList = JSON.parseObject(templateJson, new TypeReference<List<GenerateReportTemplateDTO>>() {
        });
        if (Func.isEmpty(templateList)) {
            return BaseResponse.newFailInstance("模板解析失败");
        }
        //查询报告详细信息
        ReportQueryReq reportQueryReq = new ReportQueryReq();
        Set<String> reportIdList = Sets.newHashSet(prelimResult.getReportId());
        reportQueryReq.setReportIdList(reportIdList);
        BaseResponse<List<ReportBO>> reportListRes = reportDomainService.queryBO(reportQueryReq);
        if (reportListRes.isFail() || Func.isEmpty(reportListRes.getData())) {
            return reportListRes;
        }
        ReportBO reportBO = reportListRes.getData().get(0);
        // 查询报告关联的testLine
        if (Func.isNotEmpty(reportBO.getRelationship().getChildren())
                && Func.isNotEmpty(reportBO.getRelationship().getChildren().getReportMatrixList())) {
            Set<String> testMatrixIdList = reportBO.getRelationship().getChildren().getReportMatrixList().stream()
                    .map(ReportMatrixRelBO::getTestMatrixId).collect(Collectors.toSet());
            TestMatrixQueryReq testMatrixQueryReq = new TestMatrixQueryReq();
            testMatrixQueryReq.setTestMatrixIdList(testMatrixIdList);
            BaseResponse<List<TestMatrixPO>> testMatrixResponse = testMatrixService.queryList(testMatrixQueryReq);
            if (Func.isNotEmpty(testMatrixResponse.getData())) {
                context.setTestLineInsIdList(testMatrixResponse.getData().stream().map(TestMatrixPO::getTestLineInstanceID).collect(Collectors.toSet()));
            }
            context.setTestMatrixIds(testMatrixIdList);
        }
        // 默认获取英文模板
        context.setTemplate(templateList.stream().filter(template-> Func.equalsSafe(DigitalReportLanguageType.EN.getCode(),template.getLanguageID()))
                .findAny().orElse(null));
        context.setPrelimResult(prelimResult);
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(PrelimReportGenerateContext<PrelimReportGenerateReq> context) {
        String orderNo = context.getPrelimResult().getOrderNo();
        // 调用DFF 做行转列
        UnpivotProductReq unpivotProductReq = new UnpivotProductReq();
        unpivotProductReq.setOrderNo(orderNo);
        unpivotProductReq.setReportNo(context.getPrelimResult().getReportNo());
        unpivotProductReq.setProductLineCode(context.getLab().getBuCode());
        BaseResponse dffConvertRes = dffClient.unpivotAndSaveProduct(unpivotProductReq);
        if (dffConvertRes.isFail()) {
            return dffConvertRes;
        }
        // 组装Digital参数
        assembleDigitalRequest(context);

        // 查询Order信息
        OrderQueryReq orderQueryReq = new OrderQueryReq();
        orderQueryReq.setOrderNoList(Sets.newHashSet(orderNo));
        List<OrderBO> orderBOList = orderDomainService.queryV1(orderQueryReq).getData();
        if (Func.isNotEmpty(orderBOList)){
            OrderBO orderBO = orderBOList.stream().findFirst().orElse(null);
            context.setOrderBO(orderBO);
        }
        return BaseResponse.newSuccessInstance(true);
    }


    private void assembleDigitalRequest(PrelimReportGenerateContext<PrelimReportGenerateReq> context) {
        List<DigitalReportReq> digitalReportReqList = Lists.newArrayList();
        String orderNo = context.getPrelimResult().getOrderNo();
        String prelimResultNo = context.getPrelimResult().getPrelimResultNo();
        // 调用组装标准对象
        AssembleDataContext assembleDataContext = new AssembleDataContext();
        assembleDataContext.setPrintType(OutPutType.PRELIM_REPORT.getType());
        assembleDataContext.setPrintBy(context.getUserInfo().getRegionAccount());
        assembleDataContext.setOrderNo(orderNo);
        // 生成报告组装参数时根据Report->Matrix->TestLine只查询当前报告相关的数据
        assembleDataContext.setReportIdList(Sets.newHashSet(context.getPrelimResult().getReportId()));
        assembleDataContext.setTestLineInstanceIds(context.getTestLineInsIdList());
        assembleDataContext.setTestMatrixIds(context.getTestMatrixIds());
        assembleDataContext.setPrelimResultNo(prelimResultNo);
        BaseResponse<OutPutDataBO> assembleResponse = outputFactory.assembleData(assembleDataContext);
        OutPutDataBO outPutData = assembleResponse.getData();
        // MultilingualReport 先按照Eng处理
        if(Func.isNotEmpty(outPutData.getOrder().getServiceRequirement().getReport().getReportLanguage())){
            if(ReportLanguage.checkLanguage(outPutData.getOrder().getServiceRequirement().getReport().getReportLanguage().toString()
                    ,ReportLanguage.MultilingualReport)){
                outPutData.getOrder().getServiceRequirement().getReport().setReportLanguage(ReportLanguage.EnglishReportOnly.getLanguageId());
            }
        }

        // Prelim TestData过滤，只显示勾选了testLineFailRemarkDisplayInReport
//        if(Func.isNotEmpty(outPutData.getTestData())&&Func.isNotEmpty(outPutData.getTestData().getTestResultList())){
//            outPutData.getTestData().setTestResultList(outPutData.getTestData().getTestResultList().stream()
//                    .filter(item->Func.isNotEmpty(item.getTestLineFailRemarkDisplayInReport()) && item.getTestLineFailRemarkDisplayInReport()).collect(Collectors.toList()));
//        }
        // citationSectionName
        if(Func.isNotEmpty(assembleDataContext.getDataEntryResult()) && Func.isNotEmpty(assembleDataContext.getDataEntryResult().getTestLineList())){
            if(Func.isNotEmpty(outPutData.getTestLineList())){
                outPutData.getTestLineList().stream().forEach(testLine->{
                    // 匹配数据
                    com.sgs.framework.model.test.testline.TestLineBO dataEntryTestLine = assembleDataContext.getDataEntryResult().getTestLineList().stream()
                            .filter(item-> Func.equalsSafe(item.getTestLineInstanceId(),testLine.getId().getTestLineInstanceId())).findAny().orElse(null);
                    if(Func.isNotEmpty(dataEntryTestLine) && Func.isNotEmpty(dataEntryTestLine.getCitation()) &&
                            Func.isNotEmpty(dataEntryTestLine.getCitation().getCitationSectionName())){
                        testLine.getCitation().setCitationSectionName(dataEntryTestLine.getCitation().getCitationSectionName());
                        if(Func.isNotEmpty(testLine.getCitation().getLanguageList())){
                            testLine.getCitation().getLanguageList().stream().forEach(item->item.setCitationSectionName(dataEntryTestLine.getCitation().getCitationSectionName()));
                        }
                    }
                });
                outPutData.setTestLineList(outPutData.getTestLineList().stream().sorted(Comparator.comparing(item -> (Func.isNotEmpty(item.getCitation().getCitationSectionName())?item.getCitation().getCitationSectionName():null),
                        Comparator.nullsLast(String::compareTo)))
                        .collect(Collectors.toList()));
            }
        }
        // Datasource
        DigitalReportDataSourceBO datasource = new DigitalReportDataSourceBO();
        datasource.setProductLineCode(context.getProductLineCode());
        datasource.setVariableDataList(outPutData);

        if (Func.isNotEmpty(context.getTemplate())) {
            DigitalReportInfoBO info = new DigitalReportInfoBO(context.getProductLineCode());
            info.setInstanceNumber(context.getPrelimResult().getPrelimResultNo());
            info.setBuCode(context.getProductLineCode());
            info.setTemplateSettingID(context.getTemplate().getTemplateSettingID());
            info.setLanguageID(LanguageTypeDigitalReport.EN.getLanguageId());
            digitalReportReqList.add(new DigitalReportReq(info, Func.deepCopy(datasource, DigitalReportDataSourceBO.class)));
        }
        context.setDigitalReportReqList(digitalReportReqList);
    }


    @Override
    public BaseResponse execute(PrelimReportGenerateContext<PrelimReportGenerateReq> context) {
        if (Func.isNotEmpty(context.getDigitalReportReqList())) {
            PrelimReportGenerateReq prelimReportGenerateReq = new PrelimReportGenerateReq();
            prelimReportGenerateReq.setPrelimResultId(context.getPrelimResult().getId());
            prelimReportGenerateReq.setPrelimResultNo(context.getPrelimResult().getPrelimResultNo());
            BaseResponse<String> generateRes = reportDomainService.generatePrelimReport(prelimReportGenerateReq);
            if (generateRes.isFail()) {
                throw new BizException(generateRes.getMessage());
            }
            context.setObjectAttachmentId(generateRes.getData());
            // 触发调用digital接口
            this.callDigitalReport(context);
        }
        return BaseResponse.newSuccessInstance(true);
    }

    private void callDigitalReport(PrelimReportGenerateContext<PrelimReportGenerateReq> context) {
        // 设置异步回写函数地址
        CallbackSetting callbackSetting = new CallbackSetting();
        callbackSetting.setUri(interfaceConfig.getBaseUrl() + Constants.DIGITAL_REPORT_CALL_BACK_URL + Func.toStr(ReportActionType.GENERATE_PRELIM_RESULT.getCode()));
        context.getDigitalReportReqList().stream().forEach(item -> item.setCallbackSetting(callbackSetting));
        String url = interfaceConfig.getDigitalReportUrl() + "/api/v3/ReportAPI/GenerateReport?" +
                "appid=11&token=";

        BaseResponse generateRes = digitalReportClient.generateReport(JsonUtil.toJson(context.getDigitalReportReqList()), url);
        // 本地记录返回结果
        ObjectAttachmentPO objectAttachment = new ObjectAttachmentPO();
        objectAttachment.setId(context.getObjectAttachmentId());
        objectAttachment.setGenerateErrorMessage(generateRes.getMessage());
        objectAttachment.setGenerateStatus(generateRes.isSuccess()? GenerateStatusEnums.SUCCESS.getCode() : GenerateStatusEnums.FAIL.getCode());
        objectAttachmentService.updateById(objectAttachment);

        // 调用接口记录请求日志
        SystemLog requestLog = new SystemLog();
        requestLog.setUrl(url);
        requestLog.setObjectType("report");
        requestLog.setObjectNo(context.getPrelimResult().getPrelimResultNo());
        requestLog.setProductLineCode(context.getProductLineCode());
        requestLog.setType(SystemLogType.API.getType());
        requestLog.setRemark("调用Digital Report生成Prelim报告");
        requestLog.setRequest(JsonUtil.toJson(context.getDigitalReportReqList()));
        requestLog.setOperationType(OperationTypeEnums.GeneratePrelimReport.getValue());
        requestLog.setLocationCode(SystemContextHolder.getLab().getLocationCode());
        requestLog.setCreateBy(SystemContextHolder.getUserInfoFillSystem().getRegionAccount());
        requestLog.setResponse(JsonUtil.toJson(generateRes));
        systemLogHelper.save(requestLog);

        if (generateRes.isFail()) {
            // 更新
            throw new BizException(generateRes.getMessage());
        }
    }


    @Override
    public BaseResponse after(PrelimReportGenerateContext<PrelimReportGenerateReq> context) {
        //记录 Report Log数据
        List<DigitalReportReq> digitalReportReqList = context.getDigitalReportReqList();
        //简化数据结构
        if (Func.isNotEmpty(digitalReportReqList)) {
            digitalReportReqList.forEach(item -> {
                OutPutDataBO outPutDataBO = Optional.ofNullable(item.getDatasource())
                        .map(DigitalReportDataSourceBO::getVariableDataList)
                        .orElse(null);
                if (Func.isEmpty(outPutDataBO)) {
                    return;
                }
                outPutDataBO.setOrder(null);
                outPutDataBO.setTestSampleList(null);
                outPutDataBO.setConditionGroupList(null);
                outPutDataBO.setJobList(null);
                outPutDataBO.setSubcontractList(null);
                outPutDataBO.setReportList(null);
                outPutDataBO.setExtConclusionSummaryList(null);
                outPutDataBO.setServiceItem(null);
                outPutDataBO.setPrintConfig(null);
           });
        }
        PrelimResultPO pr = context.getPrelimResult();
        if (Func.isNotEmpty(pr)) {
            ReportLogPO reportLogPO = new ReportLogPO();
            reportLogPO.setId(UUID.randomUUID().toString());
            reportLogPO.setReportID(pr.getId());
            reportLogPO.setReportNo(pr.getPrelimResultNo());
            reportLogPO.setEventName("GeneratePrelimResult");
            reportLogPO.setDescription(JsonUtil.toJson(digitalReportReqList));
            reportLogPO.setActiveIndicator(true);
            reportLogPO.setCreatedBy(SystemContextHolder.getRegionAccount());
            reportLogPO.setCreatedDate(DateUtils.getNow());
            reportLogPO.setModifiedBy(SystemContextHolder.getRegionAccount());
            reportLogPO.setModifiedDate(DateUtils.getNow());
            reportLogService.saveReportLog(reportLogPO);
        }

        OrderBO orderBO = context.getOrderBO();
        BizLogInfo bizLog = new BizLogInfo();
        boolean labIsNotEmpty = Func.isNotEmpty(orderBO) && Func.isNotEmpty(orderBO.getLab());
        bizLog.setBu((labIsNotEmpty && Func.isNotEmpty(orderBO.getLab().getBuCode())) ? orderBO.getLab().getBuCode() : ProductLineContextHolder.getProductLineCode());
        bizLog.setLab((labIsNotEmpty && Func.isNotEmpty(orderBO.getLab().getLocationCode())) ? orderBO.getLab().getLocationCode() : SystemContextHolder.getLab().getLocationCode());
        bizLog.setOpUser(SystemContextHolder.getRegionAccount());
        bizLog.setBizId(context.getPrelimResult().getOrderNo());
        bizLog.setOpType("Generate PrelimResult Word");
        bizLog.setBizOpType(BizLogConstant.PRELIM_OPERATION_HISTORY);
        bizLog.setNewVal("Generate PrelimResult Word[" + context.getPrelimResult().getPrelimResultNo() + "]");
        bizLog.setOriginalVal("");
        bizLogClient.doSend(bizLog);
        return super.after(context);
    }

}
