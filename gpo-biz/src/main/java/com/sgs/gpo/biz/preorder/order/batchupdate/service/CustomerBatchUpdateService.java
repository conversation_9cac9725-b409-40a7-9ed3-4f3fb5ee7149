package com.sgs.gpo.biz.preorder.order.batchupdate.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.model.common.contact.ContactPersonBO;
import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.model.enums.OrderPersonType;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.model.order.v2.OrderBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.biz.preorder.order.batchupdate.context.OrderBatchUpdateContext;
import com.sgs.gpo.core.constants.BizLogConstant;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.customer.CustomerPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.OrderPersonPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.testrequest.TestRequestPO;
import com.sgs.gpo.domain.service.preorder.customer.subdomain.ICustomerService;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IOrderPersonService;
import com.sgs.gpo.domain.service.preorder.testrequest.ITestRequestService;
import com.sgs.gpo.facade.model.preorder.order.req.OrderBatchUpdateReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import com.sgs.gpo.integration.customer.CustomerClient;
import com.sgs.gpo.integration.framework.FrameworkClient;
import com.sgs.gpo.integration.quotation.QuotationClient;
import com.sgs.grus.bizlog.BizLogClient;
import com.sgs.grus.bizlog.info.BizLogInfo;
import com.sgs.otsnotes.facade.model.enums.GpoCustomerType;
import com.sgs.preorder.facade.model.dto.customer.CustomerExtDTO;
import com.sgs.preorder.facade.model.info.DataDictInfo;
import com.sgs.preorder.facade.model.req.DataDictReq;
import com.sgs.priceengine.facade.model.enums.CustomerUsage;
import com.sgs.priceengine.facade.model.request.OrderCustomerInfo;
import com.sgs.priceengine.facade.model.request.TriggerNewVersionReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service("customerBatchUpdateService")
public class CustomerBatchUpdateService extends OrderBatchUpdateBaseService {
    private static final Logger log = LoggerFactory.getLogger(CustomerBatchUpdateService.class);
    @Autowired
    private ICustomerService customerService;
    @Autowired
    private IOrderPersonService orderPersonService;
    @Resource
    private QuotationClient quotationClient;
    @Autowired
    private BizLogClient bizLogClient;
    @Autowired
    private CustomerClient customerClient;
    @Autowired
    private FrameworkClient frameworkClient;
    @Autowired
    private ITestRequestService testRequestService;

    @Override
    @Transactional
    protected BaseResponse<Boolean> update(OrderBatchUpdateContext context) {
        OrderBatchUpdateReq orderBatchUpdateReq = context.getParam();
        List<GeneralOrderPO> targetOrderList = context.getTargetOrderList();
        List<CustomerPO> orgCustomerPOList = context.getOrgCustomerPOList();
        List<CustomerPO> targetCustomerPOList = context.getTargetCustomerPOList();
        List<OrderBO> orderBOList = context.getAllOrderBOList();
        UserInfo userInfo = context.getUserInfo();
        GeneralOrderPO orgOrder = context.getOrgOrder();
        if (Func.isEmpty(orgCustomerPOList) || Func.isEmpty(orderBatchUpdateReq) || Func.isEmpty(orderBatchUpdateReq.getUpdateSection())) {
            return BaseResponse.newSuccessInstance(true);
        }
        List<OrderBatchUpdateReq.BatchUpdateSectionReq> updateSectionList = orderBatchUpdateReq.getUpdateSection();
        OrderBatchUpdateReq.BatchUpdateSectionReq customer = updateSectionList.stream().filter(item -> Func.equalsSafe(item.getAttributeCode(), "customer")).findAny().orElse(null);
        if (Func.isEmpty(customer) || Func.isEmpty(customer.getSubSectionReqList())) {
            return BaseResponse.newSuccessInstance(true);
        }
        Set<String> subAttributeCodeList = customer.getSubSectionReqList().stream().map(OrderBatchUpdateReq.BatchUpdateSubSectionReq::getAttributeCode).collect(Collectors.toSet());
        List<CustomerPO> saveOrderUpdateBatchList = new ArrayList<>();
        for (String customerCode : subAttributeCodeList) {
            for (GeneralOrderPO order : targetOrderList) {
                if (Func.isEmpty(targetCustomerPOList)) {
                    targetCustomerPOList = new ArrayList<>();
                }
                CustomerPO customerPO = null;
                switch (customerCode) {
                    case "applicantInformation":
                        customerPO = this.buildNewCustomer(context, GpoCustomerType.Applicant, order.getId());
                        break;
                    case "payerInformation":
                        customerPO = this.buildNewCustomer(context, GpoCustomerType.Payer, order.getId());
                        break;
                    case "buyerInformation":
                        customerPO = this.buildNewCustomer(context, GpoCustomerType.Buyer, order.getId());
                        break;
                    case "supplierInformation":
                        customerPO = this.buildNewCustomer(context, GpoCustomerType.Supplier, order.getId());
                        break;
                    case "manufactureInformation":
                        customerPO = this.buildNewCustomer(context, GpoCustomerType.Manufacture, order.getId());
                        break;
                }
                if (Func.isNotEmpty(customerPO)) {
                    saveOrderUpdateBatchList.add(customerPO);
                }
            }
        }
        if (Func.isNotEmpty(saveOrderUpdateBatchList)) {
            customerService.saveOrUpdateBatch(saveOrderUpdateBatchList);
            //是否更新Payer信息
            List<CustomerPO> payerCustomerList = saveOrderUpdateBatchList.stream().filter(item -> CustomerUsage.check(item.getCustomerUsage(), CustomerUsage.Payer)).collect(Collectors.toList());
            if (Func.isNotEmpty(payerCustomerList)) {
                for (CustomerPO customerPO : payerCustomerList) {
                    OrderBO orderBO = orderBOList.stream().filter(item -> Func.equalsSafe(item.getId().getOrderId(), customerPO.getGeneralOrderId())).findAny().orElse(null);
                    updateSalesPerson(orderBO, context);
                    CustomerPO oldPayer = targetCustomerPOList.stream().filter(item -> CustomerUsage.check(item.getCustomerUsage(), CustomerUsage.Payer) && Func.equalsSafe(item.getGeneralOrderId(), customerPO.getGeneralOrderId())).findAny().orElse(null);
                    //Payer更新记录BizLog
                    BizLogInfo bizLog = new BizLogInfo();
                    bizLog.setBizId(orderBO.getId().getOrderNo());
                    bizLog.setBu(orderBO.getLab().getProductLineCode());
                    bizLog.setLab(orderBO.getLab().getLocationCode());
                    bizLog.setBizOpType(BizLogConstant.ORDER_OPERATION_HISTORY);
                    bizLog.setOpType("Payer Payment term Update");
                    bizLog.setOpUser(userInfo.getRegionAccount());
                    if(Func.isNotEmpty(oldPayer)){
                        bizLog.setOriginalVal("Contact Person : " + oldPayer.getContactPersonName() +  " Payment Term: " + oldPayer.getPaymentTermName());
                    }
                    bizLog.setNewVal("Contact Person : " + customerPO.getContactPersonName() +  " Payment Term: " + customerPO.getPaymentTermName());
                    if(Func.isEmpty(oldPayer) || !Func.equalsSafe(oldPayer.getPaymentTermName(), customerPO.getPaymentTermName())){
                        bizLogClient.doSend(bizLog);
                    }
                }
            }
            Set<String> orderIdList = targetOrderList.stream().map(GeneralOrderPO::getId).collect(Collectors.toSet());
            OrderIdReq orderIdReq = new OrderIdReq();
            orderIdReq.setOrderIdList(orderIdList);
            BaseResponse<List<CustomerPO>> customerPORes = customerService.select(orderIdReq);
            List<CustomerPO> allCustomerPOList = null;
            if (customerPORes.isSuccess()) {
                allCustomerPOList = customerPORes.getData();
            }
            for (GeneralOrderPO generalOrderPO : targetOrderList) {
                OrderBO orderBO = orderBOList.stream().filter(item -> Func.equalsSafe(item.getId().getOrderNo(), generalOrderPO.getOrderNo())).findAny().orElse(null);
                if (Func.isNotEmpty(orderBO)) {
                    List<OrderCustomerInfo> customerInfoList = Lists.newArrayList();
                    List<CustomerPO> customerPOList = allCustomerPOList.stream().filter(item -> Func.equalsSafe(item.getGeneralOrderId(), generalOrderPO.getId())).collect(Collectors.toList());
                    if (Func.isNotEmpty(customerPOList)) {
                        customerInfoList = Func.copy(customerPOList, CustomerPO.class, OrderCustomerInfo.class);
                    }
                    Set<String> customerIds = customerPOList.stream().filter(item -> CustomerUsage.check(Func.toStr(item.getCustomerUsage()), CustomerUsage.Applicant, CustomerUsage.Buyer, CustomerUsage.Payer)).map(item -> item.getCustomerId()).collect(Collectors.toSet());
                    List<TestRequestPO> targetTestRequestPOList = context.getTargetTestRequestPOList();
                    if (Func.isNotEmpty(targetTestRequestPOList)) {
                        TestRequestPO targetTestRequestPO = targetTestRequestPOList.stream().filter(item -> Func.equalsSafe(item.getGeneralOrderId(), generalOrderPO.getId())).findAny().orElse(null);
                        if (Func.isNotEmpty(targetTestRequestPO)) {
                            updateTestRequest(orderBO, targetTestRequestPO.getId(), customerIds);
                        }
                    }
                    //触发Qutation升级
                    TriggerNewVersionReq triggerNewVersionReq = new TriggerNewVersionReq();
                    triggerNewVersionReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                    triggerNewVersionReq.setSystemId(SgsSystem.GPO.getSgsSystemId());
                    triggerNewVersionReq.setOrderId(generalOrderPO.getId());
                    triggerNewVersionReq.setTriggerType(3);
                    triggerNewVersionReq.setSgsToken(context.getToken());
                    triggerNewVersionReq.setOrderType(orderBO.getHeader().getCaseType());
                    triggerNewVersionReq.setSalesPerson(context.getNewSalesPerson());
                    triggerNewVersionReq.setCustomerInfoList(customerInfoList);
                    BaseResponse baseResponse = quotationClient.triggerNewVersion(triggerNewVersionReq);
                    log.info("triggerNewVersion res:{}",Func.toJson(baseResponse));
                    //记录BizLog
                    BizLogInfo bizLog = new BizLogInfo();
                    bizLog.setBizId(generalOrderPO.getOrderNo());
                    bizLog.setBu(orderBO.getLab().getBuCode());
                    bizLog.setLab(orderBO.getLab().getLocationCode());
                    bizLog.setBizOpType(BizLogConstant.ORDER_OPERATION_HISTORY);
                    bizLog.setOpType("Batch Update");
                    bizLog.setOpUser(userInfo.getRegionAccount());
                    String bizNewVal = "Batch update Customer From OrderNo:" + orgOrder.getOrderNo();
                    bizLog.setNewVal(bizNewVal);
                    bizLogClient.doSend(bizLog);
                }
            }
        }
        return BaseResponse.newSuccessInstance(true);
    }

    private void updateTestRequest(OrderBO orderBO, String testRequestId, Set<String> customerIds) {
        String labCode = orderBO.getLab().getLabCode();
        String buCode = orderBO.getLab().getBuCode();
        String locationCode = orderBO.getLab().getLocationCode();
        Integer sampleSaveDuration = this.getSampleSaveDurationByCustomer(customerIds, locationCode, buCode, labCode);
        TestRequestPO testRequestPO = new TestRequestPO();
        testRequestPO.setId(testRequestId);
        testRequestPO.setSampleSaveDuration(sampleSaveDuration);
        testRequestService.updateById(testRequestPO);
    }

    public Integer getSampleSaveDurationByCustomer(Set<String> customerIdList, String locationCode, String buCode, String labCode){
        //根据Applicant、Buyer、Payer查询样品保存天数 优先取时间最长的，没有维护取Lab的General保存期限
        if(Func.isEmpty(customerIdList)){
            return getSampleSaveDurationByLab(labCode);
        }else{
            String customerIdStr = String.join(",", customerIdList);
            log.info("getSampleSaveDurationByCustomer.getCustomerExt:{}",customerIdStr);
            List<CustomerExtDTO> customerExtDTOS = customerClient.getCustomerExt(customerIdStr, locationCode,buCode);
            log.info("getSampleSaveDurationByCustomer.getCustomerExt result:{}", JSON.toJSONString(customerExtDTOS));

            customerExtDTOS = customerExtDTOS.stream().filter(i->Func.isNotEmpty(i.getSampleStoragePeriod())).collect(Collectors.toList());
            if(Func.isEmpty(customerExtDTOS)){
                return getSampleSaveDurationByLab(labCode);
            }else{
                //取天数最大的
                CustomerExtDTO customerExtDTO = customerExtDTOS.stream().max((po1, po2) -> po1.getSampleStoragePeriod() > po2.getSampleStoragePeriod() ? 1 : -1).get();
                log.info("max SampleStoragePeriod customerExtDTO Info:{}",JSON.toJSONString(customerExtDTO));
                if(Func.isEmpty(customerExtDTO) || Func.isEmpty(customerExtDTO.getSampleStoragePeriod())){
                    return getSampleSaveDurationByLab(labCode);
                }else{
                    return customerExtDTO.getSampleStoragePeriod();
                }
            }

        }
    }
    private Integer getSampleSaveDurationByLab(String labCode){
        log.info("getSampleSaveDurationByLab:{}",labCode);
        //根据Applicant、Buyer、Payer查询样品保存天数 优先取时间最长的，没有维护取Lab的General保存期限
        DataDictReq dataDictReq = new DataDictReq();
        dataDictReq.setSysKeyGroup(Constants.DATA_DICT.SYS_KEY_GROUP.DEFAULT_SAMPLE_STORAGE_PERIOD);
        //此处systemId要求传固定值0
        dataDictReq.setSystemId(SgsSystem.DEFAULT.getSgsSystemId());
        dataDictReq.setSysKey(labCode);
        List<DataDictInfo> dataDictList = frameworkClient.getDataDictList(dataDictReq);
        if(Func.isNotEmpty(dataDictList) && Func.isNotEmpty(dataDictList.get(0).getSysValue())){
            return Integer.parseInt(dataDictList.get(0).getSysValue());
        }else{
            return null;
        }
    }


    private void updateSalesPerson(OrderBO orderBO, OrderBatchUpdateContext context) {
        String salesPerson = context.getNewSalesPerson();
        if (Func.isEmpty(salesPerson) || Func.isEmpty(orderBO)) {
            return;
        }

        List<ContactPersonBO> contactPersonList = orderBO.getContactPersonList();
        if (Func.isNotEmpty(contactPersonList)) {
            ContactPersonBO contactPersonBO = contactPersonList.stream().filter(item -> OrderPersonType.check(item.getContactUsage(), OrderPersonType.SALES)).findAny().orElse(null);
            OrderPersonPO personPO = new OrderPersonPO();
            if (Func.isNotEmpty(contactPersonBO)) {
                personPO.setId(contactPersonBO.getId());
            } else {
                personPO.setId(IdUtil.uuId());
                personPO.setCreatedDate(new Date());
                personPO.setCreatedBy(context.getUserInfo().getRegionAccount());
            }
            personPO.setGeneralOrderId(orderBO.getId().getOrderId());
            personPO.setPersonType(OrderPersonType.SALES.getCode());
            personPO.setRegionAccount(context.getNewSalesPerson());
            personPO.setEmail(context.getNewSalesPersonEmail());
            personPO.setActiveIndicator(ActiveType.Enable.getStatus());
            personPO.setModifiedDate(new Date());
            personPO.setModifiedBy(context.getUserInfo().getRegionAccount());
            orderPersonService.saveOrUpdate(personPO);
        }
    }


    private CustomerPO buildNewCustomer(OrderBatchUpdateContext context, GpoCustomerType customerType, String orderId) {
        Date newDate = new Date();
        String regionAccount = context.getUserInfo().getRegionAccount();

        List<CustomerPO> orgCustomerPOList = context.getOrgCustomerPOList();
        List<CustomerPO> targetCustomerPOList = context.getTargetCustomerPOList();
        CustomerPO orgCustomer = orgCustomerPOList.stream().filter(item -> Func.equalsSafe(item.getCustomerUsage(), customerType.getCode())).findAny().orElse(null);
        if (Func.isEmpty(orgCustomer)) {
            return null;
        }
        CustomerPO targetCustomer = null;
        if (Func.isNotEmpty(targetCustomerPOList)) {
            targetCustomer = targetCustomerPOList.stream().filter(item -> Func.equalsSafe(item.getGeneralOrderId(), orderId) && Func.equalsSafe(item.getCustomerUsage(), customerType.getCode())).findAny().orElse(null);
        }
        CustomerPO newCustomer = new CustomerPO();
        newCustomer.setCustomerId(Func.toStr(orgCustomer.getCustomerId()));
        newCustomer.setCustomerGroupId(Func.toStr(orgCustomer.getCustomerGroupId()));
        newCustomer.setCustomerAddressCn(Func.toStr(orgCustomer.getCustomerAddressCn()));
        newCustomer.setCustomerAddressEn(Func.toStr(orgCustomer.getCustomerAddressEn()));
        newCustomer.setCustomerNameCn(Func.toStr(orgCustomer.getCustomerNameCn()));
        newCustomer.setCustomerNameEn(Func.toStr(orgCustomer.getCustomerNameEn()));
        newCustomer.setContactPersonEmail(Func.toStr(orgCustomer.getContactPersonEmail()));
        newCustomer.setContactPersonFax(Func.toStr(orgCustomer.getContactPersonFax()));
        newCustomer.setContactPersonPhone1(Func.toStr(orgCustomer.getContactPersonPhone1()));
        newCustomer.setContactPersonName(Func.toStr(orgCustomer.getContactPersonName()));
        newCustomer.setContactPersonRemark(Func.toStr(orgCustomer.getContactPersonRemark()));
        newCustomer.setContactPersonPhone2(Func.toStr(orgCustomer.getContactPersonPhone2()));
        newCustomer.setCustomerCredit(Func.toStr(orgCustomer.getCustomerCredit()));
        newCustomer.setCustomerUsage(Func.toStr(orgCustomer.getCustomerUsage()));
        newCustomer.setAccountId(Func.toLong(orgCustomer.getAccountId()));
        newCustomer.setBossNumber(Func.toLong(orgCustomer.getBossNumber()));
        newCustomer.setContactAddressId(Func.toStr(orgCustomer.getContactAddressId()));
        newCustomer.setBuyerGroup(Func.toStr(orgCustomer.getBuyerGroup()));
        newCustomer.setBuyerGroupName(Func.toStr(orgCustomer.getBuyerGroupName()));
        newCustomer.setSupplierNo(Func.toStr(orgCustomer.getSupplierNo()));
        newCustomer.setLogoCloudId(Func.toStr(orgCustomer.getLogoCloudId()));
        newCustomer.setOrganizationName(Func.toStr(orgCustomer.getOrganizationName()));
        newCustomer.setIsAsApplicant(Func.toInteger(orgCustomer.getIsAsApplicant()));
        newCustomer.setReportDeliveredTo(Func.toStr(orgCustomer.getReportDeliveredTo()));
        newCustomer.setFailedReportDeliveredTo(Func.toStr(orgCustomer.getFailedReportDeliveredTo()));
        newCustomer.setBossSiteUseId(Func.toLong(orgCustomer.getBossSiteUseId()));
        newCustomer.setBossContactId(Func.toLong(orgCustomer.getBossContactId()));
        newCustomer.setPrimaryFlag(Func.toStr(orgCustomer.getPrimaryFlag()));
        newCustomer.setMonthlyPayment(Func.toStr(orgCustomer.getMonthlyPayment()));
        newCustomer.setBossLocationCode(Func.toStr(orgCustomer.getBossLocationCode()));
        newCustomer.setPaymentTermName(Func.toStr(orgCustomer.getPaymentTermName()));
        newCustomer.setCustomerLock(Func.toInteger(orgCustomer.getCustomerLock()));
        newCustomer.setSgsMartAccount(Func.toStr(orgCustomer.getSgsMartAccount()));
        newCustomer.setSgsMartUserId(Func.toStr(orgCustomer.getSgsMartUserId()));

        newCustomer.setModifiedDate(newDate);
        newCustomer.setModifiedBy(regionAccount);
        newCustomer.setActiveIndicator(ActiveType.Enable.getStatus());
        if (Func.isEmpty(targetCustomer)) {
            //新增
            newCustomer.setId(IdUtil.uuId());
            newCustomer.setCreatedDate(newDate);
            newCustomer.setCreatedBy(regionAccount);
        }else{
            newCustomer.setId(targetCustomer.getId());
            newCustomer.setCreatedBy(targetCustomer.getCreatedBy());
            newCustomer.setCreatedDate(targetCustomer.getCreatedDate());
        }
        newCustomer.setGeneralOrderId(orderId);
        return newCustomer;
    }
}
