package com.sgs.gpo.biz.otsnotes.dataentry.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Sets;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseExecutor;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.model.Lab;
import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.framework.model.enums.ReportEntryModeEnum;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.biz.otsnotes.dataentry.IDateEntryBizService;
import com.sgs.gpo.biz.otsnotes.dataentry.command.TLDataEntryQueryBizCMD;
import com.sgs.gpo.biz.otsnotes.dataentry.command.TLDataEntrySaveBizCMD;
import com.sgs.gpo.biz.otsnotes.dataentry.command.TLDataEntrySaveCheckBizCMD;
import com.sgs.gpo.biz.otsnotes.dataentry.context.TLDataEntryContext;
import com.sgs.gpo.core.common.SystemCache;
import com.sgs.gpo.core.enums.DataEntryEventEnums;
import com.sgs.gpo.core.enums.MatrixActionEnums;
import com.sgs.gpo.core.enums.ReportStatus;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO;
import com.sgs.gpo.domain.service.otsnotes.report.IReportDomainService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportMatrixRelService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportService;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.ITestMatrixDomainService;
import com.sgs.gpo.facade.model.dataentry.DataEntryEventReq;
import com.sgs.gpo.facade.model.otsnotes.testdata.dto.TLDataEntryDTO;
import com.sgs.gpo.facade.model.otsnotes.testdata.req.TLDataHeaderQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testdata.req.TLDataHeaderSaveReq;
import com.sgs.gpo.facade.model.otsnotes.testdata.rsp.TLDataHeaderSaveCheckRsp;
import com.sgs.gpo.facade.model.otsnotes.testdata.rsp.TLDataHeaderSaveRsp;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixProcessReq;
import com.sgs.gpo.facade.model.report.bo.ReportMatrixRelBO;
import com.sgs.gpo.facade.model.report.req.ReportEntryModeUpdateReq;
import com.sgs.gpo.facade.model.report.req.ReportExtForTLUpdateReq;
import com.sgs.gpo.facade.model.report.req.ReportMatrixQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportTestResultStatusUpdateReq;
import com.sgs.otsnotes.facade.model.enums.MatrixStatus;
import com.sgs.otsnotes.facade.model.enums.SignTypeEnums;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DataEntreBizServiceImpl implements IDateEntryBizService {
    @Autowired
    private ITestMatrixDomainService testMatrixDomainService;
    @Autowired
    private IReportMatrixRelService reportMatrixRelService;
    @Autowired
    private IReportService reportService;
    @Autowired
    private IReportDomainService reportDomainService;
    @Override
    public BaseResponse<IPage<TLDataEntryDTO>> queryTlDataPage(TLDataHeaderQueryReq tlDataHeaderQueryReq, Integer page, Integer rows) {
        tlDataHeaderQueryReq.setQueryPage(true);
        TLDataEntryContext<TLDataHeaderQueryReq> tlDataEntryContext = new TLDataEntryContext();
        tlDataEntryContext.setUserInfo(SystemContextHolder.getUserInfo());
        tlDataEntryContext.setToken(SystemContextHolder.getSgsToken());
        tlDataEntryContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        tlDataEntryContext.setTlDataEntryPage(new Page<>());
        tlDataEntryContext.setParam(tlDataHeaderQueryReq);
        tlDataEntryContext.setLab(SystemContextHolder.getLab());
        tlDataEntryContext.setPage(page);
        tlDataEntryContext.setRows(rows);
        return BaseExecutor.start(TLDataEntryQueryBizCMD.class,tlDataEntryContext);
    }

    @Override
    public BaseResponse<List<TLDataEntryDTO>> queryTlDataList(TLDataHeaderQueryReq tlDataHeaderQueryReq) {
        TLDataEntryContext<TLDataHeaderQueryReq> tlDataEntryContext = new TLDataEntryContext();
        tlDataEntryContext.setUserInfo(SystemContextHolder.getUserInfo());
        tlDataEntryContext.setToken(SystemContextHolder.getSgsToken());
        tlDataEntryContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        tlDataEntryContext.setTlDataEntryPage(new Page<>());
        tlDataEntryContext.setParam(tlDataHeaderQueryReq);
        if(Func.isNotEmpty(tlDataHeaderQueryReq.getLabCode())){
            Lab currentLab = SystemCache.labCache.get(tlDataHeaderQueryReq.getLabCode());
            if(Func.isNotEmpty(currentLab)){
                SystemContextHolder.getContext().setLab(currentLab);
            }
        }
        tlDataEntryContext.setLab(SystemContextHolder.getLab());
        BaseResponse baseResponse = BaseExecutor.start(TLDataEntryQueryBizCMD.class, tlDataEntryContext);
        if(baseResponse.isSuccess()){
            IPage<TLDataEntryDTO> tlDataEntryDTOIPage = (IPage<TLDataEntryDTO>) baseResponse.getData();
            List<TLDataEntryDTO> records = new ArrayList<>();
            if(Func.isNotEmpty(tlDataEntryDTOIPage)){
                records = tlDataEntryDTOIPage.getRecords();
            }
            return BaseResponse.newSuccessInstance(records);
        }else{
            return baseResponse;
        }
    }

    @Override
    public BaseResponse<TLDataHeaderSaveRsp> saveTLDataHeader(TLDataHeaderSaveReq tlDataHeaderSaveReq) {
        String productLineCode = ProductLineContextHolder.getProductLineCode();
        if(!StringUtils.equalsIgnoreCase(ProductLineType.MR.getProductLineAbbr(),productLineCode)){
            return BaseResponse.newSuccessInstance(true);
        }
        TLDataEntryContext<TLDataHeaderSaveReq> tlDataEntryContext = new TLDataEntryContext();
        tlDataEntryContext.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        tlDataEntryContext.setToken(tlDataHeaderSaveReq.getToken());
        if(Func.isEmpty(tlDataEntryContext.getToken())){
            tlDataEntryContext.setToken(SystemContextHolder.getSgsToken());
        }
        tlDataEntryContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        tlDataEntryContext.setTlDataEntryPage(new Page<>());
        tlDataEntryContext.setParam(tlDataHeaderSaveReq);
        tlDataEntryContext.setLab(SystemContextHolder.getLab());
        return BaseExecutor.start(TLDataEntrySaveBizCMD.class,tlDataEntryContext);
    }
    @Override
    public BaseResponse<List<TLDataHeaderSaveCheckRsp>> saveCheckTLDataHeader(TLDataHeaderSaveReq tlDataHeaderSaveReq) {
        String productLineCode = ProductLineContextHolder.getProductLineCode();
        if(!StringUtils.equalsIgnoreCase(ProductLineType.MR.getProductLineAbbr(),productLineCode)){
            return BaseResponse.newSuccessInstance(true);
        }
        TLDataEntryContext<TLDataHeaderSaveReq> tlDataEntryContext = new TLDataEntryContext();
        tlDataEntryContext.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        tlDataEntryContext.setToken(SystemContextHolder.getSgsToken());
        tlDataEntryContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        tlDataEntryContext.setTlDataEntryPage(new Page<>());
        tlDataEntryContext.setParam(tlDataHeaderSaveReq);
        tlDataEntryContext.setLab(SystemContextHolder.getLab());
        return BaseExecutor.start(TLDataEntrySaveCheckBizCMD.class,tlDataEntryContext);
    }
    @Override
    public BaseResponse<Boolean> handleDataEntryEvent(DataEntryEventReq dataEntryEventReq) {
        log.info("DataEntryEvent Process req:{}",Func.toJson(dataEntryEventReq));
        String dimension = dataEntryEventReq.getDimension();
        String event = dataEntryEventReq.getEvent();
        String objectType = dataEntryEventReq.getObjectType();
        String orderNo = dataEntryEventReq.getOrderNo();
        String objectId = dataEntryEventReq.getObjectId();
        String reportId = dataEntryEventReq.getReportId();
        String labCode = dataEntryEventReq.getLabCode();
        String regionAccount = dataEntryEventReq.getRegionAccount();
        if(Func.isEmpty(event)){
            return BaseResponse.newFailInstance("common.param.miss",new Object[]{"event"});
        }
        // TODO  临时方案，GPO发布的分支代码中暂时不处理TestSchemeMatrix数据，待MR结构化合并后再去掉
        if(Func.equalsIgnoreCase(objectType,"TestSchemeMatrix")){
            return BaseResponse.newFailInstance("暂不支持处理TestSchemeEntry");
        }
        if(Func.isEmpty(dimension)){
            return BaseResponse.newFailInstance("common.param.miss",new Object[]{"dimension"});
        }
        if(Func.isEmpty(dataEntryEventReq.getReportId())){
            return BaseResponse.newFailInstance("common.param.miss",new Object[]{"reportId"});
        }

        event = event.toLowerCase();
        DataEntryEventEnums dataEntryEventEnums = DataEntryEventEnums.getEvent(event);
        Integer newMatrixStatus = null;
        String matrixAction = "";
        switch (dataEntryEventEnums) {
            case Entered:
                matrixAction = MatrixActionEnums.Save.getAction();
                newMatrixStatus = MatrixStatus.Entered.getStatus();
                break;
            case Return:
                matrixAction = MatrixActionEnums.Return.getAction();
                newMatrixStatus = MatrixStatus.Entered.getStatus();
                break;
            case Retest:
                matrixAction = MatrixActionEnums.Reset.getAction();
                newMatrixStatus = MatrixStatus.Entered.getStatus();
                break;
            case Submit:
                matrixAction = MatrixActionEnums.Submit.getAction();
                newMatrixStatus = MatrixStatus.Submit.getStatus();
                break;
            case Validate:
                matrixAction = MatrixActionEnums.Validate.getAction();
                newMatrixStatus = MatrixStatus.Completed.getStatus();
                break;
        }
        if(Func.isAnyEmpty(matrixAction,newMatrixStatus)){
            return BaseResponse.newFailInstance("Unknown Matrix Action or MatrixStatus");
        }

        JSONObject jsonObject = JSONObject.parseObject(dimension);
        if(Func.isEmpty(jsonObject) || Func.isEmpty(jsonObject.getOrDefault("testLineInstanceId",""))){
            return BaseResponse.newFailInstance("common.param.miss",new Object[]{"testLineInstanceId"});
        }
        ReportMatrixQueryReq reportMatrixQueryReq = new ReportMatrixQueryReq();
        reportMatrixQueryReq.setTestLineInstanceIdList(Sets.newHashSet(Func.toStr(jsonObject.get("testLineInstanceId"))));
        reportMatrixQueryReq.setReportIdList(Sets.newHashSet(reportId));
        List<ReportMatrixRelBO> reportMatrixRelBOList = reportMatrixRelService.queryReportMatrix(reportMatrixQueryReq).getData();
        if(Func.isEmpty(reportMatrixRelBOList)){
            return BaseResponse.newFailInstance("common.empty",new Object[]{"ReportMatrix"});
        }
        log.info("DataEntryEvent Process reportMatrixRelList:{}",Func.toJson(reportMatrixRelBOList));
        reportMatrixRelBOList = reportMatrixRelBOList.stream().filter(item->Func.equalsSafe(item.getReportId(),reportId)).collect(Collectors.toList());
        Set<String> testMatrixIdList = reportMatrixRelBOList.parallelStream().map(ReportMatrixRelBO::getTestMatrixId).collect(Collectors.toSet());
        Set<String> reportIdList = reportMatrixRelBOList.parallelStream().map(ReportMatrixRelBO::getReportId).collect(Collectors.toSet());
        TestMatrixProcessReq testMatrixProcessReq = new TestMatrixProcessReq();
        testMatrixProcessReq.setTestMatrixIdList(testMatrixIdList);
        testMatrixProcessReq.setAction(matrixAction);
        testMatrixProcessReq.setNewMatrixStatus(newMatrixStatus);
        testMatrixProcessReq.setToken(dataEntryEventReq.getToken());
        testMatrixProcessReq.setProductLineCode(dataEntryEventReq.getProductLineCode());
        testMatrixProcessReq.setTestLineInstanceIdList(Sets.newHashSet(Func.toStr(jsonObject.get("testLineInstanceId"))));
        testMatrixProcessReq.setEntryMode(ReportEntryModeEnum.REPORT_TEST_LINE.getCode());
        BaseResponse updateMatrixRes = testMatrixDomainService.updateMatrixStatus(testMatrixProcessReq);
        log.info("DataEntry Event updateMatrixStatus Res:{}-{}",Func.toJson(testMatrixProcessReq),Func.toJson(updateMatrixRes));
        if(updateMatrixRes.isFail()){
            return BaseResponse.newFailInstance(updateMatrixRes.getMessage());
        }

        if(Func.isNotEmpty(reportId)){
            try {
                ReportEntryModeUpdateReq reportEntryModeUpdateReq = new ReportEntryModeUpdateReq();
                reportEntryModeUpdateReq.setReportIdList(Sets.newHashSet(reportId));
                reportEntryModeUpdateReq.setEntryMode(ReportEntryModeEnum.REPORT_TEST_LINE.getCode());
                reportEntryModeUpdateReq.setToken(testMatrixProcessReq.getToken());
                BaseResponse<Boolean> booleanBaseResponse = reportService.updateReportEntryMode(reportEntryModeUpdateReq);
                ReportTestResultStatusUpdateReq reportTestResultStatusUpdateReq = new ReportTestResultStatusUpdateReq();
                reportTestResultStatusUpdateReq.setOrderNo(orderNo);
                reportTestResultStatusUpdateReq.setReportIdList(Sets.newHashSet(reportId));
                reportTestResultStatusUpdateReq.setEntryMode(testMatrixProcessReq.getEntryMode());
                reportTestResultStatusUpdateReq.setToken(testMatrixProcessReq.getToken());
                reportService.updateReportTestResultStatus(reportTestResultStatusUpdateReq);
                //重新计算TL 关联的Report的TL Completed Falg
                ReportExtForTLUpdateReq reportExtForTLUpdateReq = new ReportExtForTLUpdateReq();
                reportExtForTLUpdateReq.setReportIdList(reportIdList);
                reportExtForTLUpdateReq.setToken(testMatrixProcessReq.getToken());
                BaseResponse<Boolean> baseResponse = reportDomainService.updateReportExtForTL(reportExtForTLUpdateReq);
                if(DataEntryEventEnums.checkEvent(matrixAction,DataEntryEventEnums.Validate)) {
                    //validate时查询report的状态，如果是new，更新Reviewer
                    updateReviewer(regionAccount, labCode, reportId);
                }
            } catch (Exception e) {
                    log.error("handle DataEntry ReportEntry error:{}",e.getMessage());
            }
        }

        return BaseResponse.newSuccessInstance(true);
    }

    private void updateReviewer(String regionAccount, String labCode, String reportId) {
        if (Func.isEmpty(regionAccount) || Func.isEmpty(labCode) || Func.isEmpty(reportId)){
            log.error("Data Entry Update Reviewer Param Is Null {}-{}-{}",reportId,regionAccount,labCode);
            return;
        }
        // validate时查询report的状态，如果是new，更新Reviewer
        ReportPO reportPO = reportService.getById(reportId);
        if (Func.isNotEmpty(reportPO) && ReportStatus.check(reportPO.getReportStatus(), ReportStatus.New)){
            // 查询签名信息
            String sginInfo = reportDomainService.getSignatureInfo(regionAccount, labCode, SignTypeEnums.REVIEWER.getCode());
            if(Func.isNotEmpty(sginInfo)){
                ReportPO report = new ReportPO();
                report.setId(reportId);
                report.setReviewerBy(regionAccount);
                report.setReviewer(sginInfo);
                reportService.updateById(report);
            }
        }

    }
}
