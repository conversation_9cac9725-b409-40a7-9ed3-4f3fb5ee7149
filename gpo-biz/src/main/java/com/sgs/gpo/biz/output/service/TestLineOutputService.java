package com.sgs.gpo.biz.output.service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sgs.customer.facade.domain.rsp.CustomerGroupRsp;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.enums.YesOrNo;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.framework.model.enums.TestLineStatus;
import com.sgs.framework.model.enums.TestLineType;
import com.sgs.framework.model.report.template.AccreditationBO;
import com.sgs.framework.model.test.condition.conditiongroup.v2.ConditionGroupBO;
import com.sgs.framework.model.test.pp.PPLanguageBO;
import com.sgs.framework.model.test.pp.pptestline.v2.PPTestLineRelHeaderBO;
import com.sgs.framework.model.test.pp.pptestline.v2.RootPPRelBO;
import com.sgs.framework.model.test.testline.TestLineLanguageBO;
import com.sgs.framework.model.test.testline.v2.CombinedConditionBO;
import com.sgs.framework.model.test.testline.v2.TestLineBO;
import com.sgs.framework.model.test.testline.v2.TestLineSampleBO;
import com.sgs.framework.model.test.testmatrix.v2.TestMatrixBO;
import com.sgs.framework.model.trims.labsection.LabSectionBO;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.framework.tool.utils.StringPool;
import com.sgs.gpo.biz.output.context.AssembleDataContext;
import com.sgs.gpo.core.annotation.Output;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.enums.OutPutType;
import com.sgs.gpo.domain.service.otsnotes.testdata.ITestDataHeaderService;
import com.sgs.gpo.domain.service.otsnotes.testline.ITestLineDomainService;
import com.sgs.gpo.domain.service.setting.buparam.IBUParam;
import com.sgs.gpo.facade.model.otsnotes.testdata.dto.TLDataEntryDTO;
import com.sgs.gpo.facade.model.otsnotes.testdata.req.TLDataHeaderQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import com.sgs.gpo.integration.customer.CustomerClient;
import com.sgs.gpo.integration.sodanotes.SodaNotesClient;
import com.sgs.gpo.integration.sodanotes.req.QueryConditionListReq;
import com.sgs.gpo.integration.sodanotes.rsp.QueryConditionListRsp;
import com.sgs.gpo.integration.trims.TrimsClient;
import com.sgs.gpo.integration.trims.req.QueryTrimsAccreditationReq;
import com.sgs.gpo.integration.trims.rsp.TrimsAccreditationRsp;
import com.sgs.trimslocal.facade.model.artifact.req.PpArtifactInfoReq;
import com.sgs.trimslocal.facade.model.artifact.rsp.PpArtifactInfoRsp;
import com.sgs.trimslocal.facade.model.artifact.rsp.PpArtifactRelLangInfoRsp;
import com.sgs.trimslocal.facade.model.pp.req.GetPpInfoReq;
import com.sgs.trimslocal.facade.model.pp.rsp.GetPpBaseInfoRsp;
import com.sgs.trimslocal.facade.model.testline.req.GetTestLineBaseInfoReq;
import com.sgs.trimslocal.facade.model.testline.rsp.GetTestLineBaseInfoRsp;
import com.sgs.trimslocal.facade.model.testline.rsp.TestLineBaseInfoLanguagesRsp;
import com.sgs.trimslocal.facade.model.workinginstruction.req.WorkingInstructionReq;
import com.sgs.trimslocal.facade.model.workinginstruction.rsp.WorkingInstructionRsp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 输出标准的TestLine数据集合
 */
@Output(order = 9,
        output = {OutPutType.MASTER_LIST, OutPutType.COMPONENT_LIST, OutPutType.REPORT,OutPutType.PRELIM_REPORT,OutPutType.RD},
        attribute = "TestLineList")
@Slf4j
@Service
public class TestLineOutputService extends BaseOutputService<List<TestLineBO>> {

    @Autowired
    private SodaNotesClient sodaNotesClient;
    @Autowired
    private TrimsClient trimsClient;
    @Autowired
    private CustomerClient customerClient;
    @Autowired
    private ITestLineDomainService testLineDomainService;
    @Autowired
    private IBUParam ibuParam;
    @Autowired
    private ITestDataHeaderService testDataHeaderService;

    @Override
    protected List<TestLineBO> getData(AssembleDataContext context) {
        log.info("TestLineListService is running");
        List<TestLineBO> testLineBOList = Lists.newArrayList();
        if (Func.isEmpty(context.getGpnOrderId())) {
            return testLineBOList;
        }
        if (Func.isEmpty(context.getOrderTestLineMaps())) {
            context.setOrderTestLineMaps(Maps.newHashMap());
        }
        OrderTestLineReq orderTestLineReq = new OrderTestLineReq();
        orderTestLineReq.setOrderId(context.getGpnOrderId());
        orderTestLineReq.setCrossDbQuery(false);
        if (Func.isNotEmpty(context.getTestLineInstanceIds())) {
            orderTestLineReq.setTestLineInstanceIdList(context.getTestLineInstanceIds());
        }
        if (Func.isNotEmpty(context.getJobNo())) {
            orderTestLineReq.setJobNo(context.getJobNo());
        }
        orderTestLineReq.setMergePP(context.isMergePP());
        if(OutPutType.check(context.getPrintType(),OutPutType.REPORT,OutPutType.PRELIM_REPORT,OutPutType.RD)){
            orderTestLineReq.setCaller(Constants.OBJECT.REPORT.NAME);
        }
        orderTestLineReq.setBaseQuery(context.isSlMaster());
        BaseResponse<List<TestLineBO>> testLineBOListRes = testLineDomainService.queryBO(orderTestLineReq);
        if (testLineBOListRes.isFail() || Func.isEmpty(testLineBOListRes.getData())) {
            return testLineBOList;
        }
        if(OutPutType.check(context.getPrintType(), OutPutType.REPORT)){
            List<TestLineBO> testLineBOS = testLineBOListRes.getData().stream().filter(testLineBO -> !TestLineType.check(testLineBO.getHeader().getTestLineType(), TestLineType.MAIN_PP)).collect(Collectors.toList());
            if (Func.isEmpty(testLineBOS)) {
                return testLineBOList;
            }
            testLineBOListRes.setData(testLineBOS);
        }
        LanguageType primaryLanguage = LanguageType.English;
        BaseResponse<LanguageType> primaryLanguageRsp = ibuParam.getPrimaryLanguage(ProductLineContextHolder.getProductLineCode());
        if (Func.isNotEmpty(primaryLanguageRsp)) {
            primaryLanguage = primaryLanguageRsp.getData();
        }
        List<TLDataEntryDTO> tlDataEntryDTOList = new ArrayList<>();
        if(OutPutType.check(context.getPrintType(),OutPutType.REPORT,OutPutType.PRELIM_REPORT,OutPutType.RD) && Func.isNotEmpty(context.getReportIdList()) && Func.isNotEmpty(context.getTestLineInstanceIds())){
            //调用DataEntry QueryTestDataHeader接口
            TLDataHeaderQueryReq tlDataHeaderQueryReq = new TLDataHeaderQueryReq();
            tlDataHeaderQueryReq.setTestLineInstanceIdList(context.getTestLineInstanceIds());
            tlDataHeaderQueryReq.setReportNoList(context.getReportNoList());
            tlDataHeaderQueryReq.setLabCode(context.getLabCode());
            BaseResponse<List<TLDataEntryDTO>> tLDataEntryDTORes = testDataHeaderService.selectTlDataEntryList(null, tlDataHeaderQueryReq);
            if(Func.isNotEmpty(tLDataEntryDTORes) && tLDataEntryDTORes.isSuccess() && Func.isNotEmpty(tLDataEntryDTORes.getData())){
                tlDataEntryDTOList = tLDataEntryDTORes.getData();
            }
        }

        boolean isChinese = LanguageType.check(primaryLanguage.getLanguageId(), LanguageType.Chinese);
        // 记录订单下的全集TestLine,用于数据校验
        context.getOrderTestLineMaps().put(context.getOrderNo(), Func.copy(testLineBOListRes.getData(), TestLineBO.class, TestLineBO.class));
        // TODO 先通过接口查询Soda condition数据
        QueryConditionListReq queryConditionListReq = new QueryConditionListReq();
        queryConditionListReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        queryConditionListReq.setToken(SecurityContextHolder.getSgsToken());
        queryConditionListReq.setOrderNo(context.getOrderNo());
        List<QueryConditionListRsp> sodaConditionList = sodaNotesClient.getTestLineConditionList(queryConditionListReq);

        //查询testLine对应的资质信息
        Map<Integer, List<TrimsAccreditationRsp>> testLineAccreditationMap = Maps.newHashMap();
        Map<Integer,List<TrimsAccreditationRsp>> ppAccreditationMap = Maps.newHashMap();
        if(OutPutType.check(context.getPrintType(), OutPutType.MASTER_LIST)){
            Set<Integer> testLineVersionIds = testLineBOListRes.getData().stream().map(testLineBO -> testLineBO.getId().getTestLineVersionId())
                    .collect(Collectors.toSet()).stream().collect(Collectors.toSet());
            if(Func.isNotEmpty(testLineVersionIds)){
                testLineAccreditationMap = this.getAccreditationList(testLineVersionIds, null,context.getLabId());
            }
            //查询PP对应的资质信息
            Set<Integer> ppVersionIdSets = Sets.newHashSet();
            testLineBOListRes.getData().stream().forEach(testLine->{
                if(Func.isNotEmpty(testLine.getPpTestLineRelList())){
                    testLine.getPpTestLineRelList().stream().forEach(ppTestLineRelBO ->{
                        if(Func.isNotEmpty(ppTestLineRelBO) && Func.isNotEmpty(ppTestLineRelBO.getId()) &&
                                Func.isNotEmpty(ppTestLineRelBO.getId().getPpVersionId())){
                            ppVersionIdSets.add(ppTestLineRelBO.getId().getPpVersionId());
                        }
                    });
                }
            });
            if(Func.isNotEmpty(ppVersionIdSets)){
                ppAccreditationMap = this.getAccreditationList(null, ppVersionIdSets,context.getLabId());
            }
        }

        // testLine Cutting数据
        Map<Integer, WorkingInstructionRsp> testLineMaps = this.queryCuttingRequest(context, testLineBOListRes.getData().stream().map(testLineBO -> testLineBO.getId().getTestLineVersionId())
                .collect(Collectors.toSet()));
        // TestLine Trims信息
        Map<Integer, List<GetTestLineBaseInfoRsp>> testLineBaseInfoMaps = null;
        Map<Long, List<PpArtifactInfoRsp>> ppArtifactInfoMaps = null;
        if (OutPutType.check(context.getPrintType(), OutPutType.REPORT,OutPutType.PRELIM_REPORT,OutPutType.RD)) {
            // 查询PP下维护的PPNotes
            Set<Long> ppArtifactRelIds = testLineBOListRes.getData().stream().filter(testLine -> Func.isNotEmpty(testLine.getPpTestLineRelList())
                    && Func.isNotEmpty(testLine.getPpTestLineRelList().get(0).getId()) && Func.isNotEmpty(testLine.getPpTestLineRelList().get(0).getId().getPpArtifactRelId()))
                    .map(testLine -> testLine.getPpTestLineRelList().get(0).getId().getPpArtifactRelId()).collect(Collectors.toSet());
            if (Func.isNotEmpty(ppArtifactRelIds)) {
                PpArtifactInfoReq ppArtifactInfoReq = new PpArtifactInfoReq();
                ppArtifactInfoReq.setPpArtifactRelIds(ppArtifactRelIds);
                List<PpArtifactInfoRsp> ppArtifactList = trimsClient.getPpArtifactBaseInfoList(ppArtifactInfoReq);
                if (Func.isNotEmpty(ppArtifactList)) {
                    ppArtifactInfoMaps = ppArtifactList.stream().collect(Collectors.groupingBy(PpArtifactInfoRsp::getPpArtifactRelId));
                }
            }
            testLineBaseInfoMaps = this.getTestLineBaseInfo(testLineBOListRes.getData().stream().map(testLineBO -> testLineBO.getId().getTestLineId()).collect(Collectors.toSet()));
        }
        // 打印过滤有效的数据
        testLineBOList = testLineBOListRes.getData().stream().filter(testLine -> {
            if (TestLineStatus.check(testLine.getHeader().getTestLineStatus(), TestLineStatus.Cancelled)) {
                return false;
            }
            if (OutPutType.check(context.getPrintType(), OutPutType.COMPONENT_LIST) &&
                    !Func.equalsSafe(testLine.getHeader().getProductLineAbbr(), "CChemLab")) {
                return false;
            }
            return true;
        }).collect(Collectors.toList());
        if (Func.isEmpty(testLineBOList)) {
            return Lists.newArrayList();
        }
        Map<Integer, List<GetTestLineBaseInfoRsp>> finalTestLineBaseInfoMaps = testLineBaseInfoMaps;
        Map<Long, List<PpArtifactInfoRsp>> finalPpArtifactInfoMaps = ppArtifactInfoMaps;
        List<TLDataEntryDTO> finalTlDataEntryDTOList = tlDataEntryDTOList;
        Map<Integer, List<TrimsAccreditationRsp>> finalTestLineAccreditationMap = testLineAccreditationMap;
        Map<Integer, List<TrimsAccreditationRsp>> finalPpAccreditationMap = ppAccreditationMap;
        testLineBOList.stream().forEach(localTestLine -> {
            if(Func.isNotEmpty(finalTlDataEntryDTOList)){
                TLDataEntryDTO tlDataEntryDTO = finalTlDataEntryDTOList.stream().filter(item -> Func.equalsSafe(item.getTestLineInstanceId(), localTestLine.getId().getTestLineInstanceId())).findAny().orElse(null);
                if(Func.isNotEmpty(tlDataEntryDTO)){
                    localTestLine.getHeader().setTestDataCreateDate(tlDataEntryDTO.getCreatedDate());
                }
            }
            Integer testLineId = localTestLine.getId().getTestLineId();
            Integer testLineVersionId = localTestLine.getId().getTestLineVersionId();
            Boolean isPP = Func.isNotEmpty(localTestLine.getPpTestLineRelList()) && Func.isNotEmpty(localTestLine.getPpTestLineRelList().get(0))
                    && Func.isNotEmpty(localTestLine.getPpTestLineRelList().get(0).getId().getPpArtifactRelId()) && !Func.equalsSafe(localTestLine.getPpTestLineRelList().get(0).getId().getPpArtifactRelId(),0L);
            GetTestLineBaseInfoRsp testLineBaseInfoRsp = null;
            if (Func.isNotEmpty(finalTestLineBaseInfoMaps) && finalTestLineBaseInfoMaps.containsKey(testLineId)) {
                List<GetTestLineBaseInfoRsp> testLineBaseInfoRspList = finalTestLineBaseInfoMaps.get(testLineId);
                if (Func.isNotEmpty(testLineBaseInfoRspList)) {
                    testLineBaseInfoRsp = testLineBaseInfoRspList.stream().filter(item->Func.equalsSafe(item.getTestLineVersionId(), testLineVersionId)).findAny().orElse(null);
                }
            }
            List<TestLineLanguageBO> languageList = localTestLine.getHeader().getLanguageList();
            if(isPP){
                Long ppArtifactRelId = localTestLine.getPpTestLineRelList().get(0).getId().getPpArtifactRelId();
                if (Func.isNotEmpty(finalPpArtifactInfoMaps) && finalPpArtifactInfoMaps.containsKey(ppArtifactRelId)) {
                    List<PpArtifactInfoRsp> ppArtifactInfoRspList = finalPpArtifactInfoMaps.get(ppArtifactRelId);
                    if(Func.isNotEmpty(ppArtifactInfoRspList)){
                        PpArtifactInfoRsp ppArtifactInfoRsp = ppArtifactInfoRspList.get(0);
                        String reportReferenceNotes_pptl = ppArtifactInfoRsp.getReportReferenceNoteAlias();
                        String reportReferenceNotes_pptl_CN = StringPool.EMPTY;
                        List<PpArtifactRelLangInfoRsp> ppArtifactRelLangInfoRspList = ppArtifactInfoRsp.getLanguages();
                        if(Func.isNotEmpty(testLineBaseInfoRsp)){
                            localTestLine.getHeader().setNoOfReplication(Func.toStr(testLineBaseInfoRsp.getReplicationNo()));
                        }
                        localTestLine.getHeader().setReportReferenceNotes(reportReferenceNotes_pptl);
                        // GPO2-15729,TOBE-1，ReportReferenceNotesPP内如果取不到，则取TL上的
                        //localTrims的LanguageList中只有中文，英文数据与languageList同级
                        if(Func.isNotEmpty(ppArtifactRelLangInfoRspList)){
                            PpArtifactRelLangInfoRsp ppArtifactRelLangInfoRsp_CN = ppArtifactRelLangInfoRspList.stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.Chinese)).findAny().orElse(null);
                            if(Func.isNotEmpty(ppArtifactRelLangInfoRsp_CN) && Func.isNotEmpty(ppArtifactRelLangInfoRsp_CN.getReportReferenceNoteAlias())){
                                reportReferenceNotes_pptl_CN = ppArtifactRelLangInfoRsp_CN.getReportReferenceNoteAlias();
                            }
                        }
                        //PPTL维度的reportReferenceNotes都为空时，取TL维度的
                        if (Func.isAllEmpty(reportReferenceNotes_pptl,reportReferenceNotes_pptl_CN) && Func.isNotEmpty(testLineBaseInfoRsp)) {
                            assembleReportReferenceNotesForTL(localTestLine,testLineBaseInfoRsp,isChinese);
                        }else if(Func.isNotEmpty(languageList)){
                            for (TestLineLanguageBO testLineLanguageBO : languageList) {
                                if(Func.isNotEmpty(ppArtifactRelLangInfoRspList)){
                                    PpArtifactRelLangInfoRsp ppArtifactRelLangInfoRsp = ppArtifactRelLangInfoRspList.stream().filter(item -> Func.equalsSafe(item.getLanguageId(), testLineLanguageBO.getLanguageId())).findAny().orElse(null);
                                    if(Func.isNotEmpty(ppArtifactRelLangInfoRsp) && Func.isNotEmpty(ppArtifactRelLangInfoRsp.getReportReferenceNoteAlias())){
                                        if(isChinese && LanguageType.check(ppArtifactRelLangInfoRsp.getLanguageId(),LanguageType.Chinese)){
                                            localTestLine.getHeader().setReportReferenceNotes(ppArtifactRelLangInfoRsp.getReportReferenceNoteAlias());
                                        }
                                        testLineLanguageBO.setReportReferenceNotes(ppArtifactRelLangInfoRsp.getReportReferenceNoteAlias());
                                    }else if(Func.isNotEmpty(ppArtifactInfoRsp.getReportReferenceNoteAlias())){
                                        testLineLanguageBO.setReportReferenceNotes(ppArtifactInfoRsp.getReportReferenceNoteAlias());
                                    }
                                }
                            }
                            //增补 TL没有中文 PPNotes 有中文 增补中文
                            TestLineLanguageBO chineseBO = languageList.stream().filter(e -> LanguageType.check(e.getLanguageId(),LanguageType.Chinese)).findAny().orElse(null);
                            if(Func.isEmpty(chineseBO) && Func.isNotEmpty(reportReferenceNotes_pptl_CN)){
                                TestLineLanguageBO chinese = new TestLineLanguageBO();
                                chinese.setLanguageId(LanguageType.Chinese.getLanguageId());
                                chinese.setReportReferenceNotes(reportReferenceNotes_pptl_CN);
                                localTestLine.getHeader().getLanguageList().add(chinese);
                            }
                        }
                    }
                }
            }else if (Func.isNotEmpty(testLineBaseInfoRsp)) {
                localTestLine.getHeader().setNoOfReplication(Func.toStr(testLineBaseInfoRsp.getReplicationNo()));
                assembleReportReferenceNotesForTL(localTestLine,testLineBaseInfoRsp,isChinese);
            }
            //TODO 修改为读取规则
            localTestLine.getHeader().setEditableFlag(TestLineStatus.check(localTestLine.getHeader().getTestLineStatus(), TestLineStatus.Typing, TestLineStatus.Entered) ? YesOrNo.Yes.name() : YesOrNo.No.name());

            //condition
            assembleCondition(localTestLine, sodaConditionList);
            // pretreatment 组装处理
            if (TestLineType.check(localTestLine.getHeader().getTestLineType(), TestLineType.Pretreatment)) {
                assemblePretreatment(localTestLine, context);
            }
            WorkingInstructionRsp cuttingRequest = testLineMaps.get(localTestLine.getId().getTestLineVersionId());
            if (cuttingRequest != null) {
                localTestLine.getHeader().setCuttingRequest(cuttingRequest.getMultiWorkInstructionText());
            }
            if (OutPutType.check(context.getPrintType(), OutPutType.REPORT,OutPutType.PRELIM_REPORT,OutPutType.RD)) {
                assemblePPNotes(finalTestLineBaseInfoMaps, localTestLine, finalPpArtifactInfoMaps);
                if(TestLineType.check(localTestLine.getHeader().getTestLineType(),TestLineType.SUB_PP) && Func.isNotEmpty(localTestLine.getPpTestLineRelList())){
                    localTestLine.getPpTestLineRelList().stream().forEach(item->{
                        item.getHeader().setSubPP(true);
                    });
                }
                // SubPP的场景需要查询主PP信息
                assembleRootPPInfo(localTestLine);
            }
            if(OutPutType.check(context.getPrintType(), OutPutType.MASTER_LIST)){
                // 设置TestLine的资质
                localTestLine.setAccreditationList(convertToAccreditationList(finalTestLineAccreditationMap.get(localTestLine.getId().getTestLineVersionId())));
                // 设置PP的资质
                if(Func.isNotEmpty(localTestLine.getPpTestLineRelList())){
                    localTestLine.getPpTestLineRelList().stream().forEach(ppRel->{
                        if(Func.isNotEmpty(ppRel.getId()) && Func.isNotEmpty(ppRel.getId().getPpVersionId())){
                            ppRel.getHeader().setAccreditationList(convertToAccreditationList(finalPpAccreditationMap.get(ppRel.getId().getPpVersionId())));
                        }
                    });
                }
            }
        });
        log.info("TestLineListService run end");
        // MasterList 排序
        if (OutPutType.check(context.getPrintType(), OutPutType.MASTER_LIST)) {
            if(StringUtils.equalsIgnoreCase(ProductLineContextHolder.getProductLineCode(),ProductLineType.SL.getProductLineAbbr())){
                return sortTestLineList(testLineBOList);
            }
        }
        if(StringUtils.equalsIgnoreCase(ProductLineContextHolder.getProductLineCode(), ProductLineType.SL.getProductLineAbbr())){
            return testLineBOList.stream().sorted(Comparator.comparing(item -> (Func.isEmpty(item.getId().getTestLineId()) ? 0 : item.getId().getTestLineId()), Comparator.nullsLast(Integer::compareTo)))
                    .collect(Collectors.toList());
        }
        return testLineBOList;
    }


    private List<AccreditationBO> convertToAccreditationList(List<TrimsAccreditationRsp> testLineAccreditationList){
        List<AccreditationBO> accreditationBOList = Lists.newArrayList();
        if(Func.isEmpty(testLineAccreditationList)){
            return accreditationBOList;
        }
        testLineAccreditationList.stream().forEach(item->{
            if(Func.isNotEmpty(item.getAccreditationTypeItems())){
                item.getAccreditationTypeItems().stream().forEach(accreditation ->{
                    AccreditationBO accreditationBO = new AccreditationBO();
                    accreditationBO.setAccreditationTypeName(accreditation.getAccreditationType());
                    accreditationBO.setAccreditationTypeId(accreditation.getAccreditationTypeId());
                    accreditationBOList.add(accreditationBO);
                } );
            }
        });
        return accreditationBOList;
    }

    private void assembleReportReferenceNotesForTL(TestLineBO localTestLine,GetTestLineBaseInfoRsp testLineBaseInfoRsp, boolean isChinese){
        if(Func.isAnyEmpty(localTestLine,testLineBaseInfoRsp)){
            return;
        }
        List<TestLineLanguageBO> languageList = localTestLine.getHeader().getLanguageList();
        List<TestLineBaseInfoLanguagesRsp> testLineBaseInfoLanguagesRspList = testLineBaseInfoRsp.getLanguages();
        localTestLine.getHeader().setReportReferenceNotes(testLineBaseInfoRsp.getReportReferenceNote());
        if(Func.isNoneEmpty(languageList,testLineBaseInfoRsp.getLanguages())){
            for (TestLineLanguageBO testLineLanguageBO : languageList) {
                TestLineBaseInfoLanguagesRsp testLineBaseInfoLanguagesRsp = testLineBaseInfoLanguagesRspList.stream().filter(item -> Func.equalsSafe(item.getLanguageId(), testLineLanguageBO.getLanguageId())).findAny().orElse(null);
                if(Func.isNotEmpty(testLineBaseInfoLanguagesRsp) && Func.isNotEmpty(testLineBaseInfoLanguagesRsp.getReportReferenceNote())){
                    if(isChinese && LanguageType.check(testLineBaseInfoLanguagesRsp.getLanguageId(),LanguageType.Chinese)){
                        localTestLine.getHeader().setReportReferenceNotes(testLineBaseInfoLanguagesRsp.getReportReferenceNote());
                    }
                    testLineLanguageBO.setReportReferenceNotes(testLineBaseInfoLanguagesRsp.getReportReferenceNote());
                }else{
                    testLineLanguageBO.setReportReferenceNotes(testLineBaseInfoRsp.getReportReferenceNote());
                }
            }
        }
    }

    private void assembleRootPPInfo(TestLineBO localTestLine){
        if(Func.isEmpty(localTestLine.getPpTestLineRelList()) || Func.isEmpty(localTestLine.getPpTestLineRelList().get(0).getHeader().getRootPP())
                || Func.isEmpty(localTestLine.getPpTestLineRelList().get(0).getHeader().getRootPP().getPpAid())){
            return;
        }
        if(Func.isEmpty(localTestLine.getPpTestLineRelList().get(0).getId().getRootPPBaseId())){
            return;
        }
        Long rootPPBaseId = localTestLine.getPpTestLineRelList().get(0).getId().getRootPPBaseId();
        if(Func.isEmpty(rootPPBaseId)){
            return;
        }
        Set<Long> ppBaseIds = Sets.newHashSet();
        ppBaseIds.add(rootPPBaseId);
        GetPpInfoReq ppInfoReq = new GetPpInfoReq();
        ppInfoReq.setCallerBU(ProductLineContextHolder.getProductLineCode());
        ppInfoReq.setPpBaseIds(ppBaseIds);
        List<Integer> languageIds = Lists.newArrayList();
        languageIds.add(LanguageType.English.getLanguageId());
        languageIds.add(LanguageType.Chinese.getLanguageId());
        ppInfoReq.setLanguageIds(Lists.newArrayList(languageIds));
        List<GetPpBaseInfoRsp> ppBaseInfoRspList = trimsClient.getPpBaseInfo(ppInfoReq).getData();
        if(Func.isEmpty(ppBaseInfoRspList)){
            return;
        }
        GetPpBaseInfoRsp ppBaseInfoRsp = ppBaseInfoRspList.get(0);
        RootPPRelBO rootPP = localTestLine.getPpTestLineRelList().get(0).getHeader().getRootPP();
        rootPP.setPpNo(ppBaseInfoRsp.getPpNo());
        rootPP.setPpVersionId(ppBaseInfoRsp.getPpVersionId());
    }

    private Map<Integer, List<GetTestLineBaseInfoRsp>> getTestLineBaseInfo(Set<Integer> testLineIds) {
        Map<Integer, List<GetTestLineBaseInfoRsp>> testLineBaseInfoMaps = Maps.newHashMap();
        if (Func.isEmpty(testLineIds)) {
            return testLineBaseInfoMaps;
        }
        GetTestLineBaseInfoReq reqObject = new GetTestLineBaseInfoReq();
        reqObject.setTestLineIds(testLineIds);
        List<Integer> languageIds = Lists.newArrayList();
        languageIds.add(LanguageType.English.getLanguageId());
        languageIds.add(LanguageType.Chinese.getLanguageId());
        reqObject.setLanguageIds(Lists.newArrayList(languageIds));
        List<GetTestLineBaseInfoRsp> testLineBaseInfoList = trimsClient.getTestLineBaseInfo(reqObject);
        if (Func.isNotEmpty(testLineBaseInfoList)) {
            testLineBaseInfoMaps = testLineBaseInfoList.stream().collect(Collectors.groupingBy(GetTestLineBaseInfoRsp::getTestLineId));
        }
        return testLineBaseInfoMaps;
    }

    /**
     * 查询资质信息
     * @param testLineVersionIds
     * @param ppVersionIds
     * @param labId
     * @return
     */
    private Map<Integer, List<TrimsAccreditationRsp>> getAccreditationList(Set<Integer> testLineVersionIds, Set<Integer> ppVersionIds ,Integer labId) {
        Map<Integer, List<TrimsAccreditationRsp>> accreditationMaps = Maps.newHashMap();
        if (Func.isEmpty(testLineVersionIds) && Func.isEmpty(ppVersionIds)) {
            return accreditationMaps;
        }
        QueryTrimsAccreditationReq  reqObject = new QueryTrimsAccreditationReq();
        reqObject.setLabIds(Sets.newHashSet(labId));
        // 传哪个对象的值就查哪个对象的资质
        if(Func.isNotEmpty(testLineVersionIds)){
            reqObject.setTlVersionIdentifiers(testLineVersionIds);
        }
        if(Func.isNotEmpty(ppVersionIds)){
            reqObject.setPpVersionIdentifiers(ppVersionIds);
        }
        BaseResponse<List<TrimsAccreditationRsp>> testLineBaseInfoRsp = trimsClient.getConfigAccreditation(reqObject);
        if (Func.isNotEmpty(testLineBaseInfoRsp.getData())) {
            if(Func.isNotEmpty(testLineVersionIds)){
                accreditationMaps = testLineBaseInfoRsp.getData().stream().collect(Collectors.groupingBy(TrimsAccreditationRsp::getTlVersionIdentifier));
            }
            if(Func.isNotEmpty(ppVersionIds)){
                accreditationMaps = testLineBaseInfoRsp.getData().stream().collect(Collectors.groupingBy(TrimsAccreditationRsp::getPpVersionIdentifier));
            }
        }
        return accreditationMaps;
    }

    private void assemblePPNotes(Map<Integer, List<GetTestLineBaseInfoRsp>> testLineBaseInfoMaps, TestLineBO localTestLine, Map<Long, List<PpArtifactInfoRsp>> ppArtifactInfoMaps) {
        if (Func.isEmpty(testLineBaseInfoMaps) && Func.isEmpty(ppArtifactInfoMaps)) {
            return;
        }
        // 判断是否是通过PP添加的TestLine
        Boolean isPP = Func.isNotEmpty(localTestLine.getPpTestLineRelList()) && Func.isNotEmpty(localTestLine.getPpTestLineRelList().get(0))
                && Func.isNotEmpty(localTestLine.getPpTestLineRelList().get(0).getId().getPpArtifactRelId()) && !Func.equalsSafe(localTestLine.getPpTestLineRelList().get(0).getId().getPpArtifactRelId(),0L);
        String ppNotesEn = null;
        String ppNotesCn = null;
        if (isPP) {
            // PP 添加的TestLine取PP下的ppNotes
            Long ppArtifactRelId = localTestLine.getPpTestLineRelList().get(0).getId().getPpArtifactRelId();
            if (Func.isNotEmpty(ppArtifactInfoMaps) && ppArtifactInfoMaps.containsKey(ppArtifactRelId)) {
                List<PpArtifactInfoRsp> ppArtifactInfoRspList = ppArtifactInfoMaps.get(ppArtifactRelId);
                if(Func.isNotEmpty(ppArtifactInfoRspList)){
                    PpArtifactInfoRsp ppArtifactInfoRsp = ppArtifactInfoRspList.get(0);
                    if(Func.isNotEmpty(ppArtifactInfoRsp.getPpNotesAlias())){
                        ppNotesEn = ppArtifactInfoRsp.getPpNotesAlias();
                    }
                    if(Func.isNotEmpty(ppArtifactInfoRsp.getLanguages())){
                        PpArtifactRelLangInfoRsp ppArtifactRelCn = ppArtifactInfoRsp.getLanguages().stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.Chinese))
                                .findAny().orElse(null);
                        if(Func.isNotEmpty(ppArtifactRelCn)){
                            ppNotesCn = ppArtifactRelCn.getPpNotesAlias();
                        }
                    }
                }
            }
        } else {
            // 直接添加的TestLine取TL上维护的ppNotes
            if (Func.isNotEmpty(testLineBaseInfoMaps) && testLineBaseInfoMaps.containsKey(localTestLine.getId().getTestLineId())) {
                List<GetTestLineBaseInfoRsp> testLineBaseInfoList = testLineBaseInfoMaps.get(localTestLine.getId().getTestLineId());
                if (Func.isNotEmpty(testLineBaseInfoList)) {
                    GetTestLineBaseInfoRsp testLineBaseInfo = testLineBaseInfoList.get(0);
                    if (Func.isNotEmpty(testLineBaseInfo.getPpNotes())) {
                        ppNotesEn = testLineBaseInfo.getPpNotes();
                    }
                    if (Func.isNotEmpty(testLineBaseInfo.getLanguages())) {
                        TestLineBaseInfoLanguagesRsp testLineBaseInfoCn = testLineBaseInfo.getLanguages().stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.Chinese))
                                .findAny().orElse(null);
                        if (Func.isNotEmpty(testLineBaseInfoCn)) {
                            ppNotesCn = testLineBaseInfoCn.getPpNotes();
                        }
                    }
                }

            }
        }
        // 设置PP notes
        if (Func.isNotEmpty(localTestLine.getPpTestLineRelList())) {
            PPTestLineRelHeaderBO header = localTestLine.getPpTestLineRelList().get(0).getHeader();
            header.setPpNotes(Func.isNotEmpty(ppNotesEn)?ppNotesEn:ppNotesCn);
            // 多语言
            PPLanguageBO ppLanguageEN = null;
            PPLanguageBO ppLanguageCN = null;
            if (Func.isNotEmpty(ppNotesEn)) {
                ppLanguageEN = new PPLanguageBO();
                ppLanguageEN.setLanguageId(LanguageType.English.getLanguageId());
                ppLanguageEN.setPpNotes(ppNotesEn);
            }
            // CN
            if (Func.isNotEmpty(ppNotesCn)) {
                ppLanguageCN = new PPLanguageBO();
                ppLanguageCN.setLanguageId(LanguageType.Chinese.getLanguageId());
                ppLanguageCN.setPpNotes(ppNotesCn);
            }
            if (Func.isEmpty(header.getLanguageList())) {
                header.setLanguageList(Lists.newArrayList());
            }
            PPLanguageBO finalPpLanguageEN = ppLanguageEN;
            PPLanguageBO finalPpLanguageCN = ppLanguageCN;
            // 存在更新
            header.getLanguageList().stream().forEach(ppLanguageBO -> {
                if (LanguageType.check(ppLanguageBO.getLanguageId(), LanguageType.Chinese) && Func.isNotEmpty(finalPpLanguageCN)) {
                    ppLanguageBO.setPpNotes(finalPpLanguageCN.getPpNotes());
                }
                if (LanguageType.check(ppLanguageBO.getLanguageId(), LanguageType.English) && Func.isNotEmpty(finalPpLanguageEN)) {
                    ppLanguageBO.setPpNotes(finalPpLanguageEN.getPpNotes());
                }
            });
            // 不存在插入
            if (Func.isNotEmpty(ppLanguageCN)) {
                PPLanguageBO ppLanguageCNExit = header.getLanguageList().stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.Chinese)).findAny().orElse(null);
                if (Func.isEmpty(ppLanguageCNExit)) {
                    header.getLanguageList().add(ppLanguageCN);
                }
            }
            if (Func.isNotEmpty(ppLanguageEN)) {
                PPLanguageBO ppLanguageENExit = header.getLanguageList().stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.English)).findAny().orElse(null);
                if (Func.isEmpty(ppLanguageENExit)) {
                    header.getLanguageList().add(ppLanguageEN);
                }
            }
        }
    }

    /**
     * MasterList 排序
     * labSectionSeq testLineName testLineId
     *
     * @param testLineList
     * @return
     */
    private List<TestLineBO> sortTestLineList(List<TestLineBO> testLineList) {
        if (Func.isNotEmpty(testLineList)) {
            testLineList.stream().forEach(testLine -> {
                List<LabSectionBO> labSectionList = testLine.getLabSectionList();
                if (Func.isNotEmpty(labSectionList)) {
                    testLine.setSequence1(labSectionList.get(0).getLabSectionSeq());
                } else {
                    testLine.setSequence1(0);
                }
                TestLineLanguageBO englishTestLine = testLine.getHeader().getLanguageList().stream().filter(lan -> LanguageType.check(lan.getLanguageId(), LanguageType.English))
                        .findAny().orElse(null);
                if (Func.isNotEmpty(englishTestLine)) {
                    testLine.setSequence2(englishTestLine.getEvaluationName());
                }
                testLine.setSequence3(testLine.getId().getTestLineId());
            });
            testLineList.sort(Comparator.comparing(TestLineBO::getSequence1)
                    .thenComparing(TestLineBO::getSequence2)
                    .thenComparing(TestLineBO::getSequence3));
        }
        return testLineList;
    }

    /**
     * Report TestLine 排序
     * order by
     * ptlr.Seq,
     * ttli.TestItemNo ,
     * ptlr.CreatedDate
     *
     * @param testLineList
     * @return
     */
    private List<TestLineBO> sortTestLineListForReport(List<TestLineBO> testLineList) {
        if (Func.isNotEmpty(testLineList)) {
            testLineList.stream().forEach(testLine -> {
                //1 pp_tl_rel.Seq
                if (Func.isNotEmpty(testLine.getPpTestLineRelList()) && Func.isNotEmpty(testLine.getPpTestLineRelList().get(0).getHeader())) {
                    testLine.setSequence1(testLine.getPpTestLineRelList().get(0).getHeader().getSeq());
                } else {
                    testLine.setSequence1(0);
                }
                //2 tl.TestItemNo
                testLine.setSequence2(testLine.getId().getTestItemNo());
            });
            testLineList.sort(Comparator.comparing(TestLineBO::getSequence1,Comparator.nullsLast(Integer::compareTo))
                    .thenComparing(TestLineBO::getSequence2,Comparator.nullsLast(String::compareTo)));
        }
        return testLineList;
    }

    private void assemblePretreatment(TestLineBO testLineBO, AssembleDataContext context) {
        List<TestLineSampleBO> testLineSampleList = Func.isAnyEmpty(testLineBO.getRelationship(),testLineBO.getRelationship().getParallel())?null:
        testLineBO.getRelationship().getParallel().getTestSampleList();
        List<String> pretreatmentList = Lists.newArrayList();
        List<ConditionGroupBO> conditionGroupList = context.getConditionGroupList();
        List<TestMatrixBO> testMatrixList = context.getTestMatrixList();
        // NC的Pretreatment不会AssignSample
        if(Func.isEmpty(testLineSampleList)){
            List<String> samplePretreatmentList = Lists.newArrayList();
            samplePretreatmentList.add(testLineBO.getId().getTestLineId().toString());
            if (Func.isNotEmpty(testLineBO.getCitation().getCitationName())) {
                samplePretreatmentList.add(testLineBO.getCitation().getCitationName());
            }
            pretreatmentList.add(samplePretreatmentList.stream().collect(Collectors.joining(StringPool.COLON)));
        }else{
            // AssignSample 后按照Sample组装
            testLineSampleList.stream().forEach(sample -> {
                // 过滤当前Matrix
                List<TestMatrixBO> thisTestMatrixList = testMatrixList.stream().filter(matrix ->
                        Func.equalsSafe(matrix.getRelationship().getParent().getTestLine().getTestLineInstanceId(), testLineBO.getId().getTestLineInstanceId())
                                && Func.equalsSafe(matrix.getRelationship().getParent().getTestSample().getTestSampleInstanceId(), sample.getTestSampleInstanceId())).collect(Collectors.toList());
                if(Func.isNotEmpty(thisTestMatrixList)){
                    // 按照Matrix多一次遍历
                    thisTestMatrixList.stream().forEach(testMatrix -> {
                        List<String> samplePretreatmentList = Lists.newArrayList();
                        samplePretreatmentList.add(testLineBO.getId().getTestLineId().toString());
                        samplePretreatmentList.add(sample.getTestSampleNo());
                        if (Func.isNotEmpty(testLineBO.getCitation().getCitationName())) {
                            samplePretreatmentList.add(testLineBO.getCitation().getCitationName());
                        }
                        if(Func.isNotEmpty(conditionGroupList)){
                            ConditionGroupBO conditionGroup = conditionGroupList.stream().filter(condition ->
                                    Func.isNotEmpty(testMatrix.getRelationship().getParent().getTestConditionGroupList()) &&
                                            Func.equalsSafe(condition.getId().getConditionGroupId(), testMatrix.getRelationship().getParent().getTestConditionGroupList().get(0).getTestConditionGroupId())).findAny().orElse(null);
                            if (Func.isNotEmpty(conditionGroup)) {
                                samplePretreatmentList.add(conditionGroup.getHeader().getCombinedConditionDescription());
                            }
                        }
                        pretreatmentList.add(samplePretreatmentList.stream().collect(Collectors.joining(StringPool.COLON)));
                    });
                }else {
                    List<String> samplePretreatmentList = Lists.newArrayList();
                    samplePretreatmentList.add(testLineBO.getId().getTestLineId().toString());
                    samplePretreatmentList.add(sample.getTestSampleNo());
                    if (Func.isNotEmpty(testLineBO.getCitation().getCitationName())) {
                        samplePretreatmentList.add(testLineBO.getCitation().getCitationName());
                    }
                    pretreatmentList.add(samplePretreatmentList.stream().collect(Collectors.joining(StringPool.COLON)));
                }

            });
        }
        testLineBO.setPretreatment(pretreatmentList.stream().collect(Collectors.joining("</br>")));
    }

    //TODO 先通过调用接口实现
    private void assembleCondition(TestLineBO testLineBO, List<QueryConditionListRsp> sodaConditionList) {
        if (Func.isEmpty(sodaConditionList)) {
            return;
        }
        List<QueryConditionListRsp> conditionList = sodaConditionList.stream().filter(item -> Func.equalsSafe(item.getTestLineInstanceId()
                , testLineBO.getId().getTestLineInstanceId())).collect(Collectors.toList());
        if (Func.isNotEmpty(conditionList)) {
            List<CombinedConditionBO> combinedConditionList = Lists.newArrayList();
            conditionList.stream().forEach(condition->{
                CombinedConditionBO combinedConditionBO = new CombinedConditionBO();
                combinedConditionBO.setCombinedCondition(condition.getConditions());
                String sampleNos = condition.getSampleNos();
                if(Func.isNotEmpty(sampleNos) && sampleNos.contains("</font>")){
                    sampleNos = sampleNos.replace("</font>","@</font>");
                }
                combinedConditionBO.setSampleNos(sampleNos);
                combinedConditionList.add(combinedConditionBO);
            });
            testLineBO.setCombinedConditionList(combinedConditionList);
        }
    }


    /**
     * Soda逻辑，查询testLine对应的CuttingRequest
     *
     * @param context
     * @param testLineVersionIds
     * @return
     */
    private Map<Integer, WorkingInstructionRsp> queryCuttingRequest(AssembleDataContext context, Set<Integer> testLineVersionIds) {
        String customerGroupCode = context.getCustomerGroupCode();

        Map<Integer, WorkingInstructionRsp> testLineMaps = Maps.newHashMap();
        WorkingInstructionReq reqObject = new WorkingInstructionReq();
        reqObject.setCustomerAccountId(0);
        if (StringUtils.isNotBlank(customerGroupCode)) {
            CustomerGroupRsp customerGroupRsp = customerClient.getCustomerGroupInfo(customerGroupCode);
            if (Func.isNotEmpty(customerGroupRsp) && Func.isNotEmpty(customerGroupRsp.getAccountId())) {
                reqObject.setCustomerAccountId(Integer.valueOf(customerGroupRsp.getAccountId()));
            }
            reqObject.setCustomerGroupCode(customerGroupCode);
        }
        reqObject.setTestLineVersionIds(testLineVersionIds);
        reqObject.setProductLineId(context.getProductLineId());
        int productLineId = reqObject.getProductLineId();
        int customerAccountId = reqObject.getCustomerAccountId();

        BaseResponse<List<WorkingInstructionRsp>> workingInstructionRsp = trimsClient.getWorkingInstructionList(reqObject);
        List<WorkingInstructionRsp> finalList = Lists.newArrayList();
        for (Integer testLineVersionId : testLineVersionIds) {
            finalList.addAll(filter(workingInstructionRsp.getData().stream()
                    .filter(l -> Func.equalsSafe(l.getTestLineVersionId(), testLineVersionId))
                    .collect(Collectors.toList()), reqObject.getCustomerAccountId()));
        }

        boolean isEnglish = StringUtils.isNotBlank(context.getLabCode()) && context.getLabCode().toUpperCase().contains("HK SL");

        for (WorkingInstructionRsp cutting : finalList) {
            if (Func.isNotEmpty(cutting.getLanguages())) {
                cutting.setMultiWorkInstructionText(cutting.getLanguages().get(0).getWorkingInstructionText());
                cutting.setMultiCategoryName(cutting.getLanguages().get(0).getCategoryName());
            }
            if (isEnglish) {
                cutting.setMultiWorkInstructionText(cutting.getWorkingInstructionText());
            }
            Integer testLineVersionId = cutting.getTestLineVersionId();
            if (!testLineMaps.containsKey(testLineVersionId)) {
                testLineMaps.put(testLineVersionId, cutting);
                continue;
            }
            WorkingInstructionRsp cuttingRequest = testLineMaps.get(testLineVersionId);
            if (Func.equalsSafe(cuttingRequest.getProductLineId(), productLineId) && Func.equalsSafe(cuttingRequest.getCustomerAccountId(), customerAccountId)) {
                continue;
            }
            if ((Func.equalsSafe(cutting.getProductLineId(), productLineId) && Func.equalsSafe(cutting.getCustomerAccountId(), customerAccountId))
                    || (customerAccountId > 0 && Func.equalsSafe(cutting.getCustomerAccountId(), customerAccountId))
            ) {
                testLineMaps.put(testLineVersionId, cutting);
                continue;
            }
            if (cuttingRequest.getCustomerAccountId() > 0) {
                continue;
            }
            if (productLineId > 0 && Func.equalsSafe(cutting.getProductLineId(), productLineId)) {
                testLineMaps.put(testLineVersionId, cutting);
                continue;
            }

        }
        return testLineMaps;
    }

    private List<WorkingInstructionRsp> filter(List<WorkingInstructionRsp> list, int customerAccountId) {
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        List<WorkingInstructionRsp> filterList = list.stream().filter(workingInstructionRsp -> Func.equalsSafe(workingInstructionRsp.getCustomerAccountId(), customerAccountId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterList)) {
            filterList = list.stream().filter(workingInstructionRsp -> Func.equalsSafe(workingInstructionRsp.getCustomerAccountId(), 0)).collect(Collectors.toList());
        }
        return filterList;
    }


}
