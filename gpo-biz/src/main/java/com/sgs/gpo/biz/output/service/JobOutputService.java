package com.sgs.gpo.biz.output.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.model.enums.JobStatus;
import com.sgs.framework.model.test.execution.v2.job.JobBO;
import com.sgs.framework.model.test.execution.v2.job.JobTestLineRelBO;
import com.sgs.framework.model.trims.labsection.LabSectionBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.biz.output.context.AssembleDataContext;
import com.sgs.gpo.core.annotation.Output;
import com.sgs.gpo.core.enums.OutPutType;
import com.sgs.gpo.domain.service.otsnotes.job.IJobDomainService;
import com.sgs.gpo.facade.model.job.req.JobQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 输出标准的Job数据集合
 */
@Output(order = 3,
        output = {OutPutType.MASTER_LIST,OutPutType.RD},
        attribute = "JobList")
@Slf4j
@Service
public class JobOutputService extends BaseOutputService<List<JobBO>> {

    @Autowired
    private IJobDomainService jobDomainService;

    @Override
    protected List<JobBO> getData(AssembleDataContext context) {
        log.info("JobOutputService is running");
        List<JobBO> jobBOList = Lists.newArrayList();
        if (Func.isEmpty(context.getGpnOrderId())) {
            return jobBOList;
        }
        if (Func.isEmpty(context.getJobTestLineMaps())) {
            context.setJobTestLineMaps(new HashMap<>());
        }
        // 1.执行JOB查询
        JobQueryReq request = new JobQueryReq();
        request.setLab(SystemContextHolder.getLab());
        request.setOrderNoList(Sets.newHashSet(context.getOrderNo()));
        BaseResponse<List<JobBO>> jobBoRes = jobDomainService.queryBO(request);
        if (jobBoRes.isFail() || Func.isEmpty(jobBoRes.getData())) {
            return jobBOList;
        }
        context.getJobTestLineMaps().put(context.getOrderNo(), jobBoRes.getData().stream().filter(jobBO -> Func.isNotEmpty(jobBO.getRelationship().getChildren().getTestLineList())).
                flatMap(job->job.getRelationship().getChildren().getTestLineList().stream()).collect(Collectors.toList()));
        // 打印时需要按照场景过滤Job数据
        jobBOList = jobBoRes.getData().stream().filter(job -> {
            if (JobStatus.check(job.getHeader().getJobStatus(), JobStatus.Cancelled)) {
                return false;
            }
            if (Func.isNotEmpty(context.getTestLineInstanceIds())) {
                List<JobTestLineRelBO> jobTestLineList = job.getRelationship().getChildren().getTestLineList();
                if (Func.isEmpty(job.getRelationship().getChildren().getTestLineList())) {
                    return false;
                }
                if (Func.isEmpty(jobTestLineList.stream().filter(item -> Func.isNotEmpty(item.getTestLineInstanceId()) &&
                        context.getTestLineInstanceIds().contains(item.getTestLineInstanceId())).findAny().orElse(null))) {
                    return false;
                }
            }
            if(Func.isNotEmpty(context.getLabSectionList())){
                List<String> labSectionList = context.getLabSectionList();
                if(Func.isEmpty(labSectionList.stream()
                        .filter(item -> Func.equalsSafe(item, "all")).collect(Collectors.toList()))){
                    if(Func.isEmpty(job.getLabSection()) || !labSectionList.contains(job.getLabSection().getLabSectionId().toString())){
                        return false;
                    }
                }
            }
            return true;
        }).collect(Collectors.toList());
        log.info("JobOutputService run end");
        return jobBOList.stream()
                .sorted(Comparator.comparing(
                        JobBO::getLabSection,
                        Comparator.nullsLast(
                                Comparator.comparing(
                                        LabSectionBO::getLabSectionSeq,
                                        Comparator.nullsLast(Integer::compareTo)
                                )
                        )
                ))
                .collect(Collectors.toList());
    }

}
