package com.sgs.gpo.biz.intergration.rd.command;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.beust.jcommander.internal.Maps;
import com.google.common.collect.Sets;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.model.Lab;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.log.SystemLogHelper;
import com.sgs.framework.log.enums.SystemLogType;
import com.sgs.framework.log.model.SystemLog;
import com.sgs.framework.model.common.attachment.AttachmentBO;
import com.sgs.framework.model.common.customer.CustomerBO;
import com.sgs.framework.model.common.customer.CustomerLanguageBO;
import com.sgs.framework.model.common.dff.DFFAttrBO;
import com.sgs.framework.model.common.print.OutPutDataBO;
import com.sgs.framework.model.common.process.ProcessBO;
import com.sgs.framework.model.enums.*;
import com.sgs.framework.model.order.order.OrderOthersBO;
import com.sgs.framework.model.order.trf.TrfBO;
import com.sgs.framework.model.order.v2.EnquiryRelBO;
import com.sgs.framework.model.order.v2.OrderBO;
import com.sgs.framework.model.order.v2.OrderHeaderBO;
import com.sgs.framework.model.order.v2.ParcelBO;
import com.sgs.framework.model.report.report.v2.ReportBO;
import com.sgs.framework.model.report.report.v2.ReportHeaderBO;
import com.sgs.framework.model.report.report.v2.ReportMatrixRelBO;
import com.sgs.framework.model.test.analyte.AnalyteLanguageBO;
import com.sgs.framework.model.test.citation.CitationLanguageBO;
import com.sgs.framework.model.test.conclusion.ConclusionLangBO;
import com.sgs.framework.model.test.conclusion.v2.ObjectConclusionBO;
import com.sgs.framework.model.test.condition.conditiongroup.ConditionGroupLanguageBO;
import com.sgs.framework.model.test.pp.PPLanguageBO;
import com.sgs.framework.model.test.pp.pptestline.v2.PPTestLineRelBO;
import com.sgs.framework.model.test.testdata.testresult.TestResultBO;
import com.sgs.framework.model.test.testdata.testresult.TestResultLanguageBO;
import com.sgs.framework.model.test.testline.TestLineLanguageBO;
import com.sgs.framework.model.test.testline.v2.TestLineBO;
import com.sgs.framework.model.test.testline.v2.TestLineSampleBO;
import com.sgs.framework.model.test.testmatrix.v2.TestMatrixBO;
import com.sgs.framework.model.test.testsample.TestSampleGroupBO;
import com.sgs.framework.model.test.testsample.v2.TestSampleBO;
import com.sgs.framework.model.test.testsample.v2.TestSampleExternalBO;
import com.sgs.framework.model.test.wi.v2.WorkingInstructionBO;
import com.sgs.framework.model.trims.labsection.LabSectionBO;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.tool.jackson.JsonUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.framework.tool.utils.StringPool;
import com.sgs.gpo.biz.intergration.rd.context.ImportReportDataContext;
import com.sgs.gpo.biz.output.context.AssembleDataContext;
import com.sgs.gpo.biz.output.service.OutputFactory;
import com.sgs.gpo.core.config.SupportConfig;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.enums.OutPutType;
import com.sgs.gpo.core.enums.ReportFileType;
import com.sgs.gpo.core.enums.ReportFlagEnums;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subreport.SubReportPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testmatrix.TestMatrixExtPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testmatrix.TestMatrixPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryTrfRelationshipPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.payment.BossOrderInvoiceDTO;
import com.sgs.gpo.domain.service.otsnotes.report.IReportDomainService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportService;
import com.sgs.gpo.domain.service.otsnotes.subreport.ISubReportService;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain.ITestMatrixExtService;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain.ITestMatrixService;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryService;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryTrfRelationshipService;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IBossOrderInvoiceService;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IBossOrderService;
import com.sgs.gpo.facade.model.enums.ConclusionEnums;
import com.sgs.gpo.facade.model.otsnotes.subreport.req.SubReportQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixExtQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixQueryReq;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryIdReq;
import com.sgs.gpo.facade.model.rd.RdDffAttr;
import com.sgs.gpo.facade.model.rd.RdProtocolEntryForm;
import com.sgs.gpo.facade.model.report.req.ReferenceReportQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportQueryReq;
import com.sgs.gpo.integration.dataentry.DataEntryClient;
import com.sgs.gpo.integration.dataentry.req.ConclusionReq;
import com.sgs.gpo.integration.dataentry.rsp.DataEntryConclusionRsp;
import com.sgs.gpo.integration.dff.DFFClient;
import com.sgs.gpo.integration.dff.req.ProtocolEntryFormReq;
import com.sgs.gpo.integration.dff.req.ProtocolEntryFormReqPairs;
import com.sgs.gpo.integration.dff.rsp.DffFieldRsp;
import com.sgs.gpo.integration.dff.rsp.ProtocolEntryFormRsp;
import com.sgs.gpo.integration.framework.NotificationClient;
import com.sgs.gpo.integration.framework.req.SendEmailReq;
import com.sgs.gpo.integration.quotation.QuotationClient;
import com.sgs.gpo.integration.rd.RdClient;
import com.sgs.gpo.integration.sodanotes.SodaNotesClient;
import com.sgs.gpo.integration.sodanotes.req.QueryConclusionDetailReq;
import com.sgs.gpo.integration.sodanotes.rsp.QueryConclusionDetailRsp;
import com.sgs.otsnotes.facade.model.enums.CategoryEnums;
import com.sgs.otsnotes.facade.model.enums.ObjectType;
import com.sgs.otsnotes.facade.model.enums.ReportRequirementEnum;
import com.sgs.preorder.facade.model.enums.AttachmentToCp;
import com.sgs.preorder.facade.model.enums.ServiceType;
import com.sgs.preorder.facade.model.enums.TrfSourceType;
import com.sgs.priceengine.facade.model.enums.QuotationStatus;
import com.sgs.priceengine.facade.model.request.quotationinfo.GetQuotationInfoReq;
import com.sgs.priceengine.facade.model.response.quotationinfo.OrderQuotationRsp;
import com.sgs.priceengine.facade.model.response.quotationinfo.ServiceItemLanguageRsp;
import com.sgs.testdatabiz.facade.model.dto.rd.invoice.RdInvoiceDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.quotation.RdQuotationDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.quotation.RdQuotationRelationshipDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.*;
import com.sgs.testdatabiz.facade.model.enums.ReportType;
import com.sgs.testdatabiz.facade.model.req.rd.ImportReportDataReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.sgs.framework.model.enums.SampleType.*;

@Service
@Slf4j
@Primary
public class ImportReportDataBizCMD extends BaseCommand<ImportReportDataContext> {
    @Autowired
    private OutputFactory outputFactory;
    @Autowired
    private IReportDomainService reportDomainService;
    @Autowired
    private ITestMatrixService testMatrixService;
    @Autowired
    private RdClient rdClient;
    @Autowired
    private SystemLogHelper systemLogHelper;
    @Autowired
    private QuotationClient quotationClient;
    @Autowired
    private IBossOrderInvoiceService bossOrderInvoiceService;
    @Autowired
    private IEnquiryService enquiryService;
    @Autowired
    private IEnquiryTrfRelationshipService enquiryTrfRelationshipService;
    @Autowired
    private IReportService reportService;
    @Autowired
    private ITestMatrixExtService testMatrixExtService;
    @Autowired
    private DFFClient dffClient;
    @Autowired
    private SodaNotesClient sodaNotesClient;
    @Autowired
    private NotificationClient notificationClient;
    @Autowired
    private SupportConfig supportConfig;
    @Autowired
    private IBossOrderService bossOrderService;
    @Autowired
    private DataEntryClient dataEntryClient;
    @Autowired
    private ISubReportService subReportService;
//    @Autowired
//    private IObjectVerifyBizService objectVerifyBizService;


    @Override
    public BaseResponse validateParam(ImportReportDataContext context) {
        Assert.isTrue(Func.isNotEmpty(context.getParam()), "common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
        Assert.isTrue(Func.isNotEmpty(context.getParam().getReportId()) || Func.isNotEmpty(context.getParam().getReportNo()), "common.param.miss", new Object[]{"reportId"});
        Assert.isTrue(Func.isNotEmpty(context.getLab()), "common.param.miss", new Object[]{Constants.TERM.LAB.getCode()});
        // 参数校验 查询出报告对应的Matrix信息用于后续数据过滤
        //查询报告详细信息
        ReportQueryReq reportQueryReq = new ReportQueryReq();
        reportQueryReq.setReportId(context.getParam().getReportId());
        reportQueryReq.setReportNo(context.getParam().getReportNo());
        Lab lab = context.getLab();
        if (Func.isNotEmpty(lab)) {
            reportQueryReq.setLab(lab);
        }
        BaseResponse<List<ReportBO>> reportListRes = reportDomainService.queryBO(reportQueryReq);
        if (reportListRes.isFail() || Func.isEmpty(reportListRes.getData())) {
            return BaseResponse.newFailInstance("common.param.invalid", new Object[]{"reportId"});
        }
        ReportBO report = reportListRes.getData().get(0);
        if (Func.isEmpty(context.getParam().getReportId())) {
            context.getParam().setReportId(report.getId().getReportId());
        }
        String actualReportNo = report.getId().getActualReportNo();
        context.setActualReportNo(actualReportNo);
        context.setReportNo(report.getId().getReportNo());
        context.setReportId(report.getId().getReportId());
        if (Func.isNotEmpty(report.getRelationship()) && Func.isNotEmpty(report.getRelationship().getChildren()) && Func.isNotEmpty(report.getRelationship().getChildren().getReportMatrixList())) {
            Set<String> testMatrixIdList = report.getRelationship().getChildren().getReportMatrixList().stream().map(ReportMatrixRelBO::getTestMatrixId)
                    .collect(Collectors.toSet());
            context.setTestMatrixIdList(testMatrixIdList);
            TestMatrixQueryReq testMatrixQueryReq = new TestMatrixQueryReq();
            testMatrixQueryReq.setTestMatrixIdList(testMatrixIdList);
            BaseResponse<List<TestMatrixPO>> testMatrixResponse = testMatrixService.queryList(testMatrixQueryReq);
            if (Func.isNotEmpty(testMatrixResponse.getData())) {
                context.setTestLineInsIdList(testMatrixResponse.getData().stream().map(TestMatrixPO::getTestLineInstanceID).collect(Collectors.toSet()));
            }
        }
        context.setOrderNo(report.getRelationship().getParent().getOrder().getOrderNo());
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(ImportReportDataContext context) {
        // 查询数据需要的基本参数
        String productLineCode = context.getProductLineCode();
        String reportId = context.getParam().getReportId();
        String orderNo = context.getOrderNo();
        String labCode = context.getLab().getLabCode();

        // 调用标准结构组装的逻辑
        AssembleDataContext assembleDataContext = new AssembleDataContext();
        assembleDataContext.setPrintType(OutPutType.RD.getType());
        assembleDataContext.setReportIdList(Sets.newHashSet(reportId));
        assembleDataContext.setLabCode(labCode);
        assembleDataContext.setTestLineInstanceIds(context.getTestLineInsIdList());
        assembleDataContext.setTestMatrixIds(context.getTestMatrixIdList());
        assembleDataContext.setOrderNo(orderNo);
        assembleDataContext.setProductLineCode(productLineCode);
        ProductLineContextHolder.setProductLineCode(productLineCode);
        BaseResponse<OutPutDataBO> outPutDataRes = outputFactory.assembleData(assembleDataContext);
        if (outPutDataRes.isFail()) {
            return outPutDataRes;
        }
//        //加入数据提前校验
//        ObjectVerifyReq objectVerifyReq = new ObjectVerifyReq();
//        objectVerifyReq.setActionCode(Constants.ACTION.CALLRD);
//        objectVerifyReq.setOutPutDataBO(outPutDataRes.getData());
//        BaseResponse<List<String>> verifyRsp = objectVerifyBizService.verify(objectVerifyReq);
//        if(Func.isEmpty(verifyRsp) || verifyRsp.isFail()){
//            return BaseResponse.newFailInstance();
//        }
//        if(Func.isNotEmpty(verifyRsp.getData())){
//
//        }
        context.setOutPutData(outPutDataRes.getData());
        context.setAttachmentList(assembleDataContext.getAttachmentList());
        context.setConclusionList(Func.isNotEmpty(assembleDataContext.getDataEntryResult())
                ? assembleDataContext.getDataEntryResult().getReportConclusionList() : Lists.newArrayList());
        // 查询DataEntry录入的结论
        ConclusionReq conclusionReq = new ConclusionReq();
        conclusionReq.setProductLineCode(productLineCode);
        conclusionReq.setReportId(reportId);
        conclusionReq.setOrderNo(orderNo);
        conclusionReq.setLabCode(labCode);
        BaseResponse<DataEntryConclusionRsp> dataEntryConclusionRsp = dataEntryClient.queryConclusionInfoByType(conclusionReq);
        if (dataEntryConclusionRsp.isSuccess() && Func.isNotEmpty(dataEntryConclusionRsp.getData())) {
            context.setDataEntryConclusion(dataEntryConclusionRsp.getData());
        }
        this.buildDomain(context);
        return BaseResponse.newSuccessInstance(true);
    }

    /**
     * 数据结构转换
     *
     * @param context
     * @return
     */
    @Override
    public BaseResponse buildDomain(ImportReportDataContext context) {
        if (Func.isEmpty(context.getOutPutData())) {
            return BaseResponse.newFailInstance("GPO 未匹配出数据");
        }
        //
        Lab lab = context.getLab();
        ImportReportDataReq importReportDataReq = new ImportReportDataReq();
        // base 信息
        importReportDataReq.setLabCode(lab.getLabCode());
        importReportDataReq.setSystemId(Long.valueOf(SgsSystem.GPO.getSgsSystemId()));
        importReportDataReq.setProductLineCode(context.getLab().getBuCode());
        importReportDataReq.setRequestId(IdUtil.uuId());
        //orderList
        assembleOrder(context, importReportDataReq);
        //trfList
        assembleTrf(context, importReportDataReq);
        // reportList
        assembleReport(context, importReportDataReq);
        // testSampleList
        assembleTestSample(context, importReportDataReq);
        // testLineList
        assembleTestLine(context, importReportDataReq);
        // testResultList
        assembleTestResult(context, importReportDataReq);
        // conditionGroupList
        assembleConditionGroup(context, importReportDataReq);
        // quotationList
        assembleQuotation(context, importReportDataReq);
        // invoiceList
        assembleInvoice(context, importReportDataReq);
        // 补充Matrix数据
        supplementMatrixData(context, importReportDataReq);
        // 回写conclusion数据到每个业务节点
//        supplementConclusion(importReportDataReq);
        context.setImportReportDataReq(importReportDataReq);
        return BaseResponse.newSuccessInstance(true);
    }


    private void supplementConclusion(ImportReportDataReq importReportDataReq) {
        if (Func.isEmpty(importReportDataReq.getReportConclusionList())) {
            return;
        }
        // 601 Matrix
        List<RdReportConclusionDTO> matrixConclusion = importReportDataReq.getReportConclusionList().stream()
                .filter(item->ConclusionType.check(item.getConclusionLevelId(), ConclusionType.Matrix))
                .collect(Collectors.toList());
        if(Func.isNotEmpty(matrixConclusion) && Func.isNotEmpty(importReportDataReq.getReportList())){
            importReportDataReq.getReportList().stream().forEach(report->{
                if(Func.isEmpty(report.getReportMatrixList())){
                        return;
                }
                report.getReportMatrixList().stream().forEach(reportMatrix->{
                    if(Func.isNotEmpty(reportMatrix.getConclusion())){
                        return;
                    }
                    RdReportConclusionDTO thisMatrixConclusion = matrixConclusion.stream()
                            .filter(item->Func.equalsSafe(item.getObjectId(),reportMatrix.getTestMatrixId()))
                            .findAny().orElse(null);
                    if(Func.isNotEmpty(thisMatrixConclusion)){
                        reportMatrix.setConclusion(Func.copy(thisMatrixConclusion,RdConclusionDTO.class));
                    }
                });
            });
        }
        // 602 TestLine
        List<RdReportConclusionDTO> testLineConclusion = importReportDataReq.getReportConclusionList().stream()
                .filter(item->ConclusionType.check(item.getConclusionLevelId(), ConclusionType.TestLine))
                .collect(Collectors.toList());
        if(Func.isNotEmpty(testLineConclusion) && Func.isNotEmpty(importReportDataReq.getTestLineList())){
            importReportDataReq.getTestLineList().stream().forEach(testLine->{
                if(Func.isNotEmpty(testLine.getConclusion())){
                    return;
                }
                RdReportConclusionDTO thisTestLineConclusion = testLineConclusion.stream()
                        .filter(item->Func.equalsSafe(item.getObjectId(),testLine.getTestLineInstanceId()))
                        .findAny().orElse(null);
                if(Func.isNotEmpty(thisTestLineConclusion)){
                    testLine.setConclusion(Func.copy(thisTestLineConclusion,RdConclusionDTO.class));
                }
            });
        }
        // 603 Report
        List<RdReportConclusionDTO> reportConclusion = importReportDataReq.getReportConclusionList().stream()
                .filter(item->ConclusionType.check(item.getConclusionLevelId(), ConclusionType.Report))
                .collect(Collectors.toList());
        if(Func.isNotEmpty(reportConclusion) && Func.isNotEmpty(importReportDataReq.getReportList())){
            importReportDataReq.getReportList().stream().forEach(report->{
                if(Func.isNotEmpty(report.getConclusion())){
                    return;
                }
                RdReportConclusionDTO thisReportConclusion = reportConclusion.stream()
                        .filter(item->Func.equalsSafe(item.getObjectId(),report.getReportId()))
                        .findAny().orElse(null);
                if(Func.isNotEmpty(thisReportConclusion)){
                    report.setConclusion(Func.copy(thisReportConclusion,RdConclusionDTO.class));
                }
            });
        }
        // TODO TestSample?
    }


    private void supplementMatrixData(ImportReportDataContext context, ImportReportDataReq importReportDataReq) {
        // 如果没有返回分包相关的conclusion，直接返回
        if (Func.isEmpty(context.getDataEntryConclusion()) || Func.isEmpty(context.getDataEntryConclusion().getSubcontractConclusion())) {
            return;
        }
        // 获取已经维护好的ReportConclusionSummary数据
        List<RdReportConclusionDTO> reportConclusionList = importReportDataReq.getReportConclusionList();
        if (Func.isNull(reportConclusionList)) {
            reportConclusionList = Lists.newArrayList();
        }
        // 处理Analyte Conclusion
        List<ObjectConclusionBO> analyteConclusionList = context.getDataEntryConclusion().getSubcontractConclusion()
                .stream()
                .filter(item -> ConclusionType.check(item.getConclusionLevelId(), ConclusionType.Analyte))
                .collect(Collectors.toList());
        if (Func.isNotEmpty(analyteConclusionList)) {
            List<RdReportConclusionDTO> rdAnalyteConclusionList = Lists.newArrayList();
            analyteConclusionList.stream().forEach(analyteConclusion -> rdAnalyteConclusionList.add(convertToRdConclusion(analyteConclusion, context, ConclusionType.Analyte.getCode())));
            if (Func.isNotEmpty(rdAnalyteConclusionList)) {
                reportConclusionList.addAll(rdAnalyteConclusionList);
            }
        }
        // 处理Matrix维护的conclusion
        List<ObjectConclusionBO> matrixConclusionList = context.getDataEntryConclusion().getSubcontractConclusion()
                .stream()
                .filter(item -> ConclusionType.check(item.getConclusionLevelId(), ConclusionType.Matrix))
                .collect(Collectors.toList());
        if (Func.isEmpty(matrixConclusionList)) {
            return;
        }
        // 查询当前报告关联的SubReport数据
        SubReportQueryReq subReportQueryReq = new SubReportQueryReq();
        subReportQueryReq.setReportNo(context.getReportNo());
        List<SubReportPO> subReportPOList = subReportService.select(subReportQueryReq);
        // 如果Report没有关联的SubReport数据，不需要执行往下的增补逻辑
        if (Func.isEmpty(subReportPOList)) {
            return;
        }
        Set<String> subReportNos = subReportPOList.stream().filter(i -> Func.isNotEmpty(i.getSubReportNo()))
                .map(i -> i.getSubReportNo()).collect(Collectors.toSet());
        if (Func.isEmpty(subReportNos)) {
            return;
        }
        // 待增加的数据
        List<RdTestSampleDTO> newSampleList = Lists.newArrayList();
        List<RdTestLineDTO> newTestLineList = Lists.newArrayList();
        List<RdReportMatrixDTO> newReportMatrixList = Lists.newArrayList();
        List<RdReportConclusionDTO> reportMatrixConclusionList = Lists.newArrayList();
        // 防止重复增加
        Set<String> newSampleIds = Sets.newHashSet();
        Set<String> newTestLineIds = Sets.newHashSet();
        Set<String> testMatrixIds = Sets.newHashSet();
        Set<String> testMatrixConclusions = Sets.newHashSet();
        // 现有的数据
        List<RdTestSampleDTO> testSampleList = importReportDataReq.getTestSampleList();
        if (testSampleList == null) {
            testSampleList = Lists.newArrayList();
        }
        List<RdTestLineDTO> testLineList = importReportDataReq.getTestLineList();
        if (testLineList == null) {
            testLineList = Lists.newArrayList();
        }
        if (Func.isEmpty(importReportDataReq.getReportList())) {
            return;
        }
        RdReportDTO reportDTO = importReportDataReq.getReportList().stream().filter(item -> Func.equalsSafe(item.getReportId(), context.getParam().getReportId()))
                .findAny().orElse(null);
        if (Func.isEmpty(reportDTO)) {
            return;
        }
        List<RdReportMatrixDTO> reportMatrixList = reportDTO.getReportMatrixList();
        if (reportMatrixList == null) {
            reportMatrixList = Lists.newArrayList();
        }
        List<RdTestSampleDTO> finalTestSampleList = testSampleList;
        List<RdTestLineDTO> finalTestLineList = testLineList;
        List<RdReportMatrixDTO> finalReportMatrixList = reportMatrixList;
        List<RdReportConclusionDTO> finalReportConclusionList = reportConclusionList;
        matrixConclusionList.stream().forEach(item -> {
            // 如果返回的数据不在Report关联的SubReport中，则不需要执行
            if (Func.isEmpty(item.getSubReportNo()) || !subReportNos.contains(item.getSubReportNo())) {
                return;
            }
            // 不存在的数据需要追加
            // 追加Sample
            if (Func.isNotEmpty(item.getSampleId()) && !newSampleIds.contains(item.getSampleId())) {
                RdTestSampleDTO rdTestSample = finalTestSampleList.stream().filter(sample -> Func.equalsSafe(item.getSampleId(), sample.getTestSampleInstanceId()))
                        .findAny().orElse(null);
                if (Func.isEmpty(rdTestSample)) {
                    RdTestSampleDTO newRdSample = new RdTestSampleDTO();
                    newRdSample.setRealOrderNo(context.getOrderNo());
                    newRdSample.setOrderNo(context.getRdRootOrderNo());
                    newRdSample.setSystemId(SgsSystem.GPO.getSgsSystemId());
                    newRdSample.setTestSampleInstanceId(Func.isNotEmpty(item.getSampleId()) ? item.getSampleId() : IdUtil.uuId());
                    newRdSample.setTestSampleNo(item.getSampleNo());
                    newRdSample.setTestSampleName(item.getSampleNo());
                    newRdSample.setRootOrderNo(context.getRdRootOrderNo());
                    if (Func.isNotEmpty(item.getSampleDescription())) {
                        RdMaterialAttrDTO rdMaterialAttr = new RdMaterialAttrDTO();
                        rdMaterialAttr.setMaterialDescription(item.getSampleDescription());
                        newRdSample.setMaterialAttr(rdMaterialAttr);
                    }
                    newSampleList.add(newRdSample);
                    newSampleIds.add(item.getSampleId());
                }
            }
            // 追加TestLine
            if (Func.isNotEmpty(item.getTestLineInstanceId()) && !newTestLineIds.contains(item.getTestLineInstanceId())) {
                RdTestLineDTO rdTestLineDTO = finalTestLineList.stream().filter(testLine ->
                        Func.equalsSafe(testLine.getTestLineInstanceId(), item.getTestLineInstanceId())
                ).findAny().orElse(null);
                if (Func.isEmpty(rdTestLineDTO)) {
                    RdTestLineDTO newRdTestLine = new RdTestLineDTO();
                    newRdTestLine.setOrderNo(context.getRdRootOrderNo());
                    newRdTestLine.setRealOrderNo(context.getOrderNo());
                    newRdTestLine.setSystemId(SgsSystem.GPO.getSgsSystemId());
                    newRdTestLine.setTestLineInstanceId(Func.isNotEmpty(item.getTestLineInstanceId()) ? item.getTestLineInstanceId() : IdUtil.uuId());
                    newRdTestLine.setTestLineId(item.getTestLineId());
                    newRdTestLine.setEvaluationAlias(getEvaluationAlias(item));
                    newRdTestLine.setRootOrderNo(context.getRdRootOrderNo());
                    RdCitationDTO rdCitationDTO = new RdCitationDTO();
                    rdCitationDTO.setCitationVersionId(item.getCitationVersionId());
                    rdCitationDTO.setCitationId(item.getCitationId());
                    rdCitationDTO.setCitationFullName(item.getCitationName());
                    rdCitationDTO.setCitationName(item.getCitationName());
                    rdCitationDTO.setCitationType(item.getCitationType());
                    newRdTestLine.setCitation(rdCitationDTO);
                    List<RdPpTestLineRelDTO> ppTestLineRelList = Lists.newArrayList();
                    RdPpTestLineRelDTO rdPpTestLineRelDTO = new RdPpTestLineRelDTO();
                    rdPpTestLineRelDTO.setPpVersionId(item.getPpVersionId());
                    ppTestLineRelList.add(rdPpTestLineRelDTO);
                    newRdTestLine.setPpTestLineRelList(ppTestLineRelList);
                    newRdTestLine.setDigitalFlag(Constants.GPO_DIGITAL_FLAG);
                    newTestLineList.add(newRdTestLine);
                    newTestLineIds.add(item.getTestLineInstanceId());
                }
            }
            // 追加ReportMatrix
            RdReportMatrixDTO rdReportMatrix = finalReportMatrixList.stream().filter(matrix -> Func.equalsSafe(matrix.getTestMatrixId(), item.getObjectId())).findAny().orElse(null);
            if (Func.isNotEmpty(item.getObjectId()) && !testMatrixIds.contains(item.getObjectId())) {
                if (Func.isEmpty(rdReportMatrix)) {
                    RdReportMatrixDTO rdReportMatrixDTO = new RdReportMatrixDTO();
                    rdReportMatrixDTO.setMatrixInstanceId(item.getObjectId());
                    rdReportMatrixDTO.setTestMatrixId(item.getObjectId());
                    rdReportMatrixDTO.setTestSampleInstanceId(item.getSampleId());
                    rdReportMatrixDTO.setTestLineInstanceId(item.getTestLineInstanceId());
                    RdConclusionDTO conclusion = new RdConclusionDTO();
                    conclusion.setConclusionCode(revertConclusion(Func.toInteger(item.getConclusionId(), null)));
                    conclusion.setCustomerConclusion(conclusion.getConclusionCode());
                    rdReportMatrixDTO.setConclusion(conclusion);
                    newReportMatrixList.add(rdReportMatrixDTO);
                    testMatrixIds.add(item.getObjectId());
                }
                // 如果现有MatrixConclusion不存在当前Matrix的
                RdReportConclusionDTO rdReportConclusionDTO = finalReportConclusionList.stream().filter(conclusion -> Func.equalsSafe(conclusion.getObjectId(), item.getObjectId())
                                && ConclusionType.check(conclusion.getConclusionLevelId(), ConclusionType.Matrix))
                        .findAny().orElse(null);
                if (Func.isEmpty(rdReportConclusionDTO) && !testMatrixConclusions.contains(item.getObjectId())) {
                    // 追加MatrixConclusion
                    reportMatrixConclusionList.add(convertToRdConclusion(item, context, ConclusionType.Matrix.getCode()));
                    testMatrixConclusions.add(item.getObjectId());
                }
            }
            // 如果testMatrix是GPO已经存在的，需要更新conclusion
            if (Func.isNotEmpty(rdReportMatrix) && Func.isEmpty(rdReportMatrix.getConclusion())) {
                RdConclusionDTO conclusion = new RdConclusionDTO();
                conclusion.setConclusionCode(revertConclusion(Func.toInteger(item.getConclusionId(), null)));
                conclusion.setCustomerConclusion(conclusion.getConclusionCode());
                rdReportMatrix.setConclusion(conclusion);
            }
        });
        if (Func.isNotEmpty(newSampleList)) {
            testSampleList.addAll(newSampleList);
        }
        if (Func.isNotEmpty(newTestLineList)) {
            testLineList.addAll(newTestLineList);
        }
        if (Func.isNotEmpty(newReportMatrixList)) {
            reportMatrixList.addAll(newReportMatrixList);
            reportDTO.setReportMatrixList(reportMatrixList);
        }
        if (Func.isNotEmpty(reportMatrixConclusionList)) {
            reportConclusionList.addAll(reportMatrixConclusionList);
        }
    }

    /**
     * 将ObjectConclusion转换为RDConclusion结构
     *
     * @param objectConclusionBO
     * @param context
     * @return
     */
    private RdReportConclusionDTO convertToRdConclusion(ObjectConclusionBO objectConclusionBO, ImportReportDataContext context, Integer conclusionLevelId) {
        RdReportConclusionDTO rdReportConclusion = new RdReportConclusionDTO();
        rdReportConclusion.setRealOrderNo(context.getOrderNo());
        rdReportConclusion.setOrderNo(context.getRdRootOrderNo());
        rdReportConclusion.setReportId(context.getReportId());
        rdReportConclusion.setSystemId(SgsSystem.GPO.getSgsSystemId());
        rdReportConclusion.setRootOrderNo(context.getRdRootOrderNo());
        rdReportConclusion.setConclusionLevelId(conclusionLevelId);
        rdReportConclusion.setObjectId(objectConclusionBO.getObjectId());
        rdReportConclusion.setTestLineInstanceId(objectConclusionBO.getTestLineInstanceId());
        rdReportConclusion.setSampleInstanceId(objectConclusionBO.getSampleId());
        rdReportConclusion.setConclusionCode(revertConclusion(Func.toInteger(objectConclusionBO.getConclusionId(), null)));
        rdReportConclusion.setCustomerConclusion(rdReportConclusion.getConclusionCode());
        rdReportConclusion.setConclusionInstanceId(IdUtil.uuId());
        rdReportConclusion.setTestRequestFlag(0);
        if (ConclusionType.check(conclusionLevelId, ConclusionType.Analyte)) {
            rdReportConclusion.setObjectName(objectConclusionBO.getAnalyteName());
            rdReportConclusion.setTestMatrixId(objectConclusionBO.getTestMatrixId());
        }
        return rdReportConclusion;
    }

    /**
     * 从RD返回的数据中提取EvaluationAlias
     * 优先取外层的EvaluationAlias
     * 如果外层为空取languageList 数据
     *
     * @return
     */
    private String getEvaluationAlias(ObjectConclusionBO conclusionInfo) {
        if (Func.isEmpty(conclusionInfo)) {
            return StringPool.EMPTY;
        }
        // 优先取外层的EvaluationAlias
        if (Func.isNotEmpty(conclusionInfo.getEvaluationAlias())) {
            return conclusionInfo.getEvaluationAlias();
        }
        if (Func.isNotEmpty(conclusionInfo.getLanguageList())) {
            // 优先取英文
            ConclusionLangBO conclusionEn = conclusionInfo.getLanguageList().stream()
                    .filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.English))
                    .findAny().orElse(null);
            if (Func.isNotEmpty(conclusionEn) && Func.isNotEmpty(conclusionEn.getEvaluationAlias())) {
                return conclusionEn.getEvaluationAlias();
            }
            // 英文没值取中文
            ConclusionLangBO conclusionCn = conclusionInfo.getLanguageList().stream()
                    .filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.Chinese))
                    .findAny().orElse(null);
            if (Func.isNotEmpty(conclusionCn) && Func.isNotEmpty(conclusionCn.getEvaluationAlias())) {
                return conclusionCn.getEvaluationAlias();
            }
        }
        return StringPool.EMPTY;
    }


    /**
     * trfList
     *
     * @param context
     * @param importReportDataReq
     */
    private void assembleTrf(ImportReportDataContext context, ImportReportDataReq importReportDataReq) {
        OutPutDataBO outPutData = context.getOutPutData();
        if (Func.isEmpty(outPutData.getOrder()) || Func.isEmpty(outPutData.getOrder().getRelationship())
                || Func.isEmpty(outPutData.getOrder().getRelationship().getParent())
                || Func.isEmpty(outPutData.getOrder().getRelationship().getParent().getTrfList())) {
            importReportDataReq.setTrfList(Lists.newArrayList());
            return;
        }
        List<TrfBO> trfList = outPutData.getOrder().getRelationship().getParent().getTrfList();
        String orderId = outPutData.getOrder().getId().getOrderId();
        List<RdTrfDTO> rdTrfList = new ArrayList<>();
        if (Func.isNotEmpty(trfList)) {
            trfList.stream().forEach(trfBO -> {
                if (Func.isEmpty(trfBO.getTrfNo())) {
                    return;
                }
                RdTrfDTO rdTrfDTO = new RdTrfDTO();
                rdTrfDTO.setRefSystemId(trfBO.getRefSystemId());
                rdTrfDTO.setTrfNo(trfBO.getTrfNo());
                if (Func.isNotEmpty(trfBO.getTrfSourceType())) {
                    if (Func.equalsSafe(trfBO.getTrfSourceType(), TrfSourceType.TRF2Order.getSourceType())) {
                        rdTrfDTO.setSource("1");
                    } else if (Func.equalsSafe(trfBO.getTrfSourceType(), TrfSourceType.Order2TRF.getSourceType())) {
                        rdTrfDTO.setSource("2");
                    }
                }
                List<RdOrderRelDTO> orderList = Lists.newArrayList();
                RdOrderRelDTO rdOrderRelDTO = new RdOrderRelDTO();
                rdOrderRelDTO.setRealOrderNo(context.getOrderNo());
                rdOrderRelDTO.setOrderNo(context.getRdRootOrderNo());
                rdOrderRelDTO.setSystemId(SgsSystem.GPO.getSgsSystemId());
                rdOrderRelDTO.setRootOrderNo(context.getRdRootOrderNo());
                rdOrderRelDTO.setOrderInstanceId(orderId);
                orderList.add(rdOrderRelDTO);
                rdTrfDTO.setOrderList(orderList);
                rdTrfList.add(rdTrfDTO);
            });
        }
        importReportDataReq.setTrfList(rdTrfList);
    }

    /**
     * orderList
     *
     * @param context
     * @param importReportDataReq
     */
    private void assembleOrder(ImportReportDataContext context, ImportReportDataReq importReportDataReq) {
        OutPutDataBO outPutData = context.getOutPutData();
        if (Func.isEmpty(outPutData.getOrder())) {
            return;
        }
        OrderBO order = outPutData.getOrder();
        List<RdOrderDTO> orderList = Lists.newArrayList();
        RdOrderDTO rdOrderDTO = new RdOrderDTO();
        OrderHeaderBO orderHeaderBO = order.getHeader();
        if (Func.isNotEmpty(orderHeaderBO)) {
            rdOrderDTO = Func.copy(orderHeaderBO, RdOrderDTO.class);
            rdOrderDTO.setActiveIndicator(order.getActiveIndicator());
            rdOrderDTO.setRealOrderNo(context.getOrderNo());
            //rdOrderDTO.setOrderNo(context.getRdRootOrderNo());
            rdOrderDTO.setOrderType(orderHeaderBO.getCaseType());
            if (Func.isNotEmpty(orderHeaderBO.getServiceType())) {
                ServiceType serviceType = ServiceType.getCode(orderHeaderBO.getServiceType());
                if (Func.isNotEmpty(serviceType)) {
                    rdOrderDTO.setServiceType(serviceType.getStatus());
                }
            }
            rdOrderDTO.setCreateDate(orderHeaderBO.getCreateDate());
        }
        if (Func.isNotEmpty(order.getPayment())) {
            rdOrderDTO.setPayment(Func.copy(order.getPayment(), RdPaymentDTO.class));
        }
        if (Func.isNotEmpty(order.getContactPersonList())) {
            List<RdContactPersonDTO> contactPersonList = order.getContactPersonList().parallelStream().map(item ->
                    {
                        RdContactPersonDTO rdContactPersonDTO = Func.copy(item, RdContactPersonDTO.class);
                        Integer contactUsage = Func.isNotEmpty(OrderPersonType.getCode(rdContactPersonDTO.getContactUsage())) ? OrderPersonType.getCode(rdContactPersonDTO.getContactUsage()).getStatus() : null;
                        rdContactPersonDTO.setContactUsage(Func.toStr(contactUsage));
                        return rdContactPersonDTO;
                    }
            ).collect(Collectors.toList());
            rdOrderDTO.setContactPersonList(contactPersonList);
        }
        if (Func.isNotEmpty(order.getFlags())) {
            rdOrderDTO.setFlags(Func.copy(order.getFlags(), RdFlagsDTO.class));
        }
        if (Func.isNotEmpty(order.getOthers())) {
            RdOrderOthersDTO others = Func.copy(order.getOthers(), RdOrderOthersDTO.class);
            others.setPending(Func.copy(order.getOthers().getPending(), RdPendingDTO.class));
            rdOrderDTO.setOthers(others);
        }
        if (Func.isNotEmpty(order.getCustomerList())) {
            List<RdCustomerDTO> rdCustomerList = order.getCustomerList().parallelStream().map(item -> {
                RdCustomerDTO rdCustomerDTO = Func.copy(item, RdCustomerDTO.class);
                rdCustomerDTO.setCustomerInstanceId(item.getId());
                if (Func.isNotEmpty(item.getLanguageList())) {
                    rdCustomerDTO.setLanguageList(item.getLanguageList().parallelStream().map(lan -> Func.copy(lan, RdCustomerLanguageDTO.class)).collect(Collectors.toList()));
                }
                if (Func.isNotEmpty(item.getCustomerContactList())) {
                    List<RdCustomerContactDTO> customerContactList = item.getCustomerContactList().parallelStream().map(contact -> {
                        RdCustomerContactDTO rdCustomerContact = Func.copy(contact, RdCustomerContactDTO.class);
                        rdCustomerContact.setSgsAccountCode(contact.getContactSgsMartAccount());
                        rdCustomerContact.setSgsUserId(contact.getContactSgsMartUserId());
                        return rdCustomerContact;
                    }).collect(Collectors.toList());
                    rdCustomerDTO.setCustomerContactList(customerContactList);
                }
                OrderOthersBO orderOthersBO = order.getOthers();
                if (Func.equalsSafe(item.getCustomerUsage(), CustomerType.Buyer.getStatus()) && Func.isNotEmpty(orderOthersBO)) {
                    rdCustomerDTO.setMarketSegmentCode(orderOthersBO.getDepartmentCode());
                    rdCustomerDTO.setMarketSegmentName(orderOthersBO.getDepartmentCode());
                }
                return rdCustomerDTO;
            }).collect(Collectors.toList());
            rdOrderDTO.setCustomerList(rdCustomerList);
        }
        if (Func.isNotEmpty(order.getProduct())) {
            List<RdProductDTO> productList = Lists.newArrayList();
            RdProductDTO rdProduct = Func.copy(order.getProduct(), RdProductDTO.class);
            if (Func.isNotEmpty(order.getProduct().getProductAttrList())) {
                rdProduct.setProductAttrList(convertAttrList(order.getProduct().getProductAttrList()));
            }
            productList.add(rdProduct);
            rdOrderDTO.setProductList(productList);
        }
        if (Func.isNotEmpty(order.getSampleList())) {
            List<RdSampleDTO> sampleList = Lists.newArrayList();
            List<com.sgs.framework.model.test.conclusion.ObjectConclusionBO> conclusionList = context.getConclusionList();
            order.getSampleList().parallelStream().forEach(sampleBO -> {
                RdSampleDTO rdSample = Func.copy(sampleBO, RdSampleDTO.class);
                if (Func.isNotEmpty(sampleBO.getSampleAttrList())) {
                    rdSample.setSampleAttrList(convertAttrList(sampleBO.getSampleAttrList()));
                }
                // 过滤Sample维度的conclusion
                if (Func.isNotEmpty(conclusionList)) {
                    com.sgs.framework.model.test.conclusion.ObjectConclusionBO sampleConclusion = conclusionList.stream()
                            .filter(conclusion -> Func.equalsSafe(conclusion.getSampleInstanceId(), rdSample.getTestSampleInstanceId())
                                    && ConclusionType.check(conclusion.getConclusionLevelId(), ConclusionType.OriginalSample)).findAny().orElse(null);
                    if (Func.isNotEmpty(sampleConclusion)) {
                        RdConclusionDTO conclusion = Func.copy(sampleConclusion, RdConclusionDTO.class);
                        rdSample.setConclusion(conclusion);
                    }
                }
                sampleList.add(rdSample);
            });
            rdOrderDTO.setSampleList(sampleList);
        }
        // serviceRequirement
        if (Func.isNotEmpty(order.getServiceRequirement())) {
            RdServiceRequirementDTO serviceRequirement = new RdServiceRequirementDTO();
            serviceRequirement.setOtherRequestRemark(order.getServiceRequirement().getOtherRequestRemark());
            RdServiceRequirementReportDTO report = new RdServiceRequirementReportDTO();
            if (Func.isNotEmpty(order.getServiceRequirement().getReport())) {
                report = Func.copy(order.getServiceRequirement().getReport(), RdServiceRequirementReportDTO.class);
                report.setAccreditation(order.getServiceRequirement().getReport().getNeedAccreditation());
                if (Func.isNotEmpty(order.getServiceRequirement().getReport().getLanguageList())) {
                    List<RdReportLanguageDTO> languageList = Lists.newArrayList();
                    order.getServiceRequirement().getReport().getLanguageList().stream().forEach(item -> {
                        RdReportLanguageDTO reportLanguageDTO = new RdReportLanguageDTO();
                        reportLanguageDTO.setLanguageId(item.getLanguageId());
                        reportLanguageDTO.setReportHeader(item.getReportHeader());
                        reportLanguageDTO.setReportAddress(item.getReportAddress());
                        languageList.add(reportLanguageDTO);
                    });
                    report.setLanguageList(languageList);
                }
                // hardCopy
                RdDeliveryDTO hardCopy = new RdDeliveryDTO();
                hardCopy.setRequired(Func.toStr(order.getServiceRequirement().getReport().getHardcopyRequired()));
                hardCopy.setDeliveryWay(order.getServiceRequirement().getReport().getHardCopyReportDeliverWay());
                hardCopy.setDeliveryOthers(order.getServiceRequirement().getReport().getHardCopyToOther());
            }
            report.setCommentFlag(Func.toStr(order.getServiceRequirement().getCommentFlag()));
            report.setConfirmCoverPageFlag(Func.toStr(order.getServiceRequirement().getConfirmCoverPageFlag()));
            serviceRequirement.setReport(report);
            RdServiceRequirementSampleDTO sample = new RdServiceRequirementSampleDTO();
            sample.setLiquid(order.getServiceRequirement().getLiquidTestSample());
            RdDeliveryDTO returnResidueSample = new RdDeliveryDTO();
            returnResidueSample.setRequired(Func.toStr(order.getServiceRequirement().getReturnResidueSampleFlag()));
            sample.setReturnResidueSample(returnResidueSample);
            RdDeliveryDTO returnTestSample = new RdDeliveryDTO();
            returnTestSample.setRequired(Func.toStr(order.getServiceRequirement().getReturnTestSampleFlag()));
            sample.setReturnTestSample(returnTestSample);
            serviceRequirement.setSample(sample);
            RdServiceRequirementInvoiceDTO invoice = new RdServiceRequirementInvoiceDTO();
            RdDeliveryDTO invoiceDetail = new RdDeliveryDTO();
            invoiceDetail.setDeliveryWay(order.getServiceRequirement().getInvoiceDeliverWay());
            invoice.setInvoice(invoiceDetail);
            serviceRequirement.setInvoice(invoice);
            rdOrderDTO.setServiceRequirement(serviceRequirement);
        }
        // testItemMappingList
        // attachmentList
        if (Func.isNotEmpty(context.getAttachmentList())) {
            List<AttachmentBO> attachmentBOList = context.getAttachmentList().parallelStream().filter(item -> Func.equalsSafe(item.getToCp(), AttachmentToCp.YES.getType())).collect(Collectors.toList());
            if (Func.isNotEmpty(attachmentBOList)) {
                List<RdAttachmentDTO> attachmentList = attachmentBOList.parallelStream().map(item ->
                        {
                            RdAttachmentDTO rdAttachment = Func.copy(item, RdAttachmentDTO.class);
                            rdAttachment.setToCustomerFlag(item.getToCp());
                            rdAttachment.setAttachmentInstanceId(item.getId());
                            rdAttachment.setActiveIndicator(ActiveType.Enable.getStatus());
                            rdAttachment.setFileType(item.getFileType());
                            rdAttachment.setObjectType(item.getObjectType());
                            rdAttachment.setBizType(item.getBusinessType());
                            rdAttachment.setObjectNo(Func.isEmpty(item.getObjectId()) ? order.getId().getOrderId() : item.getObjectId());
                            return rdAttachment;
                        }
                ).collect(Collectors.toList());
                rdOrderDTO.setAttachmentList(attachmentList);
            }

        }

        //设置送给RD的RootOrderNo，该值与GPO表中的RootOrderNo不同
        String rdRootOrderNo = orderHeaderBO.getOrderNo();
        String enquiryNo = "";
        if (Func.isNotEmpty(order.getRelationship()) && Func.isNotEmpty(order.getRelationship().getParent()) && Func.isNotEmpty(order.getRelationship().getParent().getEnquiry())) {
            EnquiryRelBO enquiry = order.getRelationship().getParent().getEnquiry();
            enquiryNo = enquiry.getEnquiryNo();
        }
        List<EnquiryTrfRelationshipPO> enquiryTrfRelationshipPOList = null;
        if (Func.isNotEmpty(enquiryNo)) {
            EnquiryIdReq enquiryIdReq = new EnquiryIdReq();
            enquiryIdReq.setEnquiryNoList(Sets.newHashSet(enquiryNo));
            List<EnquiryPO> enquiryPOList = enquiryService.select(enquiryIdReq).getData();
            enquiryIdReq.setEnquiryIdList(Sets.newHashSet(enquiryPOList.stream().map(EnquiryPO::getId).filter(Func::isNotEmpty).collect(Collectors.toSet())));
            enquiryTrfRelationshipPOList = enquiryTrfRelationshipService.select(enquiryIdReq).getData();
        }
        if (Func.isNotEmpty(enquiryTrfRelationshipPOList)) {
            boolean existEnquiryTrfRel = enquiryTrfRelationshipPOList.stream().anyMatch(item -> RefIntegrationChannel.check(item.getIntegrationChannel(), RefIntegrationChannel.SCI));
            if (existEnquiryTrfRel) {
                rdRootOrderNo = enquiryNo;
            }
        }
        context.setRdRootOrderNo(rdRootOrderNo);
        rdOrderDTO.setOrderNo(context.getRdRootOrderNo());
        rdOrderDTO.setRootOrderNo(rdRootOrderNo);
        if (Func.isNotEmpty(order.getRelationship()) && Func.isNotEmpty(order.getRelationship().getParent())) {
            // trfList
            if (Func.isNotEmpty(order.getRelationship().getParent().getTrfList())) {
                List<TrfBO> trfList = outPutData.getOrder().getRelationship().getParent().getTrfList();
                List<RdTrfRelDTO> rdTrfList = trfList.parallelStream().filter(item -> Func.isNotEmpty(item.getTrfNo()))
                        .map(item -> Func.copy(item, RdTrfRelDTO.class)).collect(Collectors.toList());
                rdOrderDTO.setTrfList(rdTrfList);
            }

        }
        rdOrderDTO.setSystemId(SgsSystem.GPO.getSgsSystemId());
        rdOrderDTO.setParentOrderNo(order.getId().getParentOrderNo());
        rdOrderDTO.setOriginalOrderNo(order.getId().getRootOrderNo());
        if (Func.isNotEmpty(order.getProcessList())) {
            RdProcessListDTO rdProcessListDTO = this.convertProcessList(order.getProcessList());
            List<RdProcessListDTO> processList = Lists.newArrayList();
            processList.add(rdProcessListDTO);
            rdOrderDTO.setProcessList(processList);
        }
        rdOrderDTO.setOrderInstanceId(order.getId().getOrderId());
        if (Func.isNotEmpty(order.getTops())) {
            rdOrderDTO.setTopsLabId(Func.toStr(order.getTops().get(0).getToLabId()));
            rdOrderDTO.setTopsLabCode(order.getTops().get(0).getToLab());
        }
        if (Func.isNotEmpty(order.getParcelList())) {
            RdOrderDTO.RdRelationshipDTO rdRelationship = new RdOrderDTO.RdRelationshipDTO();
            RdOrderDTO.RdRelationshipDTO.RdRelationshipParentDTO parent = new RdOrderDTO.RdRelationshipDTO.RdRelationshipParentDTO();
            List<String> parcelNoList = order.getParcelList().stream().map(ParcelBO::getParcelNo).collect(Collectors.toList());
            parent.setParcelNoList(parcelNoList);
            rdRelationship.setParent(parent);
            rdOrderDTO.setRelationship(rdRelationship);
        }
        orderList.add(rdOrderDTO);
        importReportDataReq.setOrderList(orderList);
    }

    private RdProcessListDTO convertProcessList(List<ProcessBO> processList) {
        RdProcessListDTO rdProcessListDTO = new RdProcessListDTO();
        if (Func.isEmpty(processList)) {
            return rdProcessListDTO;
        }
        processList.stream().forEach(item -> {
            if (Func.equalsSafe(Constants.OBJECT.ORDER.ACTION.create.name(), item.getNodePoint())) {
                RdProcessDTO create = new RdProcessDTO();
                create.setNodePoint(item.getNodePoint());
                create.setOperationTime(item.getCompletedDateTime());
                create.setOperator(item.getOperator());
                create.setRemark(item.getRemark());
                rdProcessListDTO.setCreate(create);
                return;
            }
            if (Func.equalsSafe(Constants.OBJECT.ORDER.ACTION.confirm.name(), item.getNodePoint())) {
                RdProcessDTO confirm = new RdProcessDTO();
                confirm.setNodePoint(item.getNodePoint());
                confirm.setOperationTime(item.getCompletedDateTime());
                confirm.setOperator(item.getOperator());
                confirm.setRemark(item.getRemark());
                rdProcessListDTO.setConfirm(confirm);
                return;
            }
            if (Func.equalsSafe(Constants.OBJECT.ORDER.ACTION.delivery.name(), item.getNodePoint())) {
                RdProcessDTO delivery = new RdProcessDTO();
                delivery.setNodePoint(item.getNodePoint());
                delivery.setOperationTime(item.getCompletedDateTime());
                delivery.setOperator(item.getOperator());
                delivery.setRemark(item.getRemark());
                rdProcessListDTO.setDelivery(delivery);
                return;
            }
            if (Func.equalsSafe(Constants.OBJECT.ORDER.ACTION.sampleReceive.name(), item.getNodePoint())) {
                RdProcessDTO sampleReceive = new RdProcessDTO();
                sampleReceive.setNodePoint(item.getNodePoint());
                sampleReceive.setOperationTime(item.getCompletedDateTime());
                sampleReceive.setOperator(item.getOperator());
                sampleReceive.setRemark(item.getRemark());
                rdProcessListDTO.setSampleReceive(sampleReceive);
                return;
            }
        });
        return rdProcessListDTO;
    }

    private List<RdProductSampleAttrDTO> convertAttrList(List<DFFAttrBO> gpoAttrList) {
        List<RdProductSampleAttrDTO> rdAttrList = Lists.newArrayList();
        if (Func.isEmpty(gpoAttrList)) {
            return rdAttrList;
        }
        rdAttrList = gpoAttrList.parallelStream().map(pa -> {
            RdProductSampleAttrDTO rdProductSampleAttr = Func.copy(pa, RdProductSampleAttrDTO.class);
            rdProductSampleAttr.setValue(Func.isNotEmpty(pa.getValue()) ? pa.getValue().toString() : "");
            rdProductSampleAttr.setDisplayInReport(Func.toStr(pa.getDisplayInReport()));
            if (Func.isNotEmpty(pa.getLanguageList())) {
                rdProductSampleAttr.setLanguageList(pa.getLanguageList().parallelStream().map(item -> {
                                    RdAttrLanguageDTO rdAttrLanguage = Func.copy(item, RdAttrLanguageDTO.class);
                                    rdAttrLanguage.setValue(Func.isNotEmpty(item.getValue()) ? item.getValue().toString() : "");
                                    return rdAttrLanguage;
                                }
                        )
                        .collect(Collectors.toList()));
            }
            return rdProductSampleAttr;
        }).collect(Collectors.toList());
        return rdAttrList;
    }

    /**
     * reportList
     *
     * @param context
     * @param importReportDataReq
     */
    private void assembleReport(ImportReportDataContext context, ImportReportDataReq importReportDataReq) {
        Lab lab = context.getLab();
        String orderNo = context.getOrderNo();
        OutPutDataBO outPutData = context.getOutPutData();
        if (Func.isEmpty(outPutData.getReportList())) {
            return;
        }
        // 主报告的结论
        List<ObjectConclusionBO> conclusionList = null;
        if (Func.isNotEmpty(context.getDataEntryConclusion())) {
            conclusionList = context.getDataEntryConclusion().getMainReportConclusion();
        }
        List<RdReportDTO> reportList = Lists.newArrayList();
        List<TestMatrixBO> testMatrixBOList = outPutData.getTestMatrixList();
        //获取订单上的ReportRequirement
        String reportRequirement = outPutData.getOrder().getServiceRequirement().getReportRequirement();
        Set<String> reportMatrixIdList = Sets.newHashSet();
        // testMatrix
        Map<String, String> sampleMatrixMap = Maps.newHashMap();
        // 获取当前订单的protocol数据
        List<TestResultBO> testResultList = null;
        if (Func.isNotEmpty(outPutData.getTestData())) {
            testResultList = outPutData.getTestData().getTestResultList();
        }
        List<TestResultBO> finalTestResultList = testResultList;
        outPutData.getReportList().stream().forEach(report -> {
            RdReportDTO reportDTO = new RdReportDTO();
            ReportHeaderBO header = report.getHeader();
            Func.copy(header, reportDTO);
            if (Func.isNotEmpty(header.getReportVersion())) {
                reportDTO.setReportVersion(header.getReportVersion().toString());
            }
            reportDTO.setActiveIndicator(ActiveType.Enable.getStatus());
            reportDTO.setTestMatrixMergeMode(header.getTestMatrixMergeMode());
            reportDTO.setSystemId(SgsSystem.GPO.getSgsSystemId());
            reportDTO.setRealOrderNo(report.getRelationship().getParent().getOrder().getOrderNo());
            reportDTO.setOrderNo(context.getRdRootOrderNo());
            reportDTO.setReportId(report.getId().getReportId());
            reportDTO.setReportNo(report.getId().getActualReportNo());
            reportDTO.setOriginalReportNo(report.getId().getParentReportNo());
            //转换成外部号
            if (Func.isNotEmpty(reportDTO.getOriginalReportNo())) {
                ReportQueryReq reportQueryReq = new ReportQueryReq();
                reportQueryReq.setReportNo(reportDTO.getOriginalReportNo());
                List<ReportPO> originReportList = reportService.select(reportQueryReq).getData();
                if (Func.isNotEmpty(originReportList) && Func.isNotEmpty(originReportList.get(0).getActualReportNo())) {
                    reportDTO.setOriginalReportNo(originReportList.get(0).getActualReportNo());
                }
            }
            reportDTO.setCreateDate(header.getCreateDate());
            reportDTO.setCreateBy(header.getCreateBy());
            if (Func.isNotEmpty(lab)) {
                reportDTO.setLab(Func.copy(lab, RdLabDTO.class));
            }
            if (Func.isNotEmpty(report.getConclusion())) {
                reportDTO.setConclusion(Func.copy(report.getConclusion(), RdConclusionDTO.class));
            }
            if (Func.isNotEmpty(report.getRelationship()) && Func.isNotEmpty(report.getRelationship().getChildren().getReportMatrixList())) {
                List<ReportMatrixRelBO> reportMatrixList = report.getRelationship().getChildren().getReportMatrixList();
                if (Func.isNotEmpty(reportMatrixList)) {
                    List<RdReportMatrixDTO> rdReportMatrixList = Lists.newArrayList();
                    reportMatrixList.stream().forEach(reportMatrix -> {
                        RdReportMatrixDTO matrix = Func.copy(reportMatrix, RdReportMatrixDTO.class);
                        // 如果protocol中勾选为false不需要送RD
                        if (Func.isNotEmpty(finalTestResultList)) {
                            TestResultBO protocolTestResult = finalTestResultList.stream().filter(item -> Func.equalsSafe(item.getTestMatrixId(), matrix.getTestMatrixId()))
                                    .findAny().orElse(null);
                            if (Func.isNotEmpty(protocolTestResult) && Func.isNotEmpty(protocolTestResult.getTestResultDisplayInReport()) && !protocolTestResult.getTestResultDisplayInReport()) {
                                return;
                            }
                        }
                        // 根据TestMatrix设置具体的值
                        if (Func.isNotEmpty(testMatrixBOList)) {
                            TestMatrixBO testMatrixBO = testMatrixBOList.stream().filter(tm -> Func.equalsSafe(tm.getId().getTestMatrixId(), matrix.getTestMatrixId())).findAny().orElse(null);
                            if (Func.isEmpty(testMatrixBO)) {
                                return;
                            }
                            matrix.setTestLineInstanceId(testMatrixBO.getRelationship().getParent().getTestLine().getTestLineInstanceId());
                            matrix.setTestSampleInstanceId(testMatrixBO.getRelationship().getParent().getTestSample().getTestSampleInstanceId());
                            matrix.setMatrixInstanceId(matrix.getTestMatrixId());
                            matrix.setTestMatrixGroupId(testMatrixBO.getHeader().getTestMatrixGroupId());
                            matrix.setActiveIndicator(testMatrixBO.getActiveIndicator());
                            if (Func.isNotEmpty(testMatrixBO.getHeader().getApplicationFactorIds())) {
                                matrix.setApplicationFactor(JsonUtil.toJson(testMatrixBO.getHeader().getApplicationFactorIds()));
                            }
                            sampleMatrixMap.put(matrix.getTestMatrixId(), testMatrixBO.getRelationship().getParent().getTestSample().getTestSampleNo());
                        }
                        reportMatrixIdList.add(matrix.getTestMatrixId());
                        rdReportMatrixList.add(matrix);
                    });
                    reportDTO.setReportMatrixList(rdReportMatrixList);
                }
            }
            // reportFileList
            if (Func.isNotEmpty(report.getAttachmentList())) {
                // 只推送Final报告给RD
                List<AttachmentBO> finalReport = getFinalReport(report.getAttachmentList(), reportRequirement);
                if (Func.isNotEmpty(finalReport)) {
                    reportDTO.setReportFileList(
                            finalReport.stream().map(item -> {
                                RdAttachmentDTO rdAttachmentDTO = Func.copy(item, RdAttachmentDTO.class);
                                rdAttachmentDTO.setAttachmentInstanceId(item.getId());
                                rdAttachmentDTO.setLanguageId(item.getFileLanguageId());
                                rdAttachmentDTO.setBizType(item.getBusinessType());
                                rdAttachmentDTO.setObjectType(item.getObjectType());
                                return rdAttachmentDTO;
                            }).collect(Collectors.toList())
                    );
                }
            }
            // subReportList
            if (Func.isNotEmpty(report.getSubReportList())) {
                reportDTO.setSubReportList(report.getSubReportList().parallelStream().map(item -> {
                    RdSubReportDTO rdSubReportDTO = Func.copy(item, RdSubReportDTO.class);
                    List<RdAttachmentDTO> subReportFileList = Lists.newArrayList();
                    item.getSubReportFileList().stream().forEach(subReportFile -> {
                        RdAttachmentDTO rdAttachmentDTO = Func.copy(subReportFile, RdAttachmentDTO.class);
                        rdAttachmentDTO.setAttachmentInstanceId(subReportFile.getId());
                        rdAttachmentDTO.setLanguageId(subReportFile.getFileLanguageId());
                        rdAttachmentDTO.setBizType(subReportFile.getBusinessType());
                        rdAttachmentDTO.setObjectType(subReportFile.getObjectType());
                        subReportFileList.add(rdAttachmentDTO);
                    });
                    rdSubReportDTO.setSubReportFileList(subReportFileList);
                    return rdSubReportDTO;
                }).collect(Collectors.toList()));
            }
            reportDTO.setRootOrderNo(context.getRdRootOrderNo());
            // COC
            if (Func.isNotEmpty(report.getReportCertificateList())) {
                List<ReportCertificateDTO> reportCertificateList = Lists.newArrayList();
                report.getReportCertificateList().stream().forEach(item -> {
                    ReportCertificateDTO reportCertificateDTO = Func.copy(item, ReportCertificateDTO.class);
                    reportCertificateList.add(reportCertificateDTO);
                });
                reportDTO.setReportCertificateList(reportCertificateList);
            }
            reportDTO.setApproveBy(report.getHeader().getApproverBy());
            reportDTO.setReportInstanceId(report.getId().getReportId());
            reportDTO.setSoftCopyDeliveryDate(report.getHeader().getSoftDeliveryDate());
            reportDTO.setReportRemark(report.getHeader().getRemark());
            reportDTO.setCountryOfDestination(report.getHeader().getCountryOfDestination());
            reportDTO.setTestingType(report.getOthers().getTestingType());
            // certificateRequirement
            RdCertificateDTO certificate = new RdCertificateDTO();
            certificate.setCertificateName(report.getHeader().getCertificateName());
            if (Func.isNotEmpty(report.getReportTemplate()) && Func.isNotEmpty(report.getReportTemplate().getAccreditation())) {
                certificate.setAcceditationRemark(report.getReportTemplate().getAccreditation().getAccreditationRemark());
                certificate.setReportApprovedStatement(report.getReportTemplate().getAccreditation().getStatement());
                certificate.setDescription(report.getReportTemplate().getAccreditation().getDescription());
            }
            reportDTO.setCertificateRequirement(certificate);

            // signList
            if (Func.isNotEmpty(report.getReportTemplate().getSignatureList())) {
                reportDTO.setSignList(JSON.toJSONString(report.getReportTemplate().getSignatureList()));
            }
            //reportType reportSource
            reportDTO.setReportType(ReportType.SubReport);
            reportDTO.setReportSource("GPO");
            if (ReportFlagEnums.check(report.getHeader().getReportFlag(), ReportFlagEnums.REPORT)) {
                reportDTO.setReportType(ReportType.MyReport);
            }
            if (ReportFlagEnums.check(report.getHeader().getReportFlag(), ReportFlagEnums.SLIM_REPORT)) {
                reportDTO.setReportSource("Slim");
            }
            if (ReportFlagEnums.check(report.getHeader().getReportFlag(), ReportFlagEnums.STARLIMS_REPORT)) {
                reportDTO.setReportSource("Starlims");
            }
            // meta
            Map<String, List<RdProtocolEntryForm>> protocolDFFMap = this.getProtocolEntryForm(context);
            if (Func.isNotEmpty(protocolDFFMap)) {
                reportDTO.setMetaData(JSONObject.toJSONString(protocolDFFMap));
            }
            reportList.add(reportDTO);
        });
        importReportDataReq.setReportList(reportList);
        // reportConclusionList
        List<RdReportConclusionDTO> reportConclusionList = Lists.newArrayList();
        if (Func.isNotEmpty(conclusionList)) {
            // 追加getConclusionList接口返回的conclusion数据
            conclusionList.stream().forEach(conclusion -> {
                RdReportConclusionDTO rdReportConclusion = new RdReportConclusionDTO();
                rdReportConclusion.setOrderNo(context.getRdRootOrderNo());
                rdReportConclusion.setRealOrderNo(orderNo);
                rdReportConclusion.setSystemId(SgsSystem.GPO.getSgsSystemId());
                rdReportConclusion.setRootOrderNo(context.getRdRootOrderNo());
                rdReportConclusion.setConclusionInstanceId(conclusion.getId());
                rdReportConclusion.setConclusionLevelId(conclusion.getConclusionLevelId());
                rdReportConclusion.setObjectId(conclusion.getObjectId());
                rdReportConclusion.setReportId(conclusion.getReportId());
                rdReportConclusion.setConclusionId(conclusion.getConclusionId());
                if (Func.isNotEmpty(conclusion.getConclusion())) {
                    rdReportConclusion.setConclusionCode(revertConclusion(Func.toInteger(conclusion.getConclusion().getConclusionCode(), null)));
                    rdReportConclusion.setCustomerConclusion(conclusion.getConclusion().getCustomerConclusion());
                }
                rdReportConclusion.setCustomerConclusionId(conclusion.getConclusionSettingId());
                rdReportConclusion.setConclusionRemark(conclusion.getConclusionRemark());
                rdReportConclusion.setTestLineInstanceId(conclusion.getTestLineInstanceId());
                if (Func.isNotEmpty(conclusion.getSectionId())) {
                    rdReportConclusion.setSectionId(conclusion.getSectionId().intValue());
                }
                rdReportConclusion.setPpArtifactRelId(conclusion.getPpSampleRelId());
                rdReportConclusion.setRootOrderNo(context.getRdRootOrderNo());
                rdReportConclusion.setPpSampleRelId(conclusion.getPpSampleRelId());
                rdReportConclusion.setSampleInstanceId(conclusion.getSampleId());
                rdReportConclusion.setTestRequestFlag(Func.equalsSafe(conclusion.getItemType(), "TestGroup") ? 1 : 0);
                if (ConclusionType.check(conclusion.getConclusionLevelId(), ConclusionType.TestLine) ||
                        Func.equalsSafe(conclusion.getItemType(), "TestGroup")) {
                    rdReportConclusion.setObjectName(getEvaluationAlias(conclusion));
                }
                rdReportConclusion.setActiveIndicator(ActiveType.Enable.getStatus());
                reportConclusionList.add(rdReportConclusion);
            });
        }
        // 追加protocol记录的testRequest(物理的)
        List<TestMatrixExtPO> testMatrixExtList = null;
        if (Func.isNotEmpty(reportMatrixIdList)) {
            TestMatrixExtQueryReq testMatrixExtQueryReq = new TestMatrixExtQueryReq();
            testMatrixExtQueryReq.setTestMatrixIdList(reportMatrixIdList);
            testMatrixExtList = testMatrixExtService.query(testMatrixExtQueryReq);
        }
        if (Func.isNotEmpty(testMatrixExtList)) {
            Set<String> testRequestSets = Sets.newHashSet();
            testMatrixExtList.stream().forEach(item -> {
                if (Func.isEmpty(item.getProtocolTemplate())) {
                    return;
                }
                JSONObject protocolJSON = JSONObject.parseObject(item.getProtocolTemplate());
                if (Func.isEmpty(protocolJSON) || Func.isEmpty(protocolJSON.get("testRequest"))) {
                    return;
                }
                JSONArray testRequestArray = JSONObject.parseArray(protocolJSON.get("testRequest").toString());
                if (testRequestArray.size() == 0) {
                    return;
                }
                for (int i = 0; i < testRequestArray.size(); i++) {
                    JSONObject jsonObject = testRequestArray.getJSONObject(i);
                    String showRequest = jsonObject.getString("showRequest");
                    if (!Func.equalsSafe(showRequest, "true")) {
                        continue;
                    }
                    RdReportConclusionDTO rdReportConclusion = new RdReportConclusionDTO();
                    String requestConclusionSettingId = jsonObject.getString("requestConclusionSettingId");
                    if (Func.isNotEmpty(requestConclusionSettingId)) {
                        // 查询ConclusionSetting
                        QueryConclusionDetailReq conclusionDetailReq = new QueryConclusionDetailReq();
                        conclusionDetailReq.setId(requestConclusionSettingId);
                        conclusionDetailReq.setProductLineCode(context.getProductLineCode());
                        QueryConclusionDetailRsp conclusionDetailRsp = sodaNotesClient.getCustomerConclusionDetailInfo(conclusionDetailReq);
                        if (Func.isNotEmpty(conclusionDetailRsp)) {
                            rdReportConclusion.setConclusionId(conclusionDetailRsp.getId());
                        }
                        rdReportConclusion.setCustomerConclusionId(requestConclusionSettingId);
                    }
                    String itemKey = String.format("%s_%s_%s", Func.toStr(jsonObject.getString("requestContent")).hashCode(), Func.toStr(rdReportConclusion.getCustomerConclusionId()), sampleMatrixMap.get(item.getTestMatrixId()));
                    if (testRequestSets.contains(itemKey)) {
                        continue;
                    }
                    testRequestSets.add(itemKey);
                    rdReportConclusion.setOrderNo(context.getRdRootOrderNo());
                    rdReportConclusion.setRealOrderNo(orderNo);
                    rdReportConclusion.setRootOrderNo(context.getRdRootOrderNo());
                    rdReportConclusion.setSystemId(SgsSystem.GPO.getSgsSystemId());
                    rdReportConclusion.setConclusionInstanceId(IdUtil.uuId());
                    rdReportConclusion.setCustomerConclusion(jsonObject.getString("requestConclusion"));
                    rdReportConclusion.setObjectName(jsonObject.getString("requestContent"));
                    rdReportConclusion.setObjectId(jsonObject.getString("testRequestInstanceId"));
                    rdReportConclusion.setConclusionCode(revertConclusion(Func.toInteger(jsonObject.get("requestConclusion"), null)));
                    rdReportConclusion.setReportId(context.getParam().getReportId());
                    rdReportConclusion.setConclusionLevelId(ConclusionType.TestLine.getCode());
                    rdReportConclusion.setTestRequestFlag(1);
                    rdReportConclusion.setConclusionRemark(jsonObject.getString("conclusionRemark"));
                    reportConclusionList.add(rdReportConclusion);
                }
            });
        }
        importReportDataReq.setReportConclusionList(reportConclusionList);
    }

    private List<AttachmentBO> getFinalReport(List<AttachmentBO> attachmentBOList, String reportRequirement) {
        //送RD ReportList.reportFileList数据取值逻辑调整（基于ReportFileType判断）
        // ReportRequireMent = Customer_Report_PDF ，只推送1502 PDF给RD
        // ReportRequireMent = Sub_Report_Word，只推送 1501 && businessType=word Word给RD
        // ReportRequireMent =Customer_Report_Word，只推送 1501 && businessType=word Word给RD
        List<AttachmentBO> finalReport = attachmentBOList.stream().filter(attachmentBO ->
                Func.isNotEmpty(attachmentBO.getFileType()) &&
                        ((ReportRequirementEnum.check(reportRequirement, ReportRequirementEnum.Customer_Report_PDF) &&
                                ReportFileType.check(Integer.valueOf(attachmentBO.getFileType()), ReportFileType.PDF))
                                || (ReportRequirementEnum.check(reportRequirement, ReportRequirementEnum.Sub_Report_Word, ReportRequirementEnum.Customer_Report_Word) &&
                                ReportFileType.check(Integer.valueOf(attachmentBO.getFileType()), ReportFileType.Word) &&
                                Func.equals(attachmentBO.getBusinessType(), ReportFileType.Word.message())))
        ).collect(Collectors.toList());
        return finalReport;
    }

    private Map<String, List<RdProtocolEntryForm>> getProtocolEntryForm(ImportReportDataContext context) {
        Map<String, List<RdProtocolEntryForm>> dataMap = Maps.newHashMap();
        // 组装查询参数
        OutPutDataBO outPutData = context.getOutPutData();
        String orderId = outPutData.getOrder().getId().getOrderId();
        String productLineCode = outPutData.getOrder().getLab().getProductLineCode();
        Set<String> objectIdList = Sets.newHashSet();
        Map<String, String> objectSampleNoMap = Maps.newHashMap();
        Map<String, Long> objectPPMap = Maps.newHashMap();
        Map<String, String> objectSampleIdMap = Maps.newHashMap();
        if (Func.isNotEmpty(outPutData.getTestLineList())) {
            outPutData.getTestLineList().stream().filter(item -> Func.isNotEmpty(item.getRelationship()) &&
                            Func.isNotEmpty(item.getRelationship().getParallel()) && Func.isNotEmpty(item.getRelationship().getParallel().getTestSampleList()) && Func.isNotEmpty(item.getPpTestLineRelList()))
                    .forEach(testLine -> {
                        List<TestLineSampleBO> testSampleList = testLine.getRelationship().getParallel().getTestSampleList();
                        List<PPTestLineRelBO> ppTestLineRelList = testLine.getPpTestLineRelList().stream().filter(item -> Func.isNotEmpty(item.getId()) && Func.isNotEmpty(item.getId().getPpVersionId()) && item.getId().getPpVersionId() > 0).collect(Collectors.toList());
                        if (Func.isEmpty(ppTestLineRelList)) {
                            return;
                        }
                        ppTestLineRelList.stream().forEach(item -> testSampleList.stream().forEach(sample -> {
                                    String objId = String.format("%s%s", item.getId().getPpBaseId(), sample.getTestSampleInstanceId());
                                    objectIdList.add(objId);
                                    objectSampleNoMap.put(objId, sample.getTestSampleNo());
                                    objectSampleIdMap.put(objId, sample.getTestSampleInstanceId());
                                    objectPPMap.put(objId, item.getId().getPpBaseId());
                                }
                        ));
                    });
        }
        if (Func.isEmpty(objectIdList)) {
            return dataMap;
        }
        List<ProtocolEntryFormReqPairs> orderObjectPairs = Lists.newArrayList();
        objectIdList.stream().forEach(item -> {
            ProtocolEntryFormReqPairs protocolEntryFormReqPairs = new ProtocolEntryFormReqPairs();
            protocolEntryFormReqPairs.setOrderId(orderId);
            protocolEntryFormReqPairs.setObjectId(item);
            orderObjectPairs.add(protocolEntryFormReqPairs);
        });
        ProtocolEntryFormReq protocolEntryFormReq = new ProtocolEntryFormReq();
        protocolEntryFormReq.setProductLineCode(productLineCode);
        protocolEntryFormReq.setOrderObjectPairs(orderObjectPairs);
        BaseResponse<List<ProtocolEntryFormRsp>> protocolEntryFormRsp = dffClient.getProtocolEntryForm(protocolEntryFormReq);
        if (Func.isNotEmpty(protocolEntryFormRsp.getData())) {
            List<RdProtocolEntryForm> rdProtocolEntryFormList = Lists.newArrayList();
            protocolEntryFormRsp.getData().stream().forEach(item -> {
                RdProtocolEntryForm rdProtocolEntryForm = new RdProtocolEntryForm();
                rdProtocolEntryForm.setPpBaseId(objectPPMap.get(item.getObjectId()));
                rdProtocolEntryForm.setSampleNo(objectSampleNoMap.get(item.getObjectId()));
                rdProtocolEntryForm.setTestSampleInstanceId(objectSampleIdMap.get(item.getObjectId()));
                rdProtocolEntryForm.setTemplateId(item.getDffFormId());
                if (Func.isNotEmpty(item.getDffFieldValues())) {
                    List<DffFieldRsp> dffFields = item.getDffFieldValues().get(0);
                    if (Func.isNotEmpty(dffFields)) {
                        List<RdDffAttr> attrList = Lists.newArrayList();
                        dffFields.stream().forEach(dffField -> {
                            RdDffAttr rdDffAttr = new RdDffAttr();
                            rdDffAttr.setDataType(dffField.getFieldType());
                            rdDffAttr.setLabelCode(dffField.getFieldCode());
                            rdDffAttr.setSeq(dffField.getSequence());
                            rdDffAttr.setLabelName(dffField.getDisplayName());
                            rdDffAttr.setValue(dffField.getValue());
                            attrList.add(rdDffAttr);
                        });
                        rdProtocolEntryForm.setAttrList(attrList);
                    }
                }
                rdProtocolEntryFormList.add(rdProtocolEntryForm);
            });
            dataMap.put("protocolEntryForm", rdProtocolEntryFormList);
        }

        return dataMap;
    }

    private String revertConclusion(Integer code) {
        if (Func.isEmpty(code)) {
            return StringPool.EMPTY;
        }
        return ConclusionEnums.getMessage(code);
    }


    /**
     * testSampleList
     *
     * @param context
     * @param importReportDataReq
     */
    private void assembleTestSample(ImportReportDataContext context, ImportReportDataReq importReportDataReq) {
        OutPutDataBO outPutData = context.getOutPutData();
        if (Func.isEmpty(outPutData.getTestSampleList())) {
            importReportDataReq.setTestSampleList(Lists.newArrayList());
            return;
        }
        // Order下Sample全集
        Map<String, TestSampleBO> sourceSampleMaps = Maps.newHashMap();
        outPutData.getTestSampleList().stream().forEach(item -> sourceSampleMaps.put(item.getId().getTestSampleInstanceId(), item));
        // Mix样品中 Group顺序
        outPutData.getTestSampleList().stream().forEach(item -> {
            if (!SampleType.check(item.getHeader().getTestSampleType(), MixSample)) {
                return;
            }
            if (SampleGroupTypeEnums.check(item.getHeader().getGroupType(), SampleGroupTypeEnums.WITH)) {
                return;
            }
            List<TestSampleGroupBO> newTestSampleGroupList = Lists.newArrayList();
            List<TestSampleGroupBO> testSampleGroupList = item.getHeader().getTestSampleGroupList();
            String[] sampleNoArray = item.getId().getTestSampleNo().split("\\+");
            for (String sNo : sampleNoArray) {
                // 获取当前SampleNo对应的SampleId
                TestSampleBO thisTestSampleBO = outPutData.getTestSampleList().stream().filter(sampleBO -> Func.equalsSafe(sampleBO.getId().getTestSampleNo(), sNo)).findAny().orElse(null);
                if (Func.isNotEmpty(thisTestSampleBO)) {
                    TestSampleGroupBO testSampleGroup = testSampleGroupList.stream().filter(sg -> Func.equalsSafe(sg.getSampleGroupId(), thisTestSampleBO.getId().getTestSampleInstanceId())).findAny().orElse(null);
                    if (Func.isNotEmpty(testSampleGroup)) {
                        newTestSampleGroupList.add(testSampleGroup);
                    }
                }
            }
            if (Func.isNotEmpty(newTestSampleGroupList)) {
                item.getHeader().setTestSampleGroupList(newTestSampleGroupList);
            }
        });
        // 转换完SampleNo的数据
        // 1. 去C处理
        Map<String, RdTestSampleDTO> testSampleMaps = Maps.newHashMap();
        outPutData.getTestSampleList().stream().forEach(item -> doTraverseSampleInfo(testSampleMaps, sourceSampleMaps, item.getId().getTestSampleInstanceId()));

        List<RdTestSampleDTO> testSampleList = Lists.newArrayList();
        outPutData.getTestSampleList().stream().forEach(testSample -> {
            RdTestSampleDTO rdTestSample = new RdTestSampleDTO();
            rdTestSample.setRealOrderNo(testSample.getRelationship().getParent().getOrder().getOrderNo());
            rdTestSample.setOrderNo(context.getRdRootOrderNo());
            rdTestSample.setSystemId(SgsSystem.GPO.getSgsSystemId());
            rdTestSample.setTestSampleInstanceId(testSample.getId().getTestSampleInstanceId());
            rdTestSample.setParentTestSampleId(testSample.getHeader().getParentTestSampleId());
            rdTestSample.setTestSampleNo(testSample.getId().getTestSampleNo());
            if (Func.isNotEmpty(testSample.getId().getExternal())) {
                rdTestSample.setExternalSampleNo(testSample.getId().getExternal().getExternalSampleNo());
            }
            rdTestSample.setTestSampleType(testSample.getHeader().getTestSampleType());
            rdTestSample.setCategory(testSample.getHeader().getCategory());
            rdTestSample.setTestSampleSeq(testSample.getHeader().getTestSampleSeq());
            // testSampleGroupList
            if (Func.isNotEmpty(testSample.getHeader().getTestSampleGroupList())) {
                rdTestSample.setTestSampleGroupList(Func.copy(testSample.getHeader().getTestSampleGroupList(), TestSampleGroupBO.class, RdTestSampleGroupDTO.class));
            }
            // materialAttr
            if (Func.isNotEmpty(testSample.getMaterialAttr())) {
                RdMaterialAttrDTO rdMaterialAttr = new RdMaterialAttrDTO();
                Func.copy(testSample.getMaterialAttr(), rdMaterialAttr);
                if (Func.isNotEmpty(testSample.getMaterialAttr().getApplicableFlag())) {
                    rdMaterialAttr.setApplicableFlag(Integer.valueOf(testSample.getMaterialAttr().getApplicableFlag()));
                }
                if (Func.isNotEmpty(testSample.getMaterialAttr().getExtFields())) {
                    rdMaterialAttr.setExtFields(testSample.getMaterialAttr().getExtFields());
                }
                rdTestSample.setMaterialAttr(rdMaterialAttr);
            }
            // conclusion
            if (Func.isNotEmpty(testSample.getConclusion())) {
                rdTestSample.setConclusion(Func.copy(testSample.getConclusion(), RdConclusionDTO.class));
            }
            // external
            if (Func.isNotEmpty(testSample.getId().getExternal())) {
                TestSampleExternalBO external = testSample.getId().getExternal();
                RdTestSampleExternalDTO rdExternal = new RdTestSampleExternalDTO();
                rdExternal.setTestSampleNo(external.getExternalSampleNo());
                rdExternal.setTestSampleId(external.getExternalSampleId());
                rdTestSample.setExternal(rdExternal);
            }
            // testSamplePhoto
            if (Func.isNotEmpty(testSample.getAttachmentList())) {
                rdTestSample.setTestSamplePhoto(testSample.getAttachmentList().stream().map(item -> {
                    RdAttachmentDTO rdAttachmentDTO = Func.copy(item, RdAttachmentDTO.class);
                    rdAttachmentDTO.setAttachmentInstanceId(item.getId());
                    rdAttachmentDTO.setBizType(item.getBusinessType());
                    rdAttachmentDTO.setObjectType(item.getObjectType());
                    return rdAttachmentDTO;
                }).collect(Collectors.toList()));
            }
            rdTestSample.setRootOrderNo(context.getRdRootOrderNo());
            rdTestSample.setActiveIndicator(testSample.getActiveIndicator());
            // 2.拼接SampleName
            if (Func.isNotEmpty(testSampleMaps.get(rdTestSample.getTestSampleInstanceId()))) {
                RdTestSampleDTO testSampleDTO = testSampleMaps.get(rdTestSample.getTestSampleInstanceId());
                assembleSampleName(testSampleDTO, sourceSampleMaps);
                rdTestSample.setTestSampleName(testSampleDTO.getTestSampleName());
            }
            testSampleList.add(rdTestSample);
        });
        importReportDataReq.setTestSampleList(testSampleList);
    }


    private void assembleSampleName(RdTestSampleDTO rdTestSample, Map<String, TestSampleBO> sourceSampleMaps) {
        if (!sourceSampleMaps.containsKey(rdTestSample.getTestSampleInstanceId())) {
            return;
        }
        TestSampleBO testSample = sourceSampleMaps.get(rdTestSample.getTestSampleInstanceId());
        if (Func.isEmpty(testSample)) {
            return;
        }
        SampleType sampleType = SampleType.findType(testSample.getHeader().getTestSampleType());
        if (SampleType.check(sampleType, OriginalSample)) {
            rdTestSample.setTestSampleName(testSample.getId().getTestSampleNo());
            return;
        }
        if (SampleType.check(sampleType, MixSample)) {
            // Mix样品（多个样品组合）
            List<TestSampleGroupBO> testSampleGroupList = testSample.getHeader().getTestSampleGroupList();
            if (Func.isEmpty(testSampleGroupList)) {
                return;
            }
            if (Func.isNotEmpty(testSample.getHeader().getGroupType())
                    && SampleGroupTypeEnums.check(testSample.getHeader().getGroupType(), SampleGroupTypeEnums.WITH)) {
                testSampleGroupList = testSampleGroupList.stream()
                        .sorted(Comparator.comparing(TestSampleGroupBO::getSequence))
                        .collect(Collectors.toList());
            }

            String separator = Func.isNotEmpty(testSample.getHeader().getGroupType())
                    && SampleGroupTypeEnums.check(testSample.getHeader().getGroupType(), SampleGroupTypeEnums.WITH) ? "w/" : "\\+";
            String[] sampleNoArray = rdTestSample.getTestSampleNo().split(separator);
            List<String> newSampleList = Lists.newArrayList();
            int index = 0;
            for (TestSampleGroupBO testSampleGroupBO : testSampleGroupList) {
                TestSampleBO parentSample = sourceSampleMaps.get(testSampleGroupBO.getSampleGroupId());
                if (SampleType.check(parentSample.getHeader().getTestSampleType(), OriginalSample)) {
                    newSampleList.add(parentSample.getId().getTestSampleNo());
                } else if (!SampleType.check(parentSample.getHeader().getTestSampleType(), MixSample)) {
                    String sampleNo = getOriginalSample(testSampleGroupBO.getSampleGroupId(), sourceSampleMaps);
                    newSampleList.add(String.format("%s%s", sampleNo, sampleNoArray[index]));
                } else if (SampleGroupTypeEnums.check(parentSample.getHeader().getGroupType(), SampleGroupTypeEnums.WITH)) {
                    // With样
                    String[] sampleNoArray_ = sampleNoArray[index].split("w/");
                    int index_ = 0;
                    List<String> newSampleList_ = Lists.newArrayList();
                    List<TestSampleGroupBO> parentTestSampleGroupList = parentSample.getHeader().getTestSampleGroupList().stream()
                            .sorted(Comparator.comparing(TestSampleGroupBO::getSequence))
                            .collect(Collectors.toList());
                    for (TestSampleGroupBO parentTestSampleGroupBO : parentTestSampleGroupList) {
                        TestSampleBO groupSample = sourceSampleMaps.get(parentTestSampleGroupBO.getSampleGroupId());
                        if (SampleType.check(groupSample.getHeader().getTestSampleType(), OriginalSample)) {
                            newSampleList_.add(groupSample.getId().getTestSampleNo());
                        } else {
                            String sampleNo = getOriginalSample(parentTestSampleGroupBO.getSampleGroupId(), sourceSampleMaps);
                            newSampleList_.add(String.format("%s%s", sampleNo, sampleNoArray_[index_]));
                        }
                        index_++;
                    }
                    newSampleList.add(newSampleList_.stream().collect(Collectors.joining("w/")));
                } else {
                    // Mix样
                    log.info("Mix+Mix的场景暂时不支持");
                }
                index++;
            }
            String delimiter = Func.isNotEmpty(testSample.getHeader().getGroupType())
                    && SampleGroupTypeEnums.check(testSample.getHeader().getGroupType(), SampleGroupTypeEnums.WITH) ? "w/" : "+";
            rdTestSample.setTestSampleName(newSampleList.stream().collect(Collectors.joining(delimiter)));
        } else {
            String sampleName = getOriginalSample(rdTestSample.getTestSampleInstanceId(), sourceSampleMaps);
            rdTestSample.setTestSampleName(String.format("%s%s", sampleName, rdTestSample.getTestSampleNo()));
        }
    }


    /**
     * 递归查询
     *
     * @param testSampleId
     * @param sourceSampleMaps
     * @return
     */
    private String getOriginalSample(String testSampleId, Map<String, TestSampleBO> sourceSampleMaps) {
        TestSampleBO testSample = sourceSampleMaps.get(testSampleId);
        if (Func.isEmpty(testSample)) {
            return "";
        }
        SampleType sampleType = SampleType.findType(testSample.getHeader().getTestSampleType());
        if (sampleType == null) {
            return "";
        }
        if (SampleType.check(sampleType, SampleType.OriginalSample)) {
            return testSample.getId().getTestSampleNo();
        }
        if (SampleType.check(sampleType, SampleType.Sample)) {
            return getOriginalSample(testSample.getHeader().getParentTestSampleId(), sourceSampleMaps);
        }
        if (SampleType.check(sampleType, SampleType.SubSample)) {
            return getOriginalSample(testSample.getHeader().getParentTestSampleId(), sourceSampleMaps);
        }
        return null;
    }

    private void doTraverseSampleInfo(Map<String, RdTestSampleDTO> testSampleMaps, Map<String, TestSampleBO> sourceSampleMaps, String testSampleId) {
        if (sourceSampleMaps == null || sourceSampleMaps.isEmpty() || StringUtils.isBlank(testSampleId)) {
            return;
        }
        TestSampleBO sourceSample = sourceSampleMaps.get(testSampleId);
        if (sourceSample == null) {
            return;
        }
        SampleType sampleType = SampleType.findType(sourceSample.getHeader().getTestSampleType());
        if (sampleType == null) {
            return;
        }
        RdTestSampleDTO testSample = new RdTestSampleDTO();
        testSample.setTestSampleInstanceId(sourceSample.getId().getTestSampleInstanceId());
        String sampleNo = sourceSample.getId().getTestSampleNo();
        if (sampleType != SampleType.OriginalSample) {
            sampleNo = sampleNo.replace(sampleType.getCategoryChem(), "");
        }
        testSample.setTestSampleNo(sampleNo);

        if (SampleType.check(sampleType, OriginalSample, Sample, SubSample)) {

            if (StringUtils.isNotBlank(sourceSample.getHeader().getParentTestSampleId())) {
                this.doTraverseSampleInfo(testSampleMaps, sourceSampleMaps, sourceSample.getHeader().getParentTestSampleId());
            }
            if (testSampleMaps.containsKey(testSample.getTestSampleInstanceId())) {
                return;
            }
            testSampleMaps.put(testSample.getTestSampleInstanceId(), testSample);
            return;
        }
        List<TestSampleGroupBO> sampleGroupIds = sourceSample.getHeader().getTestSampleGroupList();
        if (sampleGroupIds == null || sampleGroupIds.isEmpty()) {
            return;
        }
        for (TestSampleGroupBO sampleGroup : sampleGroupIds) {

            this.doTraverseSampleInfo(testSampleMaps, sourceSampleMaps, sampleGroup.getSampleGroupId());
        }
        if (testSampleMaps.containsKey(testSample.getTestSampleInstanceId())) {
            return;
        }
        testSampleMaps.put(testSample.getTestSampleInstanceId(), testSample);
    }

    /**
     * testLineList
     *
     * @param context
     * @param importReportDataReq
     */
    private void assembleTestLine(ImportReportDataContext context, ImportReportDataReq importReportDataReq) {
        OutPutDataBO outPutData = context.getOutPutData();

        if (Func.isEmpty(outPutData.getTestLineList())) {
            importReportDataReq.setTestLineList(Lists.newArrayList());
            return;
        }
        List<RdTestLineDTO> testLineList = Lists.newArrayList();
        outPutData.getTestLineList().stream().forEach(testLineBO -> {
            RdTestLineDTO rdTestLineDTO = new RdTestLineDTO();
            // id header
            rdTestLineDTO.setOrderNo(context.getRdRootOrderNo());
            rdTestLineDTO.setRealOrderNo(testLineBO.getRelationship().getParent().getOrder().getOrderNo());
            rdTestLineDTO.setSystemId(SgsSystem.GPO.getSgsSystemId());
            rdTestLineDTO.setTestLineInstanceId(testLineBO.getId().getTestLineInstanceId());
            rdTestLineDTO.setTestItemNo(testLineBO.getId().getTestItemNo());
            rdTestLineDTO.setTestLineType(testLineBO.getHeader().getTestLineType());
            rdTestLineDTO.setTestLineBaseId(testLineBO.getId().getTestLineBaseId());
            rdTestLineDTO.setTestLineId(testLineBO.getId().getTestLineId());
            rdTestLineDTO.setTestLineVersionId(testLineBO.getId().getTestLineVersionId());
            rdTestLineDTO.setEvaluationAlias(testLineBO.getHeader().getEvaluationName());
            rdTestLineDTO.setEvaluationName(testLineBO.getHeader().getEvaluationName());
            rdTestLineDTO.setTestLineStatus(testLineBO.getHeader().getTestLineStatus());
            rdTestLineDTO.setTestLineSeq(testLineBO.getHeader().getTestLineSeq());
            // 1 v 多 场景怎么处理
            if (Func.isNotEmpty(testLineBO.getLabSectionList())) {
                rdTestLineDTO.setLabSectionBaseId(testLineBO.getLabSectionList().get(0).getLabSectionBaseId());
            }
            rdTestLineDTO.setLabTeam(testLineBO.getHeader().getLabTeam());
            rdTestLineDTO.setProductLineAbbr(testLineBO.getHeader().getProductLineAbbr());
            rdTestLineDTO.setTestLineRemark(testLineBO.getHeader().getTestLineRemark());
            // external
            if (Func.isNotEmpty(testLineBO.getId().getExternal())) {
                rdTestLineDTO.setExternal(Func.copy(testLineBO.getId().getExternal(), RdTestLineExternalDTO.class));
            }
            // citation
            if (Func.isNotEmpty(testLineBO.getCitation())) {
                RdCitationDTO citation = Func.copy(testLineBO.getCitation(), RdCitationDTO.class);
                if (Func.isNotEmpty(testLineBO.getCitation().getLanguageList())) {
                    citation.setLanguageList(Func.copy(testLineBO.getCitation().getLanguageList(), CitationLanguageBO.class, RdCitationLanguageDTO.class));
                }
                rdTestLineDTO.setCitation(citation);
            }
            // wi
            if (Func.isNotEmpty(testLineBO.getWiList())) {
                List<WorkingInstructionBO> wiList = testLineBO.getWiList();
                WorkingInstructionBO wiForCS = wiList.stream().filter(wi -> CategoryEnums.checkCode(wi.getCategoryId(), CategoryEnums.WIForCS)).findAny().orElse(null);
                WorkingInstructionBO wiForTest = wiList.stream().filter(wi -> CategoryEnums.checkCode(wi.getCategoryId(), CategoryEnums.WIForTesting)).findAny().orElse(null);
                WorkingInstructionBO wiForSample = wiList.stream().filter(wi -> CategoryEnums.checkCode(wi.getCategoryId(), CategoryEnums.SamplePreparation_Cutting, CategoryEnums.SamplePreparation_Injection, CategoryEnums.SamplePreparation_Tableting)).findAny().orElse(null);
                RdWiDTO rdWi = new RdWiDTO();
                if (Func.isNotEmpty(wiForCS)) {
                    rdWi.setWiForCS(wiForCS.getWorkingInstructionText());
                }
                if (Func.isNotEmpty(wiForTest)) {
                    rdWi.setWiForTest(wiForTest.getWorkingInstructionText());
                }
                if (Func.isNotEmpty(wiForSample)) {
                    rdWi.setWiForSample(wiForSample.getWorkingInstructionText());
                }
                rdTestLineDTO.setWi(rdWi);
            }
            // analyteList
            if (Func.isNotEmpty(testLineBO.getAnalyteList())) {
                List<RdAnalyteDTO> analyteList = Lists.newArrayList();
                // id header
                testLineBO.getAnalyteList().stream().forEach(analyteBO -> {
                    RdAnalyteDTO rdAnalyteDTO = new RdAnalyteDTO();
                    rdAnalyteDTO.setAnalyteInstanceId(analyteBO.getId().getAnalyteInstanceId());
                    rdAnalyteDTO.setAnalyteBaseId(analyteBO.getId().getAnalyteBaseId());
                    rdAnalyteDTO.setAnalyteId(analyteBO.getId().getAnalyteId());
                    rdAnalyteDTO.setAnalyteName(analyteBO.getHeader().getAnalyteName());
                    rdAnalyteDTO.setAnalyteSeq(analyteBO.getHeader().getAnalyteSeq());
                    rdAnalyteDTO.setUnitBaseId(analyteBO.getHeader().getUnitBaseId());
                    rdAnalyteDTO.setReportUnit(analyteBO.getHeader().getReportUnit());
                    rdAnalyteDTO.setCasNo(analyteBO.getHeader().getCasNo());
                    // languageList
                    if (Func.isNotEmpty(analyteBO.getHeader().getLanguageList())) {
                        rdAnalyteDTO.setLanguageList(Func.copy(analyteBO.getHeader().getLanguageList(), AnalyteLanguageBO.class, RdAnalyteLanguageDTO.class));
                    }
                    analyteList.add(rdAnalyteDTO);
                });
                rdTestLineDTO.setAnalyteList(analyteList);
            }
            // ppTestLineRelList
            if (Func.isNotEmpty(testLineBO.getPpTestLineRelList())) {
                List<RdPpTestLineRelDTO> ppTestLineRelList = Lists.newArrayList();
                testLineBO.getPpTestLineRelList().stream().forEach(ppTestLineRelBO -> {
                    RdPpTestLineRelDTO rdPpTestLineRelDTO = new RdPpTestLineRelDTO();
                    rdPpTestLineRelDTO.setPpTlRelId(ppTestLineRelBO.getId().getPpTlRelId());
                    rdPpTestLineRelDTO.setPpArtifactRelId(ppTestLineRelBO.getId().getPpArtifactRelId());
                    rdPpTestLineRelDTO.setPpBaseId(ppTestLineRelBO.getId().getPpBaseId());
                    rdPpTestLineRelDTO.setRootPPBaseId(ppTestLineRelBO.getId().getRootPPBaseId());
                    rdPpTestLineRelDTO.setPpNo(ppTestLineRelBO.getId().getPpNo());
                    rdPpTestLineRelDTO.setPpVersionId(ppTestLineRelBO.getId().getPpVersionId());
                    rdPpTestLineRelDTO.setPpName(ppTestLineRelBO.getHeader().getPpName());
                    rdPpTestLineRelDTO.setPpNotes(ppTestLineRelBO.getHeader().getPpNotes());
                    rdPpTestLineRelDTO.setSectionId(ppTestLineRelBO.getHeader().getSectionId());
                    rdPpTestLineRelDTO.setSectionLevel(ppTestLineRelBO.getHeader().getSectionLevel());
                    rdPpTestLineRelDTO.setSectionName(ppTestLineRelBO.getHeader().getSectionName());
                    if (Func.isNotEmpty(ppTestLineRelBO.getHeader().getAid())) {
                        rdPpTestLineRelDTO.setAid(Long.valueOf(ppTestLineRelBO.getHeader().getAid()));
                    }
                    if (Func.isNotEmpty(ppTestLineRelBO.getHeader().getLanguageList())) {
                        rdPpTestLineRelDTO.setLanguageList(Func.copy(ppTestLineRelBO.getHeader().getLanguageList(), PPLanguageBO.class, RdPpTestLineRelLangDTO.class));
                    }
                    ppTestLineRelList.add(rdPpTestLineRelDTO);
                });
                rdTestLineDTO.setPpTestLineRelList(ppTestLineRelList);
            }
            // conclusion
            if (Func.isNotEmpty(testLineBO.getConclusion())) {
                rdTestLineDTO.setConclusion(Func.copy(testLineBO.getConclusion(), RdConclusionDTO.class));
            }
            // languageList
            if (Func.isNotEmpty(testLineBO.getHeader().getLanguageList())) {
                rdTestLineDTO.setLanguageList(Func.copy(testLineBO.getHeader().getLanguageList(), TestLineLanguageBO.class, RdTestLineLanguageDTO.class));
            }
            rdTestLineDTO.setRootOrderNo(context.getRdRootOrderNo());
            if (Func.isNotEmpty(testLineBO.getLabSectionList())) {
                rdTestLineDTO.setLabSectionName(testLineBO.getLabSectionList().stream().map(LabSectionBO::getLabSectionName)
                        .collect(Collectors.joining(",")));
            }
            rdTestLineDTO.setDigitalFlag(Constants.GPO_DIGITAL_FLAG);
            testLineList.add(rdTestLineDTO);
        });
        importReportDataReq.setTestLineList(testLineList);
    }


    /**
     * testResultList
     *
     * @param context
     * @param importReportDataReq
     */
    private void assembleTestResult(ImportReportDataContext context, ImportReportDataReq importReportDataReq) {
        OutPutDataBO outPutData = context.getOutPutData();
        if (Func.isEmpty(outPutData) || Func.isEmpty(outPutData.getTestData()) || Func.isEmpty(outPutData.getTestData().getTestResultList())) {
            importReportDataReq.setTestResultList(Lists.newArrayList());
            return;
        }
        List<RdTestResultDTO> testResultList = Lists.newArrayList();
        outPutData.getTestData().getTestResultList().stream().forEach(testResultBO -> {
            RdTestResultDTO rdTestResultDTO = new RdTestResultDTO();
            rdTestResultDTO.setOrderNo(context.getRdRootOrderNo());
            rdTestResultDTO.setRealOrderNo(context.getOrderNo());
            rdTestResultDTO.setSystemId(SgsSystem.GPO.getSgsSystemId());
            rdTestResultDTO.setTestMatrixId(testResultBO.getTestMatrixId());
            rdTestResultDTO.setSubReportNo(testResultBO.getSubReportNo());
            rdTestResultDTO.setTestResultSeq(testResultBO.getTestResultSeq());
            // testResult
            RdTestResultResultDTO rdTestResultResultDTO = new RdTestResultResultDTO();
            if (Func.isNotEmpty(testResultBO.getTestResult())) {
                rdTestResultResultDTO = Func.copy(testResultBO.getTestResult(), RdTestResultResultDTO.class);
                if (Func.isNotEmpty(testResultBO.getTestResult())) {
                    rdTestResultResultDTO.setTestResultFullNameRel(Func.copy(testResultBO.getTestResult().getTestResultFullNameRel(), RdTestResultNameDTO.class));
                }
            }
            // failRemark reportRemark
            rdTestResultResultDTO.setReportRemark(testResultBO.getTestLineRemark());
            rdTestResultResultDTO.setFailRemark(testResultBO.getTestLineFailRemark());
            rdTestResultDTO.setTestResult(rdTestResultResultDTO);
            // methodLimit
            // reportLimit
            if (Func.isNotEmpty(testResultBO.getReportLimit())) {
                RdReportLimitDTO reportLimit = Func.copy(testResultBO.getReportLimit(), RdReportLimitDTO.class);
                if (Func.isNotEmpty(testResultBO.getReportLimit().getLimitValueFullNameRel())) {
                    reportLimit.setLimitValueFullNameRel(Func.copy(testResultBO.getReportLimit().getLimitValueFullNameRel(), RdLimitValueFullNameRelDTO.class));
                }
                rdTestResultDTO.setReportLimit(reportLimit);
            }
            // languageList
            if (Func.isNotEmpty(testResultBO.getLanguageList())) {
                rdTestResultDTO.setLanguageList(Func.copy(testResultBO.getLanguageList(), TestResultLanguageBO.class, RdTestResultLanguageDTO.class));
            }
            rdTestResultDTO.setRootOrderNo(context.getRdRootOrderNo());
            rdTestResultDTO.setTestResultInstanceId(testResultBO.getTestResultInstanceId());
            // shareDataRefer
            // 查询当前TestMatrix对应的TestSample
            if (Func.isNotEmpty(outPutData.getTestMatrixList())) {
                TestMatrixBO testMatrixBO = outPutData.getTestMatrixList().stream().filter(item -> Func.equalsSafe(item.getId().getTestMatrixId(), rdTestResultDTO.getTestMatrixId())).findAny().orElse(null);
                if (Func.isNotEmpty(testMatrixBO)) {
                    String testSampleInsId = testMatrixBO.getRelationship().getParent().getTestSample().getTestSampleInstanceId();
                    String testLineInstanceId = testMatrixBO.getRelationship().getParent().getTestLine().getTestLineInstanceId();
                    if (Func.isNotEmpty(outPutData.getTestSampleList())) {
                        TestSampleBO testSampleBO = outPutData.getTestSampleList().stream().filter(item -> Func.equalsSafe(item.getId().getTestSampleInstanceId(), testSampleInsId)).findAny().orElse(null);
                        if (Func.isNotEmpty(testSampleBO) && Func.isNotEmpty(testSampleBO.getRelationship().getParent()) && Func.isNotEmpty(testSampleBO.getRelationship().getParent().getReferSample())) {
                            RdShareDataReferDTO rdShareDataRefer = new RdShareDataReferDTO();
                            // 匹配referenceSample
                            rdShareDataRefer.setReferFromSampleNo(testSampleBO.getRelationship().getParent().getReferSample().getSourceSampleNo());
                            // 匹配referenceReport
                            if (Func.isNotEmpty(outPutData.getTestLineList())) {
                                // 当前TestLine信息
                                TestLineBO testLineBO = outPutData.getTestLineList().stream().filter(item -> Func.equalsSafe(item.getId().getTestLineInstanceId(), testLineInstanceId)).findAny().orElse(null);
                                if (Func.isNotEmpty(testLineBO) && Func.isNotEmpty(testLineBO.getCitation())) {
                                    String orderNo = testSampleBO.getRelationship().getParent().getReferSample().getSourceOrderNo();
                                    String testSampleInstanceId = testSampleBO.getRelationship().getParent().getReferSample().getSourceSampleId();
                                    Integer testLineVersionId = testLineBO.getId().getTestLineVersionId();
                                    Integer citationVersionId = testLineBO.getCitation().getCitationVersionId();
                                    // 根据条件查询关联的报告
                                    ReferenceReportQueryReq referenceReportQueryReq = new ReferenceReportQueryReq();
                                    referenceReportQueryReq.setTestSampleId(testSampleInstanceId);
                                    referenceReportQueryReq.setCitationVersionId(citationVersionId);
                                    referenceReportQueryReq.setTestLineVersionId(testLineVersionId);
                                    referenceReportQueryReq.setOrderNo(orderNo);
                                    List<ReportPO> referReportList = reportService.queryReferReportList(referenceReportQueryReq);
                                    if (Func.isNotEmpty(referReportList)) {
                                        Set<String> referFromReportSets = referReportList.stream().map(ReportPO::getReportNo).collect(Collectors.toSet());
                                        rdShareDataRefer.setReferFromReportNo(referFromReportSets.stream().collect(Collectors.toList()));
                                    }
                                }
                            }
                            rdTestResultDTO.setShareDataRefer(rdShareDataRefer);
                        }
                    }
                }
            }
            testResultList.add(rdTestResultDTO);
        });
        importReportDataReq.setTestResultList(testResultList);
    }

    /**
     * conditionGroupList
     *
     * @param context
     * @param importReportDataReq
     */
    private void assembleConditionGroup(ImportReportDataContext context, ImportReportDataReq importReportDataReq) {
        String orderNo = context.getOrderNo();
        OutPutDataBO outPutData = context.getOutPutData();
        if (Func.isEmpty(outPutData.getConditionGroupList())) {
            importReportDataReq.setConditionGroupList(Lists.newArrayList());
            return;
        }
        List<RdConditionGroupDTO> conditionGroupList = Lists.newArrayList();
        outPutData.getConditionGroupList().stream().forEach(conditionGroupBO -> {
            RdConditionGroupDTO rdConditionGroup = new RdConditionGroupDTO();
            rdConditionGroup.setRealOrderNo(orderNo);
            rdConditionGroup.setOrderNo(context.getRdRootOrderNo());
            rdConditionGroup.setSystemId(SgsSystem.GPO.getSgsSystemId());
            rdConditionGroup.setConditionGroupId(conditionGroupBO.getId().getConditionGroupId());
            rdConditionGroup.setCombinedConditionDescription(conditionGroupBO.getHeader().getCombinedConditionDescription());
            rdConditionGroup.setRequirement(conditionGroupBO.getHeader().getRequirement());
            //ppConditionGroupList
            //languageList
            if (Func.isNotEmpty(conditionGroupBO.getHeader().getLanguageList())) {
                rdConditionGroup.setLanguageList(Func.copy(conditionGroupBO.getHeader().getLanguageList(), ConditionGroupLanguageBO.class, RdConditionGroupLanguageDTO.class));
            }
            rdConditionGroup.setRootOrderNo(context.getRdRootOrderNo());
            conditionGroupList.add(rdConditionGroup);
        });
        importReportDataReq.setConditionGroupList(conditionGroupList);
    }


    /**
     * quotationList
     *
     * @param importReportDataReq
     */
    private void assembleQuotation(ImportReportDataContext context, ImportReportDataReq importReportDataReq) {
        String orderNo = context.getOrderNo();
        String token = context.getToken();
        OutPutDataBO outPutData = context.getOutPutData();
        List<BossOrderInvoiceDTO> bossOrderInvoiceDTOList = bossOrderService.getBossOrderNoByOrderNo(context.getOrderNo());
        GetQuotationInfoReq quotationInfoReq = new GetQuotationInfoReq();
        quotationInfoReq.setOrderNoList(Lists.newArrayList(orderNo));
        quotationInfoReq.setQuotationStatusList(Lists.newArrayList(QuotationStatus.CONFIRM.getCode(), QuotationStatus.DRAFT.getCode(), QuotationStatus.GENERATE.getCode(), QuotationStatus.CLOSED.getCode()));
        quotationInfoReq.setSgsToken(token);
        quotationInfoReq.setSystemId(SgsSystem.GPO.getSgsSystemId());
        quotationInfoReq.setProductLineCode(context.getLab().getBuCode());
        BaseResponse<List<OrderQuotationRsp>> orderQuotationRsp = quotationClient.getQuotationInfoList(quotationInfoReq);
        if (Func.isEmpty(orderQuotationRsp) || Func.isEmpty(orderQuotationRsp.getData())) {
            importReportDataReq.setQuotationList(Lists.newArrayList());
            return;
        }
        // 订单上payer设置
        RdCustomerDTO payer = new RdCustomerDTO();
        if (Func.isNotEmpty(outPutData.getOrder()) && Func.isNotEmpty(outPutData.getOrder().getCustomerList())) {
            CustomerBO payerBO = outPutData.getOrder().getCustomerList().stream().filter(customerBO -> Func.equalsSafe(CustomerType.Payer.getStatus(), customerBO.getCustomerUsage())).findAny().orElse(null);
            if (Func.isNotEmpty(payerBO)) {
                Func.copy(payerBO, payer);
                payer.setCustomerInstanceId(payerBO.getId());
                if (Func.isNotEmpty(payerBO.getCustomerContactList())) {
                    List<RdCustomerContactDTO> customerContactList = payerBO.getCustomerContactList().stream().map(item -> {
                        RdCustomerContactDTO rdCustomerContactDTO = Func.copy(item, RdCustomerContactDTO.class);
                        rdCustomerContactDTO.setSgsUserId(item.getContactSgsMartUserId());
                        rdCustomerContactDTO.setSgsAccountCode(item.getContactSgsMartAccount());
                        return rdCustomerContactDTO;
                    }).collect(Collectors.toList());
                    payer.setCustomerContactList(customerContactList);
                }
                if (Func.isNotEmpty(payerBO.getLanguageList())) {
                    payer.setLanguageList(Func.copy(payerBO.getLanguageList(), CustomerLanguageBO.class, RdCustomerLanguageDTO.class));
                }
            }
        }
        List<RdQuotationDTO> quotationList = Lists.newArrayList();
        orderQuotationRsp.getData().get(0).getQuotationList().stream().forEach(quotation -> {
            RdQuotationDTO rdQuotationDTO = new RdQuotationDTO();
            rdQuotationDTO.setQuotationInstanceId(quotation.getId());
            rdQuotationDTO.setRealOrderNo(orderNo);
            rdQuotationDTO.setOrderNo(context.getRdRootOrderNo());
            rdQuotationDTO.setSystemId(SgsSystem.GPO.getSgsSystemId());
            rdQuotationDTO.setQuotationNo(quotation.getQuotationNo());
            rdQuotationDTO.setPayer(payer);
            rdQuotationDTO.setCurrency(quotation.getCurrency());
            rdQuotationDTO.setNetAmount(quotation.getNetAmount());
            rdQuotationDTO.setVatAmount(quotation.getTaxAmount());
            rdQuotationDTO.setTotalAmount(quotation.getTotalAmount());
            rdQuotationDTO.setDiscount(quotation.getOrderDiscount());
            rdQuotationDTO.setAdjustmentAmount(quotation.getAdjustAmount());
            rdQuotationDTO.setFinalAmount(quotation.getFinalAmount());
            if (Func.isNotEmpty(quotation.getQuotationVersion())) {
                rdQuotationDTO.setQuotationVersionId(quotation.getQuotationVersion().toString());
            }
            rdQuotationDTO.setQuotationStatus(quotation.getStatus());
            //serviceItemList
            if (Func.isNotEmpty(quotation.getServiceItemList())) {
                List<RdServiceItemDTO> serviceItemList = Lists.newArrayList();
                quotation.getServiceItemList().stream().forEach(item -> {
                    RdServiceItemDTO rdServiceItem = new RdServiceItemDTO();
                    rdServiceItem.setServiceItemInstanceId(item.getId());
                    rdServiceItem.setServiceItemName(item.getChargeName());
                    rdServiceItem.setPpNo(item.getPpNo());
                    rdServiceItem.setTestLineId(item.getTestLineId());
                    rdServiceItem.setCitationId(item.getCitationId());
                    rdServiceItem.setCitationType(item.getCitationType());
                    rdServiceItem.setCitationName(item.getCitationName());
                    rdServiceItem.setEvaluationAlias(item.getChargeName());
                    rdServiceItem.setServiceItemListUnitPrice(item.getUnitPrice());
                    rdServiceItem.setServiceItemSalesUnitPrice(item.getSalesUnitPrice());
                    rdServiceItem.setServiceItemDiscount(item.getSalesDiscount());
                    rdServiceItem.setQuantity(item.getQuantity());
                    rdServiceItem.setServiceItemNetAmount(item.getNetAmount());
                    rdServiceItem.setServiceItemTotalAmount(item.getAfterTaxAmount());
                    rdServiceItem.setServiceItemVATAmount(item.getTaxAmount());
                    rdServiceItem.setServiceItemType(Func.toStr(item.getServiceItemType()));
                    rdServiceItem.setServiceItemSeq(item.getSequenceNo());
                    rdServiceItem.setExchangeRate(item.getExchangeRate());
                    rdServiceItem.setPpName(item.getPpName());
                    rdServiceItem.setSurCharge(item.getSurCharge());
                    // languageList
                    if (Func.isNotEmpty(item.getLanguageList())) {
                        rdServiceItem.setLanguageList(Func.copy(item.getLanguageList(), ServiceItemLanguageRsp.class, RdServiceItemLanguageDTO.class));
                    }
                    serviceItemList.add(rdServiceItem);
                });
                rdQuotationDTO.setServiceItemList(serviceItemList);
            }
            if (Func.isNotEmpty(bossOrderInvoiceDTOList)) {
                List<BossOrderInvoiceDTO> bossOrderInvoiceList = bossOrderInvoiceDTOList.stream().filter(e -> Func.equalsSafe(e.getQuotationNo(), quotation.getQuotationNo())).collect(Collectors.toList());
                if (Func.isNotEmpty(bossOrderInvoiceList)) {
                    List<RdQuotationRelationshipDTO.RdQuotationRelParallelDTO.RdQuotationRelParallelOrderDTO> bossOrderList = new ArrayList<>();
                    bossOrderInvoiceList.stream().forEach(bossOrderInvoiceDTO -> {
                        RdQuotationRelationshipDTO.RdQuotationRelParallelDTO.RdQuotationRelParallelOrderDTO bossOrder = new RdQuotationRelationshipDTO.RdQuotationRelParallelDTO.RdQuotationRelParallelOrderDTO();
                        if (Func.isNotEmpty(bossOrderInvoiceDTO.getBossOrderNo())) {
                            bossOrder.setBossOrderNo(bossOrderInvoiceDTO.getBossOrderNo());
                            bossOrderList.add(bossOrder);
                        }
                    });
                    RdQuotationRelationshipDTO relationshipDTO = new RdQuotationRelationshipDTO();
                    RdQuotationRelationshipDTO.RdQuotationRelParallelDTO parallelDTO = new RdQuotationRelationshipDTO.RdQuotationRelParallelDTO();
                    parallelDTO.setBossOrderList(bossOrderList);
                    relationshipDTO.setParallel(parallelDTO);
                    rdQuotationDTO.setRelationship(relationshipDTO);
                }
            }
            rdQuotationDTO.setRootOrderNo(context.getRdRootOrderNo());
            //quotationFileList
            quotationList.add(rdQuotationDTO);
        });
        importReportDataReq.setQuotationList(quotationList);
    }


    /**
     * invoiceList
     *
     * @param
     * @param importReportDataReq
     */
    private void assembleInvoice(ImportReportDataContext context, ImportReportDataReq importReportDataReq) {
        String orderNo = context.getOrderNo();
        List<RdInvoiceDTO> invoiceList = Lists.newArrayList();
        List<BossOrderInvoiceDTO> allBossOrderInvoicePOList = bossOrderInvoiceService.getBossInvoiceByOrderNo(orderNo).getData();
        if (Func.isNotEmpty(allBossOrderInvoicePOList)) {
            Map<String, List<BossOrderInvoiceDTO>> invoiceMap = allBossOrderInvoicePOList.stream().collect(Collectors.groupingBy(BossOrderInvoiceDTO::getInvoiceNo));
            for (Map.Entry<String, List<BossOrderInvoiceDTO>> entry : invoiceMap.entrySet()) {
                String key = entry.getKey();
                List<BossOrderInvoiceDTO> bossOrderInvoiceDTOList = entry.getValue();
                if (Func.isNotEmpty(key) && Func.isNotEmpty(bossOrderInvoiceDTOList)) {
                    RdInvoiceDTO rdInvoice = new RdInvoiceDTO();
                    rdInvoice.setRealOrderNo(orderNo);
                    rdInvoice.setOrderNo(context.getRdRootOrderNo());
                    rdInvoice.setSystemId(SgsSystem.GPO.getSgsSystemId());
                    rdInvoice.setInvoiceNo(key);
                    rdInvoice.setInvoiceInstanceId(bossOrderInvoiceDTOList.get(0).getInvoiceId());
                    List<String> quotationNoList = bossOrderInvoiceDTOList.stream().map(BossOrderInvoiceDTO::getQuotationNo).distinct().sorted().collect(Collectors.toList());
                    if (Func.isEmpty(quotationNoList)) {
                        continue;
                    }
                    rdInvoice.setQuotationNos(quotationNoList);
                    rdInvoice.setCurrency(bossOrderInvoiceDTOList.get(0).getCurrencyCode());
                    rdInvoice.setTotalAmount(bossOrderInvoiceDTOList.get(0).getInvoiceAmount());
                    rdInvoice.setRootOrderNo(context.getRdRootOrderNo());
                    rdInvoice.setBossOrderNo(bossOrderInvoiceDTOList.get(0).getBossOrderNo());
                    invoiceList.add(rdInvoice);
                }
            }
        }
        importReportDataReq.setInvoiceList(invoiceList);
    }


    @Override
    public BaseResponse execute(ImportReportDataContext context) {
        // call rd.import
        if (Func.isNotEmpty(context.getImportReportDataReq())) {
            BaseResponse response = rdClient.importReportData(context.getImportReportDataReq());
            SystemLog resultLog = new SystemLog();
            resultLog.setObjectType(ObjectType.Report.getCode());
            resultLog.setObjectNo(context.getActualReportNo());
            resultLog.setProductLineCode(context.getProductLineCode());
            resultLog.setType(SystemLogType.API.getType());
            resultLog.setRemark("Call RD import");
            resultLog.setResponse(JSON.toJSONString(response));
            resultLog.setRequest(JSON.toJSONString(context.getImportReportDataReq()));
            resultLog.setOperationType("RD import");
            resultLog.setLocationCode(context.getLab().getLocationCode());
            resultLog.setCreateBy(SecurityContextHolder.getUserInfoFillSystem().getRegionAccount());
            systemLogHelper.save(resultLog);
            List<String> email = supportConfig.getEmail();
            if (response.isFail() && Func.isNotEmpty(email)) {
                String reportNos = context.getImportReportDataReq().getReportList().parallelStream().map(RdReportDTO::getReportNo).collect(Collectors.joining(","));
                //发送邮件通知Support
                SendEmailReq sendEmailReq = new SendEmailReq();
                StringBuilder subject = new StringBuilder();
                subject.append("【").append(reportNos).append("】");
                subject.append("RD Import失败，请及时关注处理！");
                sendEmailReq.setMailSubject(subject.toString());
                sendEmailReq.setLabCode(context.getLab().getLabCode());
                sendEmailReq.setBuCode(context.getLab().getBuCode());
                sendEmailReq.setMailTo(email);
                sendEmailReq.setSystemId(Func.toStr(SgsSystem.GPO.getSgsSystemId()));
                StringBuilder mailText = new StringBuilder();
                mailText.append("error message detail:");
                mailText.append(response.getMessage());
                sendEmailReq.setMailText(mailText.toString());
                List<String> mailTo = Lists.newArrayList();
                mailTo.add(Constants.GPO_Support);
                notificationClient.sendEmail(sendEmailReq);

            }
            return response;
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse after(ImportReportDataContext context) {
        return BaseResponse.newSuccessInstance(true);
    }
}
