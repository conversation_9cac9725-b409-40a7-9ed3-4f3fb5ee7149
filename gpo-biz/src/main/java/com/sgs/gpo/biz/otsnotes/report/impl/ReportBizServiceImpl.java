package com.sgs.gpo.biz.otsnotes.report.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.base.command.BaseExecutor;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.model.Lab;
import com.sgs.framework.i18n.util.MessageUtil;
import com.sgs.framework.model.common.lab.LabBO;
import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.framework.model.enums.RefIntegrationChannel;
import com.sgs.framework.model.enums.ReportReviseType;
import com.sgs.framework.model.order.v2.OrderBO;
import com.sgs.framework.model.order.v2.OrderIdRelBO;
import com.sgs.framework.model.report.report.v2.ReportBO;
import com.sgs.framework.model.report.report.v2.ReportIdBO;
import com.sgs.framework.model.report.report.v2.ReportParentBO;
import com.sgs.framework.model.report.report.v2.ReportRelationBO;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.biz.otsnotes.report.IReportBizService;
import com.sgs.gpo.biz.otsnotes.report.command.PrelimReportGenerateBizCMD;
import com.sgs.gpo.biz.otsnotes.report.command.PrelimReportQueryExpireDataBizCMD;
import com.sgs.gpo.biz.otsnotes.report.command.ReportDefaultAccreditationQueryBizCMD;
import com.sgs.gpo.biz.otsnotes.report.command.ReportGenerateBizCMD;
import com.sgs.gpo.biz.otsnotes.report.command.ReportQueryBySampleBarCodeBizCMD;
import com.sgs.gpo.core.config.InterfaceConfig;
import com.sgs.gpo.core.constants.BizLogConstant;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.enums.ReportStatus;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO;
import com.sgs.gpo.domain.service.extservice.sci.SciTrfContext;
import com.sgs.gpo.domain.service.extservice.sci.command.SciSyncTrfCMD;
import com.sgs.gpo.domain.service.otsnotes.report.IReportDomainService;
import com.sgs.gpo.domain.service.otsnotes.report.command.ReportPendingBizCMD;
import com.sgs.gpo.domain.service.otsnotes.report.command.ReportUnPendingBizCMD;
import com.sgs.gpo.domain.service.otsnotes.report.context.*;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportCertificateService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportService;
import com.sgs.gpo.domain.service.preorder.order.IOrderDomainService;
import com.sgs.gpo.domain.service.preorder.ordertrfrel.IOrderTrfRelationshipService;
import com.sgs.gpo.facade.model.otsnotes.report.req.ReportCertificateSaveReq;
import com.sgs.gpo.facade.model.preorder.order.bo.OrderTrfBO;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQualificationTypeUpdateReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderTrfReq;
import com.sgs.gpo.facade.model.report.req.*;
import com.sgs.gpo.facade.model.report.rsp.DefaultAccreditationRsp;
import com.sgs.gpo.facade.model.report.rsp.ReportBatchUpdateRsp;
import com.sgs.gpo.facade.model.sci.bo.SciTrfSyncBO;
import com.sgs.gpo.facade.model.sci.req.GpoSciTrfSyncReq;
import com.sgs.gpo.facade.model.sci.rsp.SciSyncTrfRsp;
import com.sgs.grus.async.AsyncCall;
import com.sgs.grus.async.AsyncResult;
import com.sgs.grus.async.AsyncUtils;
import com.sgs.grus.bizlog.BizLogClient;
import com.sgs.grus.bizlog.info.BizLogInfo;
import com.sgs.preorder.core.common.EventType;
import com.sgs.preorder.core.common.StandardObjectType;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.ModelAndView;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@AllArgsConstructor
@Service
@Slf4j
public class ReportBizServiceImpl implements IReportBizService {
    private IReportCertificateService reportCertificateService;
    private IReportService reportService;
    private IOrderDomainService orderDomainService;
    private IReportDomainService reportDomainService;
    private IOrderTrfRelationshipService orderTrfRelationshipService;
    private InterfaceConfig interfaceConfig;
    private BizLogClient bizLogClient;
    private MessageUtil messageUtil;

    @Override
    public BaseResponse generate(ReportGenerateReq req) {
        ReportGenerateContext<ReportGenerateReq> reportGenerateContext = new ReportGenerateContext<>();
        // 生成报告的入参
        reportGenerateContext.setParam(req);
        // 上下文基本信息
        reportGenerateContext.setUserInfo(SystemContextHolder.getUserInfo());
        reportGenerateContext.setLab(SystemContextHolder.getLab());
        reportGenerateContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        reportGenerateContext.setToken(SystemContextHolder.getSgsToken());
        return BaseExecutor.start(ReportGenerateBizCMD.class,reportGenerateContext);
    }

    public BaseResponse batchGenerateAsync(ReportGenerateReq req,UserInfo userInfo,Lab lab,String productLineCode,String token){
        ReportGenerateContext<ReportGenerateReq> reportGenerateContext = new ReportGenerateContext<>();
        // 生成报告的入参
        reportGenerateContext.setParam(req);
        // 上下文基本信息
        reportGenerateContext.setUserInfo(userInfo);
        reportGenerateContext.setLab(lab);
        reportGenerateContext.setProductLineCode(productLineCode);
        reportGenerateContext.setToken(token);
        return BaseExecutor.start(ReportGenerateBizCMD.class,reportGenerateContext);
    }

    @Override
    public BaseResponse batchGenerate(ReportGenerateReq req) {
        // 入参校验
        if(Func.isEmpty(req) || Func.isEmpty(req.getReportIds())){
            return BaseResponse.newFailInstance("common.miss", new Object[]{"reportIds"});
        }
        ReportQueryReq reportQueryReq = new ReportQueryReq();
        reportQueryReq.setReportIdList(req.getReportIds());
        BaseResponse<List<ReportPO>> reportRes = reportService.select(reportQueryReq);
        if(reportRes.isFail()){
            return reportRes;
        }
        if(Func.isEmpty(reportRes.getData())){
            return BaseResponse.newFailInstance("common.param.invalid", new Object[]{"reportIds"});
        }
        List<ReportPO> reportList = reportRes.getData();
        // 执行生成报告
        AsyncCall asyncCall = new AsyncCall();
        UserInfo userInfo = SystemContextHolder.getUserInfo();
        Lab lab = SystemContextHolder.getLab();
        String productLineCode = ProductLineContextHolder.getProductLineCode();
        String token = SystemContextHolder.getSgsToken();
        req.getReportIds().stream().forEach(reportId->{
            ReportPO reportPO = reportList.stream().filter(item->Func.equalsSafe(item.getId(),reportId)).findAny().orElse(null);
            if(Func.isEmpty(reportPO)){
                return;
            }
            ReportGenerateReq reportGenerateReq = new ReportGenerateReq();
            reportGenerateReq.setReportId(reportId);
            reportGenerateReq.setReportActionType(req.getReportActionType());
            asyncCall.put(reportPO.getReportNo(),()-> batchGenerateAsync(reportGenerateReq,userInfo,lab,productLineCode,token));
        });
        List<AsyncResult> asyncResults = AsyncUtils.awaitResult(asyncCall);
        StringBuffer stringBuffer = new StringBuffer();
        for (AsyncResult asyncResult : asyncResults) {
            BaseResponse result = asyncResult.getData();
            if(Func.isEmpty(result)){
                stringBuffer.append(asyncResult.getTaskKey() + " Digital 生成报告失败:" + Func.toStr(asyncResult.getResultMsg()));
            }else if(result.isFail()){
                stringBuffer.append( asyncResult.getTaskKey() + " 生成报告失败,错误原因：" + Func.toStr(result.getMessage()));
            }
        }
        String errorMessage = stringBuffer.toString();
        if(Func.isNotEmpty(errorMessage)){
            return BaseResponse.newFailInstance(errorMessage);
        }
        return BaseResponse.newSuccessInstance("");
    }

    @Override
    public BaseResponse batchGenerateTestResult(ReportGenerateReq req) {
        // 入参校验
        if(Func.isEmpty(req) || Func.isEmpty(req.getGenerateTestResultReqs())){
            return BaseResponse.newFailInstance("common.miss", new Object[]{"generateTestResultReqs"});
        }
        StopWatch totalWatch = new StopWatch();
        totalWatch.start();
        // 自动保存模板数据
        BaseResponse updateRes = reportDomainService.updateReportTemplate(req.getGenerateTestResultReqs());
        if(updateRes.isFail()){
            return updateRes;
        }
        List<Map> maps = Lists.newArrayList();
        AsyncCall asyncCall = new AsyncCall();
        UserInfo userInfo = SystemContextHolder.getUserInfo();
        Lab lab = SystemContextHolder.getLab();
        String productLineCode = ProductLineContextHolder.getProductLineCode();
        String token = SystemContextHolder.getSgsToken();
        for (TestResultGenerateReq generateTestResultReq : req.getGenerateTestResultReqs()) {
            ReportGenerateReq reportGenerateReq = new ReportGenerateReq();
            reportGenerateReq.setReportId(generateTestResultReq.getReportId());
            reportGenerateReq.setReportActionType(generateTestResultReq.getReportActionType());
            asyncCall.put(generateTestResultReq.getReportId(),()-> batchGenerateAsync(reportGenerateReq,userInfo,lab,productLineCode,token));
        }

        List<AsyncResult> asyncResults = AsyncUtils.awaitResult(asyncCall);
        for (AsyncResult asyncResult : asyncResults) {
            String asyncType = asyncResult.getTaskKey();
            Map objMap = new HashMap();
            objMap.put("reportId", asyncType);
            BaseResponse objBaseResponse = asyncResult.getData();
            if (objBaseResponse == null || objBaseResponse.getStatus() != 200) {
                objMap.put("status", 500);
                objMap.put("message", Func.isNotEmpty(objBaseResponse)?objBaseResponse.getMessage():"执行结果为空");
            } else {
                objMap.put("message", "Success");
                objMap.put("status", 200);
            }
            maps.add(objMap);
        }
        totalWatch.stop();
        log.info("batchGenerateTestResult 耗时:{}", totalWatch.getTime());
        return BaseResponse.newSuccessInstance(maps);
    }

    @Override
    public BaseResponse generatePrelim(PrelimReportGenerateReq req) {
        PrelimReportGenerateContext<PrelimReportGenerateReq> reportGenerateContext = new PrelimReportGenerateContext<>();
        reportGenerateContext.setParam(req);
        // 上下文基本信息
        reportGenerateContext.setUserInfo(SystemContextHolder.getUserInfo());
        reportGenerateContext.setLab(SystemContextHolder.getLab());
        reportGenerateContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        reportGenerateContext.setToken(SystemContextHolder.getSgsToken());
        return BaseExecutor.start(PrelimReportGenerateBizCMD.class,reportGenerateContext);
    }

    @Override
    public BaseResponse<Set<String>> queryReportBySampleBarCode(String sampleBarCode) {
        ReportContext<String> context = new ReportContext<>(sampleBarCode);
        return BaseExecutor.start(ReportQueryBySampleBarCodeBizCMD.class,context);
    }

    @Override
    public BaseResponse<List<DefaultAccreditationRsp>> defaultAccreditationQuery(ReportDefaultAccreditationQueryReq req) {
        ReportContext<ReportDefaultAccreditationQueryReq> context = new ReportContext<>(req);
        return BaseExecutor.start(ReportDefaultAccreditationQueryBizCMD.class,context);
    }

    @Override
    public BaseResponse<Boolean> saveReportCertificate(ReportCertificateSaveReq reportCertificateSaveReq) {
        if(Func.isEmpty(reportCertificateSaveReq.getId())){
            ReportQueryReq reportQueryReq = new ReportQueryReq();
            String reportId = reportCertificateSaveReq.getReportId();
            reportQueryReq.setReportIdList(Sets.newHashSet(reportId));
            BaseResponse<List<ReportPO>> reportPORes = reportService.select(reportQueryReq);
            if(reportPORes.isSuccess() && Func.isNotEmpty(reportPORes.getData())){
                String orderNo = reportPORes.getData().get(0).getOrderNo();
                OrderQueryReq orderQueryReq = new OrderQueryReq();
                orderQueryReq.setOrderNoList(Sets.newHashSet(orderNo));
                BaseResponse<List<OrderBO>> orderListRsp = orderDomainService.queryBO(orderQueryReq);
                if(orderListRsp.isSuccess() && Func.isNotEmpty(orderListRsp.getData())){
                    List<OrderBO> orderBOList = orderListRsp.getData();
                    String productCategory = orderBOList.get(0).getHeader().getProductCategory();
                    reportCertificateSaveReq.setOrderProductCategory(productCategory);
                }
            }
        }
        return reportCertificateService.reportCertificateSave(reportCertificateSaveReq);
    }

    @Override
    public BaseResponse buildReportRetrievePageInfo(OrderIdReq orderIdReq, ModelAndView modelAndView) {
        buildPageResourceUrl(modelAndView);
        ReportQueryReq reportQueryReq  = new ReportQueryReq();
        reportQueryReq.setOrderNoList(orderIdReq.getOrderNoList());
        List<ReportPO> reportPOList = reportService.select(reportQueryReq).getData();
        ReportPO reportPO = null;
        if(Func.isNotEmpty(reportPOList)){
            reportPOList = reportPOList.stream().filter(item->!ReportStatus.checkCategory(item.getReportStatus(), Constants.OBJECT.REPORT.STATUS_CATEGORY.INACTIVE)).collect(Collectors.toList());
        }
        if(Func.isNotEmpty(reportPOList)){
            reportPO = reportPOList.get(0);
        }
        if(Func.isNotEmpty(reportPO)){
            String reportId = reportPO.getId();
            String reportNo = reportPO.getReportNo();
            String actualReportNo = reportPO.getActualReportNo();
            modelAndView.addObject("reportId", reportId);
            modelAndView.addObject("reportNo", Func.toStr(actualReportNo,reportNo));
            modelAndView.addObject("reportStatus",reportPO.getReportStatus());
            modelAndView.addObject("reportStatusDisplay",ReportStatus.getMessage(reportPO.getReportStatus()));
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse<Boolean> updateReportAccreditation(OrderQualificationTypeUpdateReq orderQualificationTypeUpdateReq) {
        String productLineCode = ProductLineContextHolder.getProductLineCode();
        if(Func.isEmpty(productLineCode)){
            ProductLineContextHolder.setProductLineCode(ProductLineType.HL.getProductLineAbbr());
        }
        Assert.isTrue(Func.isNotEmpty(orderQualificationTypeUpdateReq) && Func.isNotEmpty(orderQualificationTypeUpdateReq.getOrderIdList()),"common.param.miss",new Object[]{"orderId"});
        UserInfo userInfoFillSystem = SystemContextHolder.getUserInfoFillSystem();
        String modifiedBy = orderQualificationTypeUpdateReq.getModifiedBy();
        modifiedBy = Func.toStr(modifiedBy,userInfoFillSystem.getRegionAccount());
        String finalModifiedBy = modifiedBy;
        Set<String> orderIdList = orderQualificationTypeUpdateReq.getOrderIdList();
        OrderQueryReq orderQueryReq = new OrderQueryReq();
        orderQueryReq.setBaseQuery(true);
        orderQueryReq.setOrderIdList(orderIdList);
        List<OrderBO> orderBOList = orderDomainService.queryBO(orderQueryReq).getData();
        Assert.isTrue(Func.isNotEmpty(orderBOList),"common.records.empty",new Object[]{Constants.OBJECT.ORDER.OBJECT_CODE});
        Set<String> orderNoList = orderBOList.stream().map(item->item.getId().getOrderNo()).collect(Collectors.toSet());
        ReportQueryReq reportQueryReq = new ReportQueryReq();
        reportQueryReq.setOrderNoList(orderNoList);
        List<ReportPO> reportPOList = reportService.select(reportQueryReq).getData();
        if(Func.isEmpty(reportPOList)){
            return BaseResponse.newFailInstance("common.records.empty",new Object[]{"ReportInfo"});
        }
        //查询默认的Accreditation
        ReportDefaultAccreditationQueryReq reportDefaultAccreditationQueryReq = new ReportDefaultAccreditationQueryReq();
        reportDefaultAccreditationQueryReq.setOrderNoList(orderNoList);
        BaseResponse<List<DefaultAccreditationRsp>> defaultAccreditationQueryRes = defaultAccreditationQuery(reportDefaultAccreditationQueryReq);
        if(defaultAccreditationQueryRes.isFail()){
            return BaseResponse.newFailInstance(defaultAccreditationQueryRes.getMessage());
        }
        List<DefaultAccreditationRsp> defaultAccreditationRspList = defaultAccreditationQueryRes.getData();
        if(Func.isEmpty(defaultAccreditationRspList)){
            return BaseResponse.newFailInstance("common.records.empty",new Object[]{"DefaultAccreditation"});
        }
        //对ReportPOList根据OrderNo分组
        Map<String, List<ReportPO>> orderReportMap = reportPOList.stream().collect(Collectors.groupingBy(ReportPO::getOrderNo));
        Map<String, ReportPO> reportIdMap = reportPOList.stream().collect(Collectors.toMap(ReportPO::getId, Function.identity(), (existing, replacement) -> existing));
        Map<String, DefaultAccreditationRsp> orderAccreditationRspMap = defaultAccreditationRspList.stream().collect(Collectors.toMap(DefaultAccreditationRsp::getOrderNo, Function.identity(), (existing, replacement) -> existing));
        Date now = new Date();
        orderReportMap.forEach((orderNo, reportPOS) -> {
            //排除无效报告后是否存在状态非New的报告
            boolean existNotNewReport = reportPOS.stream().anyMatch(item -> !ReportStatus.checkCategory(item.getReportStatus(), Constants.OBJECT.REPORT.STATUS_CATEGORY.INACTIVE)
                    && !ReportStatus.check(item.getReportStatus(), ReportStatus.New));
            List<ReportPO> newReportPOList = reportPOS.stream().filter(item->ReportStatus.check(item.getReportStatus(), ReportStatus.New)).collect(Collectors.toList());
            if(existNotNewReport){
                //存在非New的报告,无法更新
                log.warn("订单{}中存在非New的报告,无法更新",orderNo);
            }else if(Func.isNotEmpty(newReportPOList)){
                DefaultAccreditationRsp defaultAccreditationRsp = orderAccreditationRspMap.getOrDefault(orderNo,null);
                if(Func.isNotEmpty(defaultAccreditationRsp)){
                    String certificateName = defaultAccreditationRsp.getCertificateName();
                    Set<String> reportIdList = newReportPOList.stream()
                            .filter(item -> !Func.equalsSafe(item.getCertificateId(), defaultAccreditationRsp.getCertificateId()))
                            .map(ReportPO::getId).collect(Collectors.toSet());
                    if(Func.isNotEmpty(reportIdList)){
                        ReportAccreditationUpdateReq reportAccreditationUpdateReq = new ReportAccreditationUpdateReq();
                        reportAccreditationUpdateReq.setReportIdList(reportIdList);
                        reportAccreditationUpdateReq.setCertificateName(certificateName);
                        reportAccreditationUpdateReq.setCertificateFileCloudKey(defaultAccreditationRsp.getCertificateFileCloudKey());
                        reportAccreditationUpdateReq.setCertificateId(defaultAccreditationRsp.getCertificateId());
                        reportAccreditationUpdateReq.setModifiedDate(now);
                        reportAccreditationUpdateReq.setModifiedBy(finalModifiedBy);
                        BaseResponse<Boolean> updateReportAccreditationRes = reportDomainService.updateReportAccreditation(reportAccreditationUpdateReq);
                        OrderBO orderBO = orderBOList.stream().filter(item -> Func.isNotEmpty(item.getId()) && Func.equalsSafe(item.getId().getOrderNo(), orderNo)).findAny().orElse(null);
                        LabBO lab = orderBO != null ? orderBO.getLab() : null;
                        if(lab != null && updateReportAccreditationRes.getData()){
                            for (String reportId : reportIdList) {
                                ReportPO reportPO = reportIdMap.getOrDefault(reportId,null);
                                if(reportPO==null){
                                    continue;
                                }
                                String actualReportNo = reportPO.getActualReportNo();
                                BizLogInfo bizLog = new BizLogInfo();
                                bizLog.setBu(lab.getBuCode());
                                bizLog.setLab(lab.getLocationCode());
                                bizLog.setOpUser(finalModifiedBy);
                                bizLog.setBizId(orderNo);
                                bizLog.setOpType("Order QualificationType Change");
                                bizLog.setBizOpType(BizLogConstant.REPORT_OPERATION_HISTORY);
                                bizLog.setNewVal(String.format("%s:[%s]", actualReportNo,certificateName));
                                bizLog.setOriginalVal(String.format("%s:[%s]", actualReportNo, reportPO.getCertificateName()));
                                bizLogClient.doSend(bizLog);
                            }
                    }
                }

                }
            }
        });
        return BaseResponse.newSuccessInstance(true);
    }

    private void buildPageResourceUrl(ModelAndView modelAndView){
        String vueUrl = interfaceConfig.getPublicFileUrl() + Constants.THYMELEAF_PATH.VUE;
        String axiosUrl = interfaceConfig.getPublicFileUrl() + Constants.THYMELEAF_PATH.AXIOS;
        String elementUrl = interfaceConfig.getPublicFileUrl() + Constants.THYMELEAF_PATH.ELEMENT;
        String jqueryUrl = interfaceConfig.getPublicFileUrl() + Constants.THYMELEAF_PATH.JQUERY;
        String indexUrl = interfaceConfig.getPublicFileUrl() + Constants.THYMELEAF_PATH.THEME;
        modelAndView.addObject("vueUrl", vueUrl);
        modelAndView.addObject("axiosUrl", axiosUrl);
        modelAndView.addObject("elementUrl", elementUrl);
        modelAndView.addObject("jqueryUrl", jqueryUrl);
        modelAndView.addObject("indexUrl", indexUrl);
    }

    /*
     *根据ReviseType校验是否满足Revise Apply条件
     * 1、【ReviseType=Rework】如果绑定了SCI,校验是否报告是否已经Confirmed,未Confirmed则不能Revise Apply
     * @param reportReviseCheckReq ReportNo：内部报告号
     * */
    @Override
    public BaseResponse<Boolean> checkReportRevise(ReportReviseCheckReq reportReviseCheckReq) {
        Assert.isTrue(Func.isNotEmpty(reportReviseCheckReq),"common.param.miss",new Object[]{"reportNo"});
        Assert.isTrue(Func.isNotEmpty(reportReviseCheckReq.getReportNo()),"common.param.miss",new Object[]{"ReportNo"});
        Integer reviseType = reportReviseCheckReq.getReviseType();
        Assert.isTrue(Func.isNotEmpty(reportReviseCheckReq.getReviseType()),"common.param.miss",new Object[]{"ReviseType"});
        Assert.isTrue(Func.isNotEmpty(reviseType),"common.param.invalid",new Object[]{"ReviseType"});
        if(ReportReviseType.check(reviseType,ReportReviseType.Rework)){
            BaseResponse<SciSyncTrfRsp> sciReportConfirmRes = querySCICanReviseReport(reportReviseCheckReq);
            if(sciReportConfirmRes.isFail() && sciReportConfirmRes.getStatus()==198){
                return BaseResponse.newFailInstance(sciReportConfirmRes.getMessage());
            }
        }
        return BaseResponse.newSuccessInstance(true);
    }
    /*
     * @Description 查询SCI中的ReportConfirmFlag,方法入参未ReportNo内部号，调用SCI接口前要转为外部号
     * @param sciReportConfirmReq
     * @return
     * */
    @Override
    public BaseResponse<SciSyncTrfRsp> querySCICanReviseReport(ReportReviseCheckReq reportReviseCheckReq) {
        Assert.isTrue(Func.isNotEmpty(reportReviseCheckReq), "common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
        String reportNo = reportReviseCheckReq.getReportNo();
        Assert.isTrue(Func.isNotEmpty(reportNo), "common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});

        ReportIdBO reportIdBO = new ReportIdBO();
        reportIdBO.setReportNo(reportNo);

        BaseResponse<ReportBO> detailRes = reportDomainService.getDetail(reportIdBO);
        if (Func.isEmpty(detailRes) || Func.isEmpty(detailRes.getData())) {
            return BaseResponse.newFailInstance("common.records.empty", new Object[]{reportNo});
        }

        ReportBO reportBO = detailRes.getData();

        // 获取订单信息
        ReportRelationBO relationship = reportBO.getRelationship();
        if (relationship == null) {
            return BaseResponse.newFailInstance("common.param.invalid", new Object[]{"ReportBO.Relationship"});
        }

        ReportParentBO parent = relationship.getParent();
        if (parent == null) {
            return BaseResponse.newFailInstance("common.param.invalid", new Object[]{"ReportBO.Parent"});
        }

        OrderIdRelBO order = parent.getOrder();
        if (order == null) {
            return BaseResponse.newFailInstance("common.param.invalid", new Object[]{"ReportBO.Order"});
        }

        String orderNo = order.getOrderNo();
        String orderId = order.getOrderId();
        String reportId = reportBO.getId().getReportId();
        String actualReportNo = reportBO.getId().getActualReportNo();

        Assert.isTrue(Func.isNotEmpty(orderNo), "common.param.invalid", new Object[]{"ReportBO.OrderNo"});

        OrderTrfReq orderTrfReq = new OrderTrfReq();
        orderTrfReq.setOrderNoList(Collections.singleton(orderNo));

        BaseResponse<List<OrderTrfBO>> trfRes = orderTrfRelationshipService.queryOrderTrf(orderTrfReq);
        if (trfRes.isFail()) {
            return BaseResponse.newFailInstance(trfRes.getMessage());
        }

        List<OrderTrfBO> orderTrfBOs = trfRes.getData();
        if (Func.isEmpty(orderTrfBOs)) {
            return BaseResponse.newSuccessInstance(true);
        }

        OrderTrfBO orderTrfBO = orderTrfBOs.stream()
                .filter(e -> RefIntegrationChannel.check(e.getIntegrationChannel(), RefIntegrationChannel.SCI))
                .findAny()
                .orElse(null);

        if (Func.isEmpty(orderTrfBO)) {
            return BaseResponse.newSuccessInstance(true);
        }

        String trfNo = orderTrfBO.getRefNo();
        Integer refSystemId = orderTrfBO.getRefSystemId();
        String buCode = SystemContextHolder.getBuCode();
        String sgsToken = SystemContextHolder.getSgsToken();

        SciTrfContext<GpoSciTrfSyncReq, SciTrfSyncBO> sciTrfContext = new SciTrfContext<>();
        GpoSciTrfSyncReq gpoSciTrfSyncReq = new GpoSciTrfSyncReq();

        gpoSciTrfSyncReq.setRefSystemId(refSystemId);
        gpoSciTrfSyncReq.setTrfNo(trfNo);
        gpoSciTrfSyncReq.setOrderNo(orderNo);
        gpoSciTrfSyncReq.setOrderId(orderId);
        gpoSciTrfSyncReq.setObjectType(StandardObjectType.Report.getName());
        gpoSciTrfSyncReq.setSourceNo(reportNo);
        gpoSciTrfSyncReq.setSourceId(reportId);
        gpoSciTrfSyncReq.setSciUrl(Constants.SCI.URL.CAN_REVISE_REPORT);
        gpoSciTrfSyncReq.setEventType(EventType.ReviseReport.getTypeName());
        gpoSciTrfSyncReq.setSciAction(EventType.ReviseReport.getSciAction());
        gpoSciTrfSyncReq.setToken(sgsToken);
        gpoSciTrfSyncReq.setProductLineCode(buCode);

        sciTrfContext.setParam(gpoSciTrfSyncReq);
        sciTrfContext.setUserInfo(SecurityContextHolder.getUserInfo());
        sciTrfContext.setToken(SecurityContextHolder.getSgsToken());
        sciTrfContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());

        try {
            BaseResponse canReviseReportRes = BaseExecutor.start(SciSyncTrfCMD.class, sciTrfContext);
            if(canReviseReportRes.isFail() && canReviseReportRes.getStatus()==198){
                return new BaseResponse(ResponseCode.FAIL.getCode(),messageUtil.get("report.revise.check.sci.report.not.completed", new Object[]{actualReportNo}));
            }

            return BaseResponse.newSuccessInstance(true);
        } catch (Exception e) {
            log.error("Error occurred while querying SCI report confirm flag for reportNo: {}, orderNo: {}", reportNo, orderNo, e);
            return BaseResponse.newFailInstance("common.system.error");
        }
    }

    @Override
    public BaseResponse queryExpireData(String productLineCode) {
        PrelimReportQueryExpireDataContext context = new PrelimReportQueryExpireDataContext();
        context.setParam(productLineCode);
        context.setProductLineCode(productLineCode);
        return BaseExecutor.start(PrelimReportQueryExpireDataBizCMD.class, context);
    }

    @Override
    public BaseResponse pending(ReportUpdateReq reportUpdateReq){
        ReportUpdateContext<ReportUpdateReq> context = new ReportUpdateContext<>();
        context.setParam(reportUpdateReq);
        context.setUserInfo(SystemContextHolder.getUserInfoFillSystem());
        context.setToken(SystemContextHolder.getSgsToken());
        context.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(ReportPendingBizCMD.class, context);
    }

    @Override
    public BaseResponse unPending(ReportUpdateReq reportUpdateReq) {
        ReportUpdateContext<ReportUpdateReq> context = new ReportUpdateContext<>();
        context.setParam(reportUpdateReq);
        context.setUserInfo(SystemContextHolder.getUserInfoFillSystem());
        context.setToken(SystemContextHolder.getSgsToken());
        context.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(ReportUnPendingBizCMD.class, context);
    }

    @Override
    public BaseResponse batchPending(ReportBatchUpdateReq reportBatchUpdateReq){
        // 入参校验
        if(Func.isEmpty(reportBatchUpdateReq) || Func.isEmpty(reportBatchUpdateReq.getReportNoList())){
            return BaseResponse.newFailInstance("common.miss", new Object[]{"reportNos"});
        }
        AsyncCall asyncCall = new AsyncCall();
        UserInfo userInfo = SystemContextHolder.getUserInfoFillSystem();
        Lab lab = SystemContextHolder.getLab();
        String productLineCode = ProductLineContextHolder.getProductLineCode();
        String token = SystemContextHolder.getSgsToken();
        reportBatchUpdateReq.getReportNoList().stream().forEach(reportNo->{

            ReportUpdateReq reportUpdateReq =  new ReportUpdateReq();
            reportUpdateReq.setReportNo(reportNo);
            reportUpdateReq.setToken(token);
            reportUpdateReq.setProductLineCode(productLineCode);
            reportUpdateReq.setUserInfo(userInfo);
            reportUpdateReq.setReasonType(reportBatchUpdateReq.getReasonType());
            reportUpdateReq.setRemark(reportBatchUpdateReq.getRemark());
            asyncCall.put(reportNo,()-> pending(reportUpdateReq));
        });
        List<AsyncResult> asyncResults = AsyncUtils.awaitResult(asyncCall);
        List<ReportBatchUpdateRsp> rspList = new ArrayList<>();
        for (AsyncResult asyncResult : asyncResults) {
            BaseResponse result = asyncResult.getData();
            ReportBatchUpdateRsp rsp = new ReportBatchUpdateRsp();
            rsp.setReportNo(asyncResult.getTaskKey());
            if(Func.isEmpty(result)){
                rsp.setMessage("Pending 失败:" + Func.toStr(asyncResult.getResultMsg()));
            }else if(result.isFail()){
                rsp.setMessage("Pending 失败:" + Func.toStr(result.getMessage()));
            } else{
                rsp.setMessage("Pending 成功");
            }
            rspList.add(rsp);
        }
        return BaseResponse.newSuccessInstance(rspList);
    }

    @Override
    public BaseResponse batchUnPending(ReportBatchUpdateReq reportBatchUpdateReq){
        // 入参校验
        if(Func.isEmpty(reportBatchUpdateReq) || Func.isEmpty(reportBatchUpdateReq.getReportNoList())){
            return BaseResponse.newFailInstance("common.miss", new Object[]{"reportNos"});
        }
        AsyncCall asyncCall = new AsyncCall();
        UserInfo userInfo = SystemContextHolder.getUserInfoFillSystem();
        Lab lab = SystemContextHolder.getLab();
        String productLineCode = ProductLineContextHolder.getProductLineCode();
        String token = SystemContextHolder.getSgsToken();
        reportBatchUpdateReq.getReportNoList().stream().forEach(reportNo->{

            ReportUpdateReq reportUpdateReq =  new ReportUpdateReq();
            reportUpdateReq.setReportNo(reportNo);
            reportUpdateReq.setToken(token);
            reportUpdateReq.setProductLineCode(productLineCode);
            reportUpdateReq.setUserInfo(userInfo);
            reportUpdateReq.setReasonType(reportBatchUpdateReq.getReasonType());
            reportUpdateReq.setRemark(reportBatchUpdateReq.getRemark());
            asyncCall.put(reportNo,()-> unPending(reportUpdateReq));
        });
        List<AsyncResult> asyncResults = AsyncUtils.awaitResult(asyncCall);
        List<ReportBatchUpdateRsp> rspList = new ArrayList<>();
        for (AsyncResult asyncResult : asyncResults) {
            BaseResponse result = asyncResult.getData();
            ReportBatchUpdateRsp rsp = new ReportBatchUpdateRsp();
            rsp.setReportNo(asyncResult.getTaskKey());
            if(Func.isEmpty(result)){
                rsp.setMessage("UnPending 失败:" + Func.toStr(asyncResult.getResultMsg()));
            }else if(result.isFail()){
                rsp.setMessage("UnPending 失败:" + Func.toStr(result.getMessage()));
            } else{
                rsp.setMessage("UnPending 成功");
            }
            rspList.add(rsp);
        }
        return BaseResponse.newSuccessInstance(rspList);
    }

}
