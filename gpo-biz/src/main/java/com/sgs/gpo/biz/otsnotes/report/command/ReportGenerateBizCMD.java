package com.sgs.gpo.biz.otsnotes.report.command;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.facade.domain.rsp.BuParamValueRsp;
import com.sgs.framework.log.SystemLogHelper;
import com.sgs.framework.log.enums.SystemLogType;
import com.sgs.framework.log.model.SystemLog;
import com.sgs.framework.model.common.attachment.AttachmentBO;
import com.sgs.framework.model.common.contact.ContactPersonBO;
import com.sgs.framework.model.common.customer.CustomerBO;
import com.sgs.framework.model.common.print.DigitalReportDataSourceBO;
import com.sgs.framework.model.common.print.OutPutDataBO;
import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.model.enums.CustomerType;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.framework.model.enums.ReportLanguage;
import com.sgs.framework.model.enums.SubContractType;
import com.sgs.framework.model.enums.TestLineType;
import com.sgs.framework.model.order.trf.TrfBO;
import com.sgs.framework.model.order.v2.OrderBO;
import com.sgs.framework.model.report.report.v2.ReportBO;
import com.sgs.framework.model.report.report.v2.ReportMatrixRelBO;
import com.sgs.framework.model.test.conclusion.v2.ConclusionExtBO;
import com.sgs.framework.model.test.pp.PPLanguageBO;
import com.sgs.framework.model.test.pp.pptestline.v2.PPTestLineRelBO;
import com.sgs.framework.model.test.testline.TestLineLanguageBO;
import com.sgs.framework.model.test.testline.v2.TestLineBO;
import com.sgs.framework.tool.jackson.JsonUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.biz.output.context.AssembleDataContext;
import com.sgs.gpo.biz.output.service.OutputFactory;
import com.sgs.gpo.biz.setting.object.IObjectVerifyBizService;
import com.sgs.gpo.core.config.InterfaceConfig;
import com.sgs.gpo.core.constants.BizLogConstant;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.enums.*;
import com.sgs.gpo.core.util.DateUtils;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.execution.ExecutionResubmissionRecordPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.job.JobPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportFilePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportTemplatePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.status.GpnStatusPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subcontract.SubcontractPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testmatrix.TestMatrixPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testsample.TestSamplePO;
import com.sgs.gpo.domain.service.common.tracking.ITrackingService;
import com.sgs.gpo.domain.service.otsnotes.execution.IExecutionResubmissionService;
import com.sgs.gpo.domain.service.otsnotes.job.subdomain.IJobService;
import com.sgs.gpo.domain.service.otsnotes.report.IReportDomainService;
import com.sgs.gpo.domain.service.otsnotes.report.context.ReportGenerateContext;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportFileService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportTemplateService;
import com.sgs.gpo.domain.service.otsnotes.status.IGpnStatusService;
import com.sgs.gpo.domain.service.otsnotes.subcontract.subdomain.ISubcontractService;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.ITestMatrixDomainService;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain.ITestMatrixService;
import com.sgs.gpo.domain.service.otsnotes.testsample.subdomain.ITestSampleService;
import com.sgs.gpo.domain.service.preorder.order.IOrderDomainService;
import com.sgs.gpo.domain.service.setting.buparam.IBUParam;
import com.sgs.gpo.facade.model.enums.ProductLines;
import com.sgs.gpo.facade.model.job.req.JobQueryReq;
import com.sgs.gpo.facade.model.otsnotes.execution.req.ResubmissionQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testsample.req.TestSampleQueryReq;
import com.sgs.gpo.facade.model.preorder.order.req.GpnStatusReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportFileQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportGenerateReq;
import com.sgs.gpo.facade.model.report.req.ReportQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportTemplateReq;
import com.sgs.gpo.facade.model.report.req.ReportUpdateReq;
import com.sgs.gpo.facade.model.subcontract.req.SubcontractQueryReq;
import com.sgs.gpo.integration.dataentry.DataEntryClient;
import com.sgs.gpo.integration.dataentry.req.ConclusionReq;
import com.sgs.gpo.integration.dff.DFFClient;
import com.sgs.gpo.integration.digital.DigitalReportClient;
import com.sgs.gpo.integration.digital.req.CallbackSetting;
import com.sgs.gpo.integration.digital.req.DigitalReportInfoBO;
import com.sgs.gpo.integration.digital.req.v2.DigitalReportReq;
import com.sgs.gpo.integration.framework.FrameworkClient;
import com.sgs.gpo.integration.framework.rsp.QueryBuCodeRsp;
import com.sgs.gpo.integration.sodanotes.SodaNotesClient;
import com.sgs.gpo.integration.sodanotes.req.SodaGenerateReportReq;
import com.sgs.gpo.integration.sodapreorder.SodaPreorderClient;
import com.sgs.gpo.integration.sodapreorder.req.SodaSyncSubContractReq;
import com.sgs.gpo.integration.trims.TrimsClient;
import com.sgs.grus.bizlog.BizLogClient;
import com.sgs.grus.bizlog.common.BizLogHelper;
import com.sgs.grus.bizlog.info.BizLogInfo;
import com.sgs.otsnotes.facade.model.enums.LanguageTypeDigitalReport;
import com.sgs.otsnotes.facade.model.enums.ReportOperationTypeEnum;
import com.sgs.preorder.facade.model.enums.OrderPersonType;
import com.sgs.preorder.facade.model.req.UnpivotProductReq;
import com.sgs.trimslocal.facade.model.artifact.req.PpArtifactRelListReq;
import com.sgs.trimslocal.facade.model.artifact.rsp.PpArtifactRelLangInfoRsp;
import com.sgs.trimslocal.facade.model.artifact.rsp.PpArtifactRsp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ReportGenerateBizCMD extends BaseCommand<ReportGenerateContext<ReportGenerateReq>> {

    @Autowired
    private IReportDomainService reportDomainService;
    @Autowired
    private IOrderDomainService orderDomainService;
    @Autowired
    private IReportFileService reportFileService;
    @Autowired
    private IReportTemplateService reportTemplateService;
    @Autowired
    private OutputFactory outputFactory;
    @Autowired
    private InterfaceConfig interfaceConfig;
    @Autowired
    private DigitalReportClient digitalReportClient;
    @Autowired
    private SystemLogHelper systemLogHelper;
    @Autowired
    private BizLogClient bizLogClient;
    @Autowired
    private ITrackingService trackingService;
    @Autowired
    private ITestMatrixService testMatrixService;
    @Autowired
    private DFFClient dffClient;
    @Autowired
    private DataEntryClient dataEntryClient;
    @Autowired
    private TrimsClient trimsClient;
    @Autowired
    private ISubcontractService subcontractService;
    @Autowired
    private IGpnStatusService gpnStatusService;
    @Autowired
    private FrameworkClient frameworkClient;
    @Autowired
    private SodaPreorderClient sodaPreorderClient;
    @Autowired
    private SodaNotesClient sodaNotesClient;
    @Autowired
    private ITestMatrixDomainService testMatrixDomainService;
    @Autowired
    private IBUParam ibuParam;
    @Autowired
    private ITestSampleService testSampleService;
    @Autowired
    private IObjectVerifyBizService objectVerifyBizService;
    @Autowired
    private IJobService jobService;
    @Autowired
    private IExecutionResubmissionService executionResubmissionService;
    /**
     * 生成报告的业务校验
     *
     * @param context
     * @return
     */
    @Override
    public BaseResponse validateParam(ReportGenerateContext<ReportGenerateReq> context) {
        log.info("generate report start... req:{}", context);
        // 校验用户信息不能为空
        if(Func.isNotEmpty(context.getProductLineCode())){
            ProductLineContextHolder.setProductLineCode(context.getProductLineCode());
        }
        UserInfo userInfo = context.getUserInfo();
        if (Func.isEmpty(userInfo)) {
            return BaseResponse.newFailInstance("common.miss", new Object[]{"user"});
        }
        // 校验入参不能为空
        if (Func.isEmpty(context.getParam())) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        ReportGenerateReq reportGenerateReq = context.getParam();
        if (Func.isEmpty(reportGenerateReq.getReportActionType())) {
            return BaseResponse.newFailInstance("reportActionType 不能为空");
        }
        // 校验ReportId不能为空
        String reportId = reportGenerateReq.getReportId();
        if (Func.isEmpty(reportId)) {
            return BaseResponse.newFailInstance("common.miss", new Object[]{"reportId"});
        }
        //查询报告详细信息
        ReportQueryReq reportQueryReq = new ReportQueryReq();
        reportQueryReq.setReportId(reportId);
        BaseResponse<List<ReportBO>> reportListRes = reportDomainService.queryBO(reportQueryReq);
        if (reportListRes.isFail() || Func.isEmpty(reportListRes.getData())) {
            return BaseResponse.newFailInstance(String.format("当前用户Lab已切换为(%s),请切换回正确的Lab后再操作.", Func.isNotEmpty(SystemContextHolder.getLab()) ? SystemContextHolder.getLab().getLabCode() : ""));
        }
        ReportBO reportBO = reportListRes.getData().get(0);
        if (ReportActionType.check(reportGenerateReq.getReportActionType(), ReportActionType.GENERATE_REPORT)) {
            // 校验报告状态
            // 报告状态不为NEW/DRAFT
            if (!ReportStatus.check(reportBO.getHeader().getReportStatus(), ReportStatus.New, ReportStatus.Draft)) {
                return BaseResponse.newFailInstance("report.generate.status.not.match", new Object[]{reportBO.getId().getReportNo()});
            }
            //校验TestResult审核状态
            //校验report下的testResult是否都已经审核
            ReportFileQueryReq reportFileQueryReq = new ReportFileQueryReq();
            List<Integer> validReportFileStatus = Arrays.asList(ReportFileStatus.NEW.getCode(), ReportFileStatus.ENTERED.getCode());
            reportFileQueryReq.setReportFileType(ReportFileType.TestResult.getCode());
            reportFileQueryReq.setReportFileStatus(validReportFileStatus);
            reportFileQueryReq.setReportIdList(Sets.newHashSet(reportGenerateReq.getReportId()));
            BaseResponse<List<ReportFilePO>> testResultListRes = reportFileService.query(reportFileQueryReq);
            if (Func.isNotEmpty(testResultListRes.getData())) {
                return BaseResponse.newFailInstance("report.test.result.not.completed", new Object[]{reportBO.getId().getReportNo()});
            }
        }
        // 报告维护的模板信息
        ReportTemplateReq reportTemplateReq = new ReportTemplateReq();
        reportTemplateReq.setReportId(reportGenerateReq.getReportId());
        BaseResponse<List<ReportTemplatePO>> reportTemplateListRes = reportTemplateService.queryReportTemplateList(reportTemplateReq);
        if (Func.isEmpty(reportTemplateListRes.getData())) {
            return BaseResponse.newFailInstance("Please select report template and save before generate report!");
        }
        List<ReportTemplatePO> reportTemplateList = reportTemplateListRes.getData();
        // 查询订单信息
        String orderNo = reportBO.getRelationship().getParent().getOrder().getOrderNo();
        OrderQueryReq orderQueryReq = new OrderQueryReq();
        orderQueryReq.setOrderNoList(Sets.newHashSet(orderNo));
        BaseResponse<List<OrderBO>> orderListRes = orderDomainService.queryBO(orderQueryReq);
        if (orderListRes.isFail() || Func.isEmpty(orderListRes.getData())) {
            return orderListRes;
        }
        OrderBO orderBO = orderListRes.getData().get(0);
        // 校验订单状态不为Pending
        if(OrderStatus.checkStatus(orderBO.getHeader().getOrderStatus(),OrderStatus.Pending)){
            return BaseResponse.newFailInstance(orderBO.getId().getOrderNo()+" is pending order , please check again");
        }
        Integer reportLanguage = orderBO.getServiceRequirement().getReport().getReportLanguage();
        // 根据TestRequest获取中英文模板,如果没匹配到模板需要返回错误
        ReportTemplatePO reportTemplateCn = null;
        ReportTemplatePO reportTemplateEn = null;
        if (ReportLanguage.checkLanguage(reportLanguage.toString(), ReportLanguage.EnglishAndChineseReport)) {
            reportTemplateCn = reportTemplateList.stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.Chinese)
            ).findAny().orElse(null);
            reportTemplateEn = reportTemplateList.stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.English)
            ).findAny().orElse(null);
        }
        if (ReportLanguage.checkLanguage(reportLanguage.toString(), ReportLanguage.EnglishReportOnly, ReportLanguage.MultilingualReport)) {
            reportTemplateEn = reportTemplateList.stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.English)
            ).findAny().orElse(null);
        }
        if (ReportLanguage.checkLanguage(reportLanguage.toString(), ReportLanguage.ChineseReportOnly)) {
            reportTemplateCn = reportTemplateList.stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.Chinese)
            ).findAny().orElse(null);
        }
        if (Func.isEmpty(reportTemplateCn) && Func.isEmpty(reportTemplateEn)) {
            return BaseResponse.newFailInstance("Please select report template and save before generate report!");
        }
        // 查询报告关联的testLine
        if (Func.isNotEmpty(reportBO.getRelationship().getChildren())
                && Func.isNotEmpty(reportBO.getRelationship().getChildren().getReportMatrixList())) {
            Set<String> testMatrixList = reportBO.getRelationship().getChildren().getReportMatrixList().stream().map(ReportMatrixRelBO::getTestMatrixId)
                    .collect(Collectors.toSet());
            TestMatrixQueryReq testMatrixQueryReq = new TestMatrixQueryReq();
            testMatrixQueryReq.setTestMatrixIdList(testMatrixList);
            BaseResponse<List<TestMatrixPO>> testMatrixResponse = testMatrixService.queryList(testMatrixQueryReq);
            if (Func.isNotEmpty(testMatrixResponse.getData())) {
                context.setTestLineInsIdList(testMatrixResponse.getData().stream().map(TestMatrixPO::getTestLineInstanceID).collect(Collectors.toSet()));
                context.setTestMatrixIdList(testMatrixResponse.getData().stream().map(TestMatrixPO::getId).collect(Collectors.toSet()));
                Set<String> testSampleInstanceIdList = testMatrixResponse.getData().stream().map(TestMatrixPO::getTestSampleID).collect(Collectors.toSet());
                TestSampleQueryReq testSampleQueryReq = new TestSampleQueryReq();
                testSampleQueryReq.setTestSampleInstanceIdList(testSampleInstanceIdList);
                BaseResponse<List<TestSamplePO>> testSampleAllRes = testSampleService.selectALL(testSampleQueryReq);
                context.setTestSampleIdList(testSampleAllRes.getData().stream().map(TestSamplePO::getId).collect(Collectors.toSet()));
            }
        }
        // 查询WorkFlow配置
        BaseResponse<Integer> workFlowRes = orderDomainService.queryWorkFlowConfig(orderNo);
        if (workFlowRes.isSuccess()) {
            context.getParam().setWorkFlow(workFlowRes.getData());
        }
        context.getParam().setReportNo(reportBO.getId().getReportNo());
        context.getParam().setActualReportNo(reportBO.getId().getActualReportNo());
        // 设置上下文参数
        context.setReportTemplateCn(reportTemplateCn);
        context.setReportTemplateEn(reportTemplateEn);
        context.setOrder(orderBO);
        context.setDomain(reportBO);
        // 执行generateReport Service 需要的入参
        reportGenerateReq.setOrderNo(orderBO.getId().getOrderNo());
        if (Func.isNotEmpty(orderBO.getServiceRequirement().getReport().getReportLanguage())) {
            reportGenerateReq.setReportLanguage(orderBO.getServiceRequirement().getReport().getReportLanguage().toString());
        }
        return BaseResponse.newSuccessInstance(true);
    }

    /**
     * 生成报告时的数据准备
     *
     * @param context
     * @return
     */
    @Override
    public BaseResponse before(ReportGenerateContext<ReportGenerateReq> context) {
        if(Func.equalsSafe(context.getLab().getLabCode(),Constants.TW_SL)){
            context.getParam().setHasFileEn(Func.isNotEmpty(context.getReportTemplateEn()));
            context.getParam().setHasFileCn(Func.isNotEmpty(context.getReportTemplateCn()));
            return BaseResponse.newSuccessInstance(true);
        }
        // 以下这几个逻辑是否还需要
        // 1 editorBy，editor，reviewerBy，reviewer字段更新
        ContactPersonBO tsPerson = null;
        if (Func.isNotEmpty(context.getOrder().getContactPersonList())) {
            tsPerson = context.getOrder().getContactPersonList().stream()
                    .filter(contactPersonBO -> Func.equalsSafe(OrderPersonType.TS.getCode(), contactPersonBO.getContactUsage())).findAny().orElse(null);
        }
        if (ReportActionType.check(context.getParam().getReportActionType(), ReportActionType.GENERATE_REPORT) || ReportActionType.check(context.getParam().getReportActionType(), ReportActionType.GENERATE_REPORT_BY_TL_REPORT)) {
            ReportUpdateReq reportUpdateReq = new ReportUpdateReq();
            reportUpdateReq.setReportId(context.getParam().getReportId());
            reportUpdateReq.setLabCode(context.getLab().getLabCode());
            reportUpdateReq.setUserName(context.getUserInfo().getRegionAccount());
            if (Func.isNotEmpty(tsPerson)) {
                reportUpdateReq.setTsPerson(tsPerson.getRegionAccount());
            }
            reportUpdateReq.setLocationCode(context.getLab().getLocationCode());
            reportUpdateReq.setProductLineCode(context.getProductLineCode());
            BaseResponse signerRes = reportDomainService.updateReportSigner(reportUpdateReq);
            if (signerRes.isFail()) {
                return BaseResponse.newFailInstance(signerRes.getMessage());
            }
            // 2 同步主订单信息到接包方
            this.syncHostOrder(context);
            // 3 纯分包业务场景下点击generate report的时候需要将01报告迁移 还需要这个逻辑嘛?
            // 4 调用DFF 做行转列
            UnpivotProductReq unpivotProductReq = new UnpivotProductReq();
            unpivotProductReq.setOrderNo(context.getOrder().getId().getOrderNo());
            unpivotProductReq.setReportNo(context.getDomain().getId().getReportNo());
            unpivotProductReq.setProductLineCode(context.getLab().getBuCode());
            BaseResponse dffConvertRes = dffClient.unpivotAndSaveProduct(unpivotProductReq);
            if (dffConvertRes.isFail()) {
                return dffConvertRes;
            }
            // 5 DAL创建的订单需要匹配对应的DAL报告号
            if(Func.isNotEmpty(context.getOrder()) && Func.isNotEmpty(context.getOrder().getRelationship())
                    && Func.isNotEmpty(context.getOrder().getRelationship().getParent())){
                List<TrfBO> orderTrfList = context.getOrder().getRelationship().getParent().getTrfList();
                if(Func.isNotEmpty(orderTrfList)){
                    TrfBO sciTrf = orderTrfList.stream().filter(trfBO -> RefSystemIdEnum.check(trfBO.getRefSystemId(),RefSystemIdEnum.DAL)).findAny().orElse(null);
                    if(Func.isNotEmpty(sciTrf)){
                        updateExternalReportNo(context,sciTrf);
                    }
                }
            }
        }
        // 生成标准的数据结构
        // 6 组装Digital参数
        this.assembleDigitalRequest(context);
        //组装对象
        BaseResponse buildResponse = this.buildDomain(context);
        if (buildResponse.isFail()) {
            return buildResponse;
        }
        LanguageType primaryLanguage = LanguageType.English;
        BaseResponse<LanguageType> primaryLanguageRsp = ibuParam.getPrimaryLanguage(ProductLineContextHolder.getProductLineCode());
        List<BuParamValueRsp> buParamMainCurrencyList = null;
        if (Func.isNotEmpty(primaryLanguageRsp)) {
            primaryLanguage = primaryLanguageRsp.getData();
        }
        boolean isChinese = LanguageType.check(primaryLanguage.getLanguageId(), LanguageType.Chinese);
        context.setChineseFlag(isChinese);
        return super.before(context);
    }

    /**
     * DAL场景更新报告ExternalReportNo
     */
    private void updateExternalReportNo(ReportGenerateContext<ReportGenerateReq> context,TrfBO sciTrf){
        ReportUpdateReq reportUpdateReq = new ReportUpdateReq();
        reportUpdateReq.setReportId(context.getParam().getReportId());
        reportUpdateReq.setTestSampleIdList(context.getTestSampleIdList());
        reportUpdateReq.setTrf(sciTrf);
        reportDomainService.updateExternalReportNo(reportUpdateReq);
    }

    private void syncHostOrder(ReportGenerateContext<ReportGenerateReq> context) {
        OrderBO order = context.getOrder();
        ReportBO report = context.getDomain();
        //判断当前订单是否是分包创建的订单
        SubcontractPO subcontractPO = subcontractService.queryOrderIsSubOrder(order.getId().getOrderNo());
        if (Func.isEmpty(subcontractPO)) {
            return;
        }
        // Light 模式，接包方报告状态非New（Pending前是New）不允许同步；
        if (SubContractType.check(subcontractPO.getSubContractOrder(), SubContractType.LightSubContract)) {
            // 基于订单查询所有报告
            ReportQueryReq reportQueryReq = new ReportQueryReq();
            reportQueryReq.setOrderNoList(Sets.newHashSet(order.getId().getOrderNo()));
            BaseResponse<List<ReportBO>> reportListRes = reportDomainService.queryBO(reportQueryReq);
            if (Func.isNotEmpty(reportListRes.getData())) {
                List<ReportBO> reportList = reportListRes.getData();
                if (Func.isNotEmpty(reportList)) {
                    // 查看有效报告，排除当前报告
                    List<ReportBO> activeReportList = reportList.stream().filter(item -> !ReportStatus.checkCategory(item.getHeader().getReportStatus(), Constants.OBJECT.REPORT.STATUS_CATEGORY.INACTIVE)
                            && !item.getId().getReportId().equals(report.getId().getReportId())).collect(Collectors.toList());
                    // 存在非New报告（含Pending前是New），不做数据同步；
                    if (Func.isNotEmpty(activeReportList)) {
                        for (ReportBO activeReport : activeReportList) {
                            if (ReportStatus.check(activeReport.getHeader().getReportStatus(), ReportStatus.Pending)) {
                                // 判断报告Pending之前的状态
                                GpnStatusReq gpnStatusReq = new GpnStatusReq();
                                gpnStatusReq.setObjectNo(report.getId().getReportNo());
                                gpnStatusReq.setOperationType(1);
                                List<GpnStatusPO> gpnStatusList = gpnStatusService.query(gpnStatusReq);
                                if (Func.isNotEmpty(gpnStatusList)) {
                                    GpnStatusPO statusInfo = gpnStatusList.get(0);
                                    if (Func.isNotEmpty(statusInfo) && !ReportStatus.check(statusInfo.getOldStatus(), ReportStatus.New)) {
                                        log.info("跳过数据同步，ReportNo:{},Pending Status:{}", report.getId().getReportNo(), statusInfo.getOldStatus());
                                        return;
                                    }
                                }
                            }
                            if (!ReportStatus.check(activeReport.getHeader().getReportStatus(), ReportStatus.New, ReportStatus.Pending)) {
                                log.info("跳过数据同步，ReportNo:{}, report Status：{}", activeReport.getId().getReportNo(), activeReport.getHeader().getReportStatus());
                                return;
                            }
                        }
                    }
                }
                log.info("Order:{},开始数据同步", order.getId().getOrderNo());
            }
        }
        // 组装GeneralSync数据
        SodaSyncSubContractReq sodaSyncSubContractReq = new SodaSyncSubContractReq();
        ReportStatus reportStatus = ReportStatus.getCode(report.getHeader().getReportStatus());
        int orderStatus = order.getHeader().getOrderStatus();
        ProductLineType productLineAbbr = ProductLineType.findProductLineAbbr(ProductLineContextHolder.getProductLineCode());
        if ((productLineAbbr != null && ProductLineType.check(productLineAbbr.getProductLineId(), ProductLineType.AUTO, ProductLineType.MR, ProductLineType.HL)) &&
                (reportStatus.check(ReportStatus.New, ReportStatus.Draft)) && (OrderStatus.checkStatus(orderStatus, OrderStatus.New, OrderStatus.Testing, OrderStatus.Confirmed, OrderStatus.Reporting))) {
            // 判断当前订单是否是分包单
            if(subcontractPO != null && SubContractType.check(subcontractPO.getSubContractOrder(), SubContractType.SubContract, SubContractType.LightSubContract)) {
                QueryBuCodeRsp queryBuCodeRsp = frameworkClient.queryBuCodeBySubcontractNo(subcontractPO.getSubContractNo());
                sodaSyncSubContractReq.setOrderNo(order.getId().getOrderNo());
                sodaSyncSubContractReq.setOldOrderNo(subcontractPO.getOrderNo());
                sodaSyncSubContractReq.setProductLineCode(queryBuCodeRsp.getTargetBuCode());
                sodaSyncSubContractReq.setSourceProductLineCode(queryBuCodeRsp.getBuCode());
                sodaSyncSubContractReq.setReportNo(report.getId().getReportNo());
            }
        }
        if (sodaSyncSubContractReq != null && StringUtils.isNotEmpty(sodaSyncSubContractReq.getOldOrderNo())) {
            CustomResult customResult = sodaPreorderClient.generalSyncSubContractInfo(sodaSyncSubContractReq);
            if (customResult != null && customResult.isSuccess()) {
                BizLogHelper.setValue(order.getId().getOrderNo(),"Generate["+ report.getId().getActualReportNo()+"], Sync Subcontract From Data");
            } else {
                throw new BizException("Sync data fail, please contact system supporter");
            }
        }
    }


    private void assembleDigitalRequest(ReportGenerateContext<ReportGenerateReq> context) {
        List<DigitalReportReq> digitalReportReqList = Lists.newArrayList();
        String orderNo = context.getOrder().getId().getOrderNo();
        String reportNo = context.getDomain().getId().getReportNo();
        Integer reportActionType = context.getParam().getReportActionType();
        // 调用组装标准对象
        AssembleDataContext assembleDataContext = new AssembleDataContext();
        assembleDataContext.setPrintType(OutPutType.REPORT.getType());
        assembleDataContext.setReportActionType(context.getParam().getReportActionType());
        assembleDataContext.setPrintBy(context.getUserInfo().getRegionAccount());
        assembleDataContext.setOrderNo(orderNo);
        // 生成报告组装参数时根据Report->Matrix->TestLine只查询当前报告相关的数据
        assembleDataContext.setReportIdList(Sets.newHashSet(context.getParam().getReportId()));
        assembleDataContext.setTestLineInstanceIds(context.getTestLineInsIdList());
        assembleDataContext.setTestMatrixIds(context.getTestMatrixIdList());
        assembleDataContext.setTestSampleIdList(context.getTestSampleIdList());
        BaseResponse<OutPutDataBO> assembleResponse = outputFactory.assembleData(assembleDataContext);
        //Generate Report 加入数据提前校验
//        ObjectVerifyReq objectVerifyReq = new ObjectVerifyReq();
//        objectVerifyReq.setActionCode(Constants.ACTION.GENERATEREPORT);
//        objectVerifyReq.setOutPutDataBO(assembleResponse.getData());
//        BaseResponse<List<String>> verifyRsp = objectVerifyBizService.verify(objectVerifyReq);
//        if(Func.isEmpty(verifyRsp) || verifyRsp.isFail()){
//            return BaseResponse.newFailInstance("generate失败");
//        }
//        if(Func.isNotEmpty(verifyRsp.getData())){
//            // 验证失败 记录日志
//            SystemLog systemLog = new SystemLog();
//            systemLog.setObjectType("report");
//            systemLog.setObjectNo(context.getDomain().getId().getReportNo());
//            systemLog.setProductLineCode(context.getProductLineCode());
//            systemLog.setType(SystemLogType.API.getType());
//            systemLog.setRemark("调用Digital Report生成报告数据校验");
//            systemLog.setRequest(assembleResponse.getData().toString());
//            systemLog.setResponse(verifyRsp.getData().toString());
//            systemLog.setOperationType(Constants.ACTION.GENERATEREPORT + "数据校验");
//            systemLog.setLocationCode(context.getOrder().getLab().getLocationCode());
//            systemLog.setCreateBy("system");
//            systemLogHelper.save(systemLog);
//        }
        OutPutDataBO outPutData = assembleResponse.getData();
        // MultilingualReport 先按照Eng处理
        if (Func.isNotEmpty(outPutData.getOrder().getServiceRequirement().getReport().getReportLanguage())) {
            if (ReportLanguage.checkLanguage(outPutData.getOrder().getServiceRequirement().getReport().getReportLanguage().toString()
                    , ReportLanguage.MultilingualReport)) {
                outPutData.getOrder().getServiceRequirement().getReport().setReportLanguage(ReportLanguage.EnglishReportOnly.getLanguageId());
            }
        }
        if (ReportActionType.check(reportActionType, ReportActionType.GENERATE_TEST_RESULT)) {
            testMatrixDomainService.buildMatrixCombinedConditionDesc(outPutData);
        }
        if (ReportActionType.check(reportActionType, ReportActionType.GENERATE_REPORT_BY_TL_REPORT)) {
            //重新对TLReport按照TestLineList的顺序排序
            this.sortTlReportByTLSeq(outPutData);
        }
        if (ReportActionType.check(reportActionType, ReportActionType.GENERATE_REPORT)) {
            // 查询ReportSampleResubmissionDate
            assembleSampleResubmissionDate(outPutData);
        }
        // GPO2-14300 Revise Extract/Translation 测试开始日期取OldReportNo对应的order confirm date
        Integer operationType = outPutData.getOrder().getHeader().getOperationType();
        String rootReportNo = context.getDomain().getId().getRootReportNo();
        if (OrderOperationType.check(operationType, OrderOperationType.TranslationReport, OrderOperationType.Extract) && Func.isNotEmpty(rootReportNo)) {
            // 查询 rootReportNo对应的订单confirmDate
            ReportQueryReq reportQueryReq = new ReportQueryReq();
            reportQueryReq.setReportNo(rootReportNo);
            BaseResponse<List<ReportBO>> reportListRes = reportDomainService.queryBO(reportQueryReq);
            if (Func.isNotEmpty(reportListRes.getData())) {
                ReportBO rootReport = reportListRes.getData().get(0);
                String rootOrderNo = rootReport.getRelationship().getParent().getOrder().getOrderNo();
                // 查询order confirm Date
                OrderQueryReq orderQueryReq = new OrderQueryReq();
                orderQueryReq.setOrderNoList(Sets.newHashSet(rootOrderNo));
                BaseResponse<List<com.sgs.framework.model.order.order.OrderBO>> orderRes = orderDomainService.queryV1(orderQueryReq);
                if (Func.isNotEmpty(orderRes.getData()) && Func.isNotEmpty(orderRes.getData().get(0).getHeader().getOrderConfirmDate())) {
                    outPutData.getOrder().getHeader().setOrderConfirmDate(orderRes.getData().get(0).getHeader().getOrderConfirmDate());
                }
            }
        }
        // 查询extConclusionSummary
        ConclusionReq conclusionReq = new ConclusionReq();
        conclusionReq.setReportId(context.getParam().getReportId());
        conclusionReq.setProductLineCode(context.getProductLineCode());
        BaseResponse<List<ConclusionExtBO>> conclusionRes = dataEntryClient.getConclusionExtList(conclusionReq);
        if (conclusionRes.isSuccess() && Func.isNotEmpty(conclusionRes.getData())) {
            outPutData.setExtConclusionSummaryList(conclusionRes.getData());
        }
        this.handleTestLineForSubPP(outPutData.getTestLineList());
        // Datasource
        DigitalReportDataSourceBO datasource = new DigitalReportDataSourceBO();
        datasource.setProductLineCode(context.getProductLineCode());
        datasource.setVariableDataList(outPutData);

        // InstanceNumber 取外部号
        String instanceNumber = null;
        if (Func.isNotEmpty(outPutData.getReportList())) {
            String actualReportNo = outPutData.getReportList().get(0).getId().getActualReportNo();
            if (Func.isNotEmpty(actualReportNo)) {
                instanceNumber = actualReportNo;
            }
        }
        // TODO HL临时逻辑Buyer有值取Buyer groupCode
        String groupCode = "General";
        List<CustomerBO> customerList = outPutData.getOrder().getCustomerList();
        if( Func.isNotEmpty(customerList)){
            if (Func.equalsSafe(outPutData.getOrder().getLab().getBuCode(), ProductLines.HARDLINE.getCode())) {
                CustomerBO buyer = customerList.stream().filter(item -> Func.equalsSafe(item.getCustomerUsage(), CustomerType.Buyer.getStatus())).findAny().orElse(null);
                if (Func.isNotEmpty(buyer) && Func.isNotEmpty(buyer.getCustomerGroupCode())) {
                    groupCode = buyer.getCustomerGroupCode();
                }
            }
            if (ReportActionType.check(context.getParam().getReportActionType(), ReportActionType.GENERATE_TEST_RESULT) || ReportActionType.check(context.getParam().getReportActionType(), ReportActionType.GENERATE_REPORT_BY_TL_REPORT)) {
                // PK逻辑
                CustomerBO oem = customerList.stream().filter(customerBO -> Func.equalsSafe(CustomerType.OEM.getStatus(), customerBO.getCustomerUsage())).findAny().orElse(null);
                CustomerBO buyer = customerList.stream().filter(customerBO -> Func.equalsSafe(CustomerType.Buyer.getStatus(), customerBO.getCustomerUsage())).findAny().orElse(null);
                CustomerBO agent = customerList.stream().filter(customerBO -> Func.equalsSafe(CustomerType.Agent.getStatus(), customerBO.getCustomerUsage())).findAny().orElse(null);

                if (Func.isNotEmpty(oem)) {
                    groupCode = oem.getCustomerGroupCode();
                } else if (Func.isNotEmpty(buyer)) {
                    groupCode = buyer.getCustomerGroupCode();
                }else if (Func.isNotEmpty(agent)) {
                    groupCode = agent.getCustomerGroupCode();
                }
            }
        }

        if (Func.isNotEmpty(context.getReportTemplateCn())) {
            DigitalReportInfoBO info = new DigitalReportInfoBO(context.getProductLineCode());
            info.setInstanceNumber(Func.isNotEmpty(instanceNumber) ? instanceNumber : reportNo);
            info.setBuCode(context.getProductLineCode());
            info.setTemplateSettingID(context.getReportTemplateCn().getTemplateSettingId());
            info.setLanguageID(LanguageTypeDigitalReport.CN.getLanguageId());
            info.setGroupCode(groupCode);
            digitalReportReqList.add(new DigitalReportReq(info, Func.deepCopy(datasource, DigitalReportDataSourceBO.class)));
            context.getParam().setHasFileCn(true);
        }
        if (Func.isNotEmpty(context.getReportTemplateEn())) {
            DigitalReportInfoBO info = new DigitalReportInfoBO(context.getProductLineCode());
            info.setInstanceNumber(Func.isNotEmpty(instanceNumber) ? instanceNumber : reportNo);
            info.setBuCode(context.getProductLineCode());
            info.setTemplateSettingID(context.getReportTemplateEn().getTemplateSettingId());
            info.setLanguageID(LanguageTypeDigitalReport.EN.getLanguageId());
            info.setGroupCode(groupCode);
            digitalReportReqList.add(new DigitalReportReq(info, Func.deepCopy(datasource, DigitalReportDataSourceBO.class)));
            context.getParam().setHasFileEn(true);
        }
        //GPO2-1481 如果出双语报告，则中文报告的报告号、文件名加’_CN’
        if (digitalReportReqList.size() > 1) {
            digitalReportReqList.stream().filter(item -> LanguageTypeDigitalReport.CN.getLanguageId() == item.getInfo().getLanguageID() && Func.isNotEmpty(item.getDatasource().getVariableDataList().getReportList()))
                    .forEach(item -> item.getDatasource().getVariableDataList().getReportList().stream().forEach(report -> report.getId().setReportNo(report.getId().getReportNo() + "_CN")));
        }
        context.setDigitalReportReqList(digitalReportReqList);
    }

    /**
     * 获取 ReportSampleResubmissionDate
     * @param outPutData
     */
    private void assembleSampleResubmissionDate(OutPutDataBO outPutData) {
        if(Func.isEmpty(outPutData) || Func.isAnyEmpty(outPutData.getOrder(),outPutData.getTestLineList(),outPutData.getReportList()) || Func.isEmpty(outPutData.getOrder().getHeader())){
            return;
        }
        Set<String> testLineInstanceIdList = outPutData.getTestLineList().stream().filter(item -> Func.isNotEmpty(item.getId())).map(e->e.getId().getTestLineInstanceId()).collect(Collectors.toSet());
        if (Func.isEmpty(testLineInstanceIdList)){
            return;
        }
        // 1、查询Order对应的ReportSampleResubmissionDate
        Date orderResubmissionDate = outPutData.getOrder().getHeader().getSampleResubmissionDate();
        Date jobSubResubmissionDate = null;
        Set<String> executionNos = new HashSet<>();
        // 2、查询TestLine对应的job
        JobQueryReq jobQueryReq = new JobQueryReq();
        jobQueryReq.setTestLineInstanceIdList(testLineInstanceIdList);
        BaseResponse<List<JobPO>> jobRes = jobService.queryJobByTestLine(jobQueryReq);
        if (jobRes.isSuccess() && Func.isNotEmpty(jobRes.getData())) {
            Set<String> jobNos = jobRes.getData().stream().map(JobPO::getJobNo).collect(Collectors.toSet());
            executionNos.addAll(jobNos);
        }
        // 3、查询TestLine对应的Subcontract
        SubcontractQueryReq subcontractQueryReq = new SubcontractQueryReq();
        subcontractQueryReq.setTestLineInstanceIdList(testLineInstanceIdList);
        BaseResponse<List<SubcontractPO>> subcontractRes = subcontractService.querySubcontractByTestLine(subcontractQueryReq);
        if (subcontractRes.isSuccess() && Func.isNotEmpty(subcontractRes.getData())) {
            Set<String> subcontractNos = subcontractRes.getData().stream().map(SubcontractPO::getSubContractNo).collect(Collectors.toSet());
            executionNos.addAll(subcontractNos);
        }
        if (Func.isNotEmpty(executionNos)){
            // 4、查询job、Subcontract对应的ReportSampleResubmissionDate
            ResubmissionQueryReq resubmissionQueryReq = new ResubmissionQueryReq();
            resubmissionQueryReq.setExecutionNos(executionNos);
            resubmissionQueryReq.setActiveIndicator(ActiveType.Enable.getStatus());
            BaseResponse<List<ExecutionResubmissionRecordPO>> executionRes = executionResubmissionService.select(resubmissionQueryReq);
            if (executionRes.isSuccess() && Func.isNotEmpty(executionRes.getData())) {
                Optional<ExecutionResubmissionRecordPO> executionResubmission = executionRes.getData().stream().filter(e -> Func.isNotEmpty(e.getResubmitDate())).max(Comparator.comparing(ExecutionResubmissionRecordPO::getResubmitDate));
                if (executionResubmission.isPresent()) {
                    ExecutionResubmissionRecordPO executionResubmissionRecordPO = executionResubmission.get();
                    jobSubResubmissionDate = executionResubmissionRecordPO.getResubmitDate();
                }
            }
        }
        // 5、ReportSampleResubmissionDate取最大值
        Date maxSampleResubmissionDate = DateUtils.getMaxDate(orderResubmissionDate, jobSubResubmissionDate);
        // 6、设置ReportSampleResubmissionDate
        outPutData.getReportList().forEach(e -> {
            if(Func.isNotEmpty(e.getHeader())){
                e.getHeader().setReportSampleResubmissionDate(maxSampleResubmissionDate);
            }
        });
    }
    private Map<String, Integer> buildTestLineIndexMap(List<TestLineBO> testLineBOList) {
        Map<String, Integer> indexMap = new HashMap<>();
        if (Func.isEmpty(testLineBOList)) {
            return indexMap;
        }
        for (int i = 0; i < testLineBOList.size(); i++) {
            TestLineBO testLine = testLineBOList.get(i);
            if (Func.isNotEmpty(testLine.getId()) && Func.isNotEmpty(testLine.getId().getTestLineInstanceId())) {
                indexMap.put(testLine.getId().getTestLineInstanceId(), i);
            }
        }
        return indexMap;
    }
    private void sortTlReportByTLSeq(OutPutDataBO outPutData){
        if(Func.isEmpty(outPutData) || Func.isEmpty(outPutData.getReportList())){
            return;
        }
        List<TestLineBO> testLineList = outPutData.getTestLineList();
        for (ReportBO reportBO : outPutData.getReportList()) {
            List<AttachmentBO> attachmentList = reportBO.getAttachmentList();
            if(Func.isEmpty(attachmentList)){
                continue;
            }
            Map<String, Integer> testLineIndexMap = buildTestLineIndexMap(testLineList);
            // 排序时直接查 map：
            Collections.sort(attachmentList, (s1, s2) -> {
                int index1 = testLineIndexMap.getOrDefault(s1.getObjectId(), -1);
                int index2 = testLineIndexMap.getOrDefault(s2.getObjectId(), -1);
                return Integer.compare(index1, index2);
            });
            for (int i = 0; i < attachmentList.size(); i++) {
                int seq = i+1;
                attachmentList.get(i).setDisplaySeq(Func.toLong(seq));
            }
            reportBO.setAttachmentList(attachmentList);
        }
    }
    /*private  int getIndexByTestLineList(List<TestLineBO> testLineBOList, String objectId) {
        if(Func.isNotEmpty(testLineBOList)){
            for (int i = 0; i < testLineBOList.size(); i++) {
                if (Func.equalsSafe(objectId,testLineBOList.get(i).getId().getTestLineInstanceId())) {
                    return i;
                }
            }
        }
        return -1;
    }
*/
    private void handleTestLineForSubPP(List<TestLineBO> testLineList) {
        // SubPP特殊处理逻辑
        if (Func.isEmpty(testLineList)) {
            return;
        }
        List<Long> artifactIds = Lists.newArrayList();
        List<PpArtifactRsp> ppArtifactList = Lists.newArrayList();
        testLineList.stream().forEach(testLine -> {
            if (Func.isEmpty(testLine.getPpTestLineRelList())) {
                return;
            }
            PPTestLineRelBO ppTestLineRel = testLine.getPpTestLineRelList().get(0);
            if (Func.isEmpty(ppTestLineRel.getHeader().getRootPP())) {
                return;
            }
            if (Func.isEmpty(ppTestLineRel.getHeader().getRootPP().getPpAid())) {
                return;
            }
            artifactIds.add(Long.valueOf(ppTestLineRel.getHeader().getRootPP().getPpAid()));
        });
        if (Func.isNotEmpty(artifactIds)) {
            PpArtifactRelListReq ppArtifactRelListReq = new PpArtifactRelListReq();
            ppArtifactRelListReq.setArtifactIds(artifactIds);
            ppArtifactList = trimsClient.getPpArtifactRelListByArtifactIds(ppArtifactRelListReq);
        }
        // 使用SubPP查询出的名称替换TestLine的名称
        if (Func.isNotEmpty(ppArtifactList)) {
            List<PpArtifactRsp> finalPpArtifactList = ppArtifactList;
            testLineList.stream().forEach(testLine -> {
                if (Func.isEmpty(testLine.getPpTestLineRelList())) {
                    return;
                }
                PPTestLineRelBO ppTestLineRel = testLine.getPpTestLineRelList().get(0);
                if (Func.isEmpty(ppTestLineRel.getHeader().getRootPP())) {
                    return;
                }
                if (Func.isEmpty(ppTestLineRel.getHeader().getRootPP().getPpAid())) {
                    return;
                }
                PpArtifactRsp ppArtifactRsp = finalPpArtifactList.stream().filter(item -> item.getArtifactId().intValue() == ppTestLineRel.getHeader().getRootPP().getPpAid())
                        .findAny().orElse(null);
                if (Func.isEmpty(ppArtifactRsp)) {
                    return;
                }
                testLine.getHeader().setEvaluationName(ppArtifactRsp.getEvaluationAlias());
                // 多语言处理
                if (Func.isEmpty(testLine.getHeader().getLanguageList())) {
                    testLine.getHeader().setLanguageList(Lists.newArrayList());
                }
                PpArtifactRelLangInfoRsp ppArtifactRelLangCN = null;
                if (Func.isNotEmpty(ppArtifactRsp.getLanguageList())) {
                    ppArtifactRelLangCN = ppArtifactRsp.getLanguageList().stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.Chinese))
                            .findAny().orElse(null);
                }
                TestLineLanguageBO testLineLanguageCN = testLine.getHeader().getLanguageList().stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.Chinese))
                        .findAny().orElse(null);
                TestLineLanguageBO testLineLanguageEN = testLine.getHeader().getLanguageList().stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.English))
                        .findAny().orElse(null);
                if (Func.isNotEmpty(ppArtifactRelLangCN) && Func.isNotEmpty(ppArtifactRelLangCN.getEvaluationAlias())) {
                    if (Func.isNotEmpty(testLineLanguageCN)) {
                        testLineLanguageCN.setEvaluationName(ppArtifactRelLangCN.getEvaluationAlias());
                    } else {
                        testLineLanguageCN = new TestLineLanguageBO();
                        testLineLanguageCN.setEvaluationName(ppArtifactRelLangCN.getEvaluationAlias());
                        testLineLanguageCN.setLanguageId(LanguageType.Chinese.getLanguageId());
                        testLine.getHeader().getLanguageList().add(testLineLanguageCN);
                    }
                }
                if (Func.isEmpty(ppArtifactRelLangCN) && Func.isNotEmpty(testLineLanguageCN)) {
                    testLineLanguageCN.setEvaluationName(null);
                }
                if (Func.isNotEmpty(testLineLanguageEN)) {
                    testLineLanguageEN.setEvaluationName(ppArtifactRsp.getEvaluationAlias());
                } else {
                    testLineLanguageEN = new TestLineLanguageBO();
                    testLineLanguageEN.setEvaluationName(ppArtifactRsp.getEvaluationAlias());
                    testLineLanguageEN.setLanguageId(LanguageType.English.getLanguageId());
                    testLine.getHeader().getLanguageList().add(testLineLanguageEN);
                }
                if(TestLineType.check(testLine.getHeader().getTestLineType(),TestLineType.SUB_PP)){
                    // SubPP PPNotes处理
                    if(Func.isEmpty(ppArtifactRsp.getPpNotesAlias())){
                        return;
                    }
                    ppTestLineRel.getHeader().setPpNotes(ppArtifactRsp.getPpNotesAlias());
                    // 多语言处理
                    if(Func.isEmpty(ppArtifactRsp.getLanguageList())){
                        ppArtifactRsp.setLanguageList(Lists.newArrayList());
                    }
                    // CN
                    PpArtifactRelLangInfoRsp ppArtifactRelCN = ppArtifactRsp.getLanguageList().stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.Chinese)).findAny().orElse(null);
                    // EN
                    PpArtifactRelLangInfoRsp ppArtifactRelEN = ppArtifactRsp.getLanguageList().stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.English)).findAny().orElse(null);
                    if(Func.isEmpty(ppTestLineRel.getHeader().getLanguageList())){
                        ppTestLineRel.getHeader().setLanguageList(Lists.newArrayList());
                    }
                    List<PPLanguageBO> ppLanguageList = Lists.newArrayList();
                    //TL上的CN信息
                    if(Func.isNotEmpty(ppArtifactRelCN) && Func.isNotEmpty(ppArtifactRelCN.getPpNotesAlias())){
                        PPLanguageBO ppLanguageCN = ppTestLineRel.getHeader().getLanguageList().stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.Chinese)).findAny().orElse( null);
                        if(Func.isEmpty(ppLanguageCN)){
                            ppLanguageCN = new PPLanguageBO();
                            ppLanguageCN.setLanguageId(LanguageType.Chinese.getLanguageId());
                        }
                        ppLanguageCN.setPpNotes(ppArtifactRelCN.getPpNotesAlias());
                        ppLanguageList.add(ppLanguageCN);
                    }

                    //TL上的EN信息
                    String ppNotesEN = Func.isNotEmpty(ppArtifactRsp.getPpNotesAlias())?ppArtifactRsp.getPpNotesAlias():ppArtifactRelEN.getPpNotesAlias();
                    if(Func.isEmpty(ppArtifactRelEN)&& Func.isNotEmpty(ppNotesEN)){
                        PPLanguageBO ppLanguageEN = ppTestLineRel.getHeader().getLanguageList().stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.English)).findAny().orElse( null);
                        if(Func.isEmpty(ppLanguageEN)){
                            ppLanguageEN = new PPLanguageBO();
                            ppLanguageEN.setLanguageId(LanguageType.English.getLanguageId());
                        }
                        ppLanguageEN.setPpNotes(ppNotesEN);
                        ppLanguageList.add(ppLanguageEN);
                    }
                    if(Func.isNotEmpty(ppLanguageList)){
                        ppTestLineRel.getHeader().setLanguageList(ppLanguageList);
                    }
                }
            });
        }
    }

    @Override
    public BaseResponse execute(ReportGenerateContext<ReportGenerateReq> context) {
        if(Func.equalsSafe(context.getLab().getLabCode(),Constants.TW_SL)){
            // 处理Report/ReportFile相关的更新
            BaseResponse generateRes = reportDomainService.generateReport(context.getParam());
            if (generateRes.isFail()) {
                throw new BizException(generateRes.getMessage());
            }
            SodaGenerateReportReq generateReportReq = new SodaGenerateReportReq();
            generateReportReq.setToken(context.getToken());
            generateReportReq.setProductLineCode(context.getProductLineCode());
            generateReportReq.setReportId(context.getParam().getReportId());
            generateReportReq.setCallbackUrl(interfaceConfig.getBaseUrl() + Constants.DIGITAL_REPORT_CALL_BACK_URL + Func.toStr(ReportActionType.GENERATE_REPORT.getCode()));
            return sodaNotesClient.generateReport(generateReportReq);
        }
        if (Func.isNotEmpty(context.getDigitalReportReqList())) {
            // 处理Report/ReportFile相关的更新
            BaseResponse generateRes = reportDomainService.generateReport(context.getParam());
            if (generateRes.isFail()) {
                throw new BizException(generateRes.getMessage());
            }
            if (ReportActionType.check(context.getParam().getReportActionType(), ReportActionType.GENERATE_REPORT) || ReportActionType.check(context.getParam().getReportActionType(), ReportActionType.GENERATE_REPORT_BY_TL_REPORT)) {
                // Lock order_report_receiver
                OrderQueryReq orderLockReq = new OrderQueryReq();
                orderLockReq.setOrderIdList(Sets.newHashSet(context.getOrder().getId().getOrderId()));
                BaseResponse<Boolean> lockRes = orderDomainService.lockOrderReportReceiver(orderLockReq);
                if (lockRes.isFail()) {
                    throw new BizException(lockRes.getMessage());
                }
            }
            // 触发调用digital接口
            this.callDigitalReport(context);
            // 5 TODO 保存数据到tb_object_relationship，取匹配到的attachment
        }
        return BaseResponse.newSuccessInstance(true);
    }

    /**
     * 调用Digital系统
     * Digital 异步返回报告
     */
    private void callDigitalReport(ReportGenerateContext<ReportGenerateReq> context) {
        // 设置异步回写函数地址
        Integer reportActionType = context.getParam().getReportActionType();
        if(ReportActionType.check(reportActionType,ReportActionType.GENERATE_REPORT) || ReportActionType.check(reportActionType,ReportActionType.GENERATE_REPORT_BY_TL_REPORT)){
            reportActionType = ReportActionType.GENERATE_REPORT.getCode();
        }
        CallbackSetting callbackSetting = new CallbackSetting();
        callbackSetting.setUri(interfaceConfig.getBaseUrl() + Constants.DIGITAL_REPORT_CALL_BACK_URL + Func.toStr(reportActionType));
        context.getDigitalReportReqList().stream().forEach(item -> item.setCallbackSetting(callbackSetting));
        String url = interfaceConfig.getDigitalReportUrl() + "/api/v3/ReportAPI/GenerateReport?" +
                "appid=11&token=";
        if (ReportActionType.check(context.getParam().getReportActionType(), ReportActionType.GENERATE_TEST_RESULT)) {
            url = interfaceConfig.getDigitalReportUrl() + "/api/v3/ReportAPI/GenerateTestResult?" +
                    "appid=11&token=";
        }else if (ReportActionType.check(context.getParam().getReportActionType(), ReportActionType.GENERATE_REPORT_BY_TL_REPORT)) {
            url = interfaceConfig.getDigitalReportUrl() + "/api/v3/ReportAPI/GenerateNoStyleReport?" +
                    "appid=11&token=";
        }

        BaseResponse generateRes = digitalReportClient.generateReport(JsonUtil.toJson(context.getDigitalReportReqList()), url);
        // 记录接口返回结果
        String jsonResult = JsonUtil.toJson(generateRes);
        // 调用接口记录请求日志
        SystemLog requestLog = new SystemLog();
        requestLog.setObjectType("report");
        requestLog.setObjectNo(context.getDomain().getId().getReportNo());
        requestLog.setProductLineCode(context.getProductLineCode());
        requestLog.setType(SystemLogType.API.getType());
        requestLog.setRemark("调用Digital Report生成报告");
        String requestParam = JsonUtil.toJson(context.getDigitalReportReqList());
        // 入参数据太大会导致消息队列消费失败，目前最大支持1M
        if (requestParam.length() > 1000000) {
            requestParam = requestParam.substring(0, 700000);
        }
        requestLog.setRequest(requestParam);
        requestLog.setOperationType(OperationTypeEnums.GenerateReport.getValue());
        requestLog.setLocationCode(context.getOrder().getLab().getLocationCode());
        requestLog.setCreateBy(SystemContextHolder.getUserInfoFillSystem().getRegionAccount());
        requestLog.setUrl(url);
        requestLog.setResponse(jsonResult);
        systemLogHelper.save(requestLog);

        if (generateRes.isFail()) {
            throw new BizException(generateRes.getMessage());
        }
    }


    @Override
    public BaseResponse after(ReportGenerateContext<ReportGenerateReq> context) {
        // 调用tracking
        trackingService.sendReportEdit(context.getOrder().getId().getOrderNo(), context.getToken(),
                context.getUserInfo().getRegionAccount(), context.getDomain().getId().getReportNo());

        // 生成报告的场景记录单据状态变化
        if (ReportActionType.check(context.getParam().getReportActionType(), ReportActionType.GENERATE_REPORT) ||
                ReportActionType.check(context.getParam().getReportActionType(), ReportActionType.GENERATE_REPORT_BY_TL_REPORT)) {
            // 记录Biz记录报告状态变化
            BizLogInfo bizLog = new BizLogInfo();
            bizLog.setBu(ProductLineContextHolder.getProductLineCode());
            bizLog.setLab(context.getOrder().getLab().getLocationCode());
            bizLog.setOpUser(context.getUserInfo().getRegionAccount());
            bizLog.setBizId(context.getDomain().getId().getReportNo());
            bizLog.setOpType(ReportOperationTypeEnum.GENERATE.getType());
            bizLog.setBizOpType(BizLogConstant.REPORT_STATUS_CHANGE_HISTORY);
            bizLog.setNewVal(ReportStatus.Draft.getCode());
            bizLog.setOriginalVal(ReportStatus.New.getCode());
            bizLogClient.doSend(bizLog);

            // 记录Biz记录生成报告的操作
            BizLogInfo bizLogInfo = new BizLogInfo();
            bizLogInfo.setBu(Func.isNotEmpty(context.getOrder().getLab().getBuCode()) ? context.getOrder().getLab().getBuCode() : ProductLineContextHolder.getProductLineCode());
            bizLogInfo.setLab(context.getOrder().getLab().getLocationCode());
            bizLogInfo.setOpUser(context.getUserInfo().getRegionAccount());
            bizLogInfo.setBizId(context.getOrder().getId().getOrderNo());
            bizLogInfo.setNewVal("Generate[" + context.getDomain().getId().getReportNo() + "]");
            bizLogInfo.setOpType("generate");
            bizLogInfo.setBizOpType(BizLogConstant.REPORT_OPERATION_HISTORY);
            log.info("bizLog param: {}", JSON.toJSONString(bizLogInfo));
            bizLogClient.doSend(bizLogInfo);
        }

        if(ReportActionType.check(context.getParam().getReportActionType(), ReportActionType.GENERATE_TEST_RESULT)){
            BizLogInfo bizLog = new BizLogInfo();
            bizLog.setBu(Func.isNotEmpty(context.getOrder().getLab().getBuCode()) ? context.getOrder().getLab().getBuCode() : ProductLineContextHolder.getProductLineCode());
            bizLog.setLab(context.getOrder().getLab().getLocationCode());
            bizLog.setOpUser(context.getUserInfo().getRegionAccount());
            bizLog.setBizId(context.getOrder().getId().getOrderNo());
            bizLog.setOpType("Generate Test Result");
            bizLog.setBizOpType(BizLogConstant.REPORT_OPERATION_HISTORY);
            bizLog.setNewVal("generateTestResult[" + context.getDomain().getId().getActualReportNo() + "]");
            bizLogClient.doSend(bizLog);
        }
        return super.after(context);
    }

}
