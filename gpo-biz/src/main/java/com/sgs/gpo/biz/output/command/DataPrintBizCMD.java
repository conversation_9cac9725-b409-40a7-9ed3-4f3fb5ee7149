package com.sgs.gpo.biz.output.command;

import com.beust.jcommander.internal.Sets;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.core.model.Lab;
import com.sgs.framework.log.SystemLogHelper;
import com.sgs.framework.log.enums.SystemLogType;
import com.sgs.framework.log.model.SystemLog;
import com.sgs.framework.model.common.print.DigitalReportDataSourceBO;
import com.sgs.framework.model.common.print.OutPutDataBO;
import com.sgs.framework.model.common.print.v1.DigitalReportDataSourceUltraBO;
import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.model.enums.TestLineStatus;
import com.sgs.framework.model.order.v2.TestLineIdRelBO;
import com.sgs.framework.model.test.execution.v2.job.JobBO;
import com.sgs.framework.model.test.execution.v2.job.JobTestLineRelBO;
import com.sgs.framework.model.test.execution.v2.subcontract.SubcontractBO;
import com.sgs.framework.model.test.pp.pptestline.v2.PPTestLineRelBO;
import com.sgs.framework.model.test.pp.priceAttribute.PriceAttributeBO;
import com.sgs.framework.model.test.pp.priceAttribute.PriceAttributeLanguageBO;
import com.sgs.framework.model.test.testline.v2.TestLineBO;
import com.sgs.framework.model.test.testline.v2.TestLineSampleBO;
import com.sgs.framework.model.test.testsample.v2.TestSampleBO;
import com.sgs.framework.tool.jackson.JsonUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.biz.output.context.AssembleDataContext;
import com.sgs.gpo.biz.output.context.DataPrintContext;
import com.sgs.gpo.biz.output.service.ObjectConvertService;
import com.sgs.gpo.biz.output.service.OutputFactory;
import com.sgs.gpo.core.constants.BizLogConstant;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.enums.OutPutType;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.objectsamplequantity.ObjectSampleQuantityPO;
import com.sgs.gpo.domain.service.otsnotes.objectsamplequantity.IObjectSampleQuantityService;
import com.sgs.gpo.domain.service.setting.buparam.IBUParam;
import com.sgs.gpo.facade.model.otsnotes.objectsamplequantity.req.ObjectSampleQuantityQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testsample.dto.PPSampleCombinedDTO;
import com.sgs.gpo.facade.model.otsnotes.testsample.dto.TestSampleDTO;
import com.sgs.gpo.facade.model.print.req.PrintDataReq;
import com.sgs.gpo.integration.digital.DigitalReportClient;
import com.sgs.gpo.integration.digital.req.DigitalReportInfoBO;
import com.sgs.gpo.integration.digital.req.v2.DigitalReportReq;
import com.sgs.gpo.integration.digital.req.v2.DigitalReportUltraReq;
import com.sgs.gpo.integration.quotation.QuotationClient;
import com.sgs.grus.bizlog.BizLogClient;
import com.sgs.grus.bizlog.info.BizLogInfo;
import com.sgs.otsnotes.facade.model.enums.PrintType;
import com.sgs.priceengine.facade.model.enums.QuotationStatus;
import com.sgs.priceengine.facade.model.request.quotationinfo.GetQuotationInfoReq;
import com.sgs.priceengine.facade.model.response.quotationinfo.OrderQuotationRsp;
import com.sgs.priceengine.facade.model.response.quotationinfo.QuotationServiceItemInfoRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 打印服务
 */
@Service
@Slf4j
public class DataPrintBizCMD extends BaseCommand<DataPrintContext> {

    @Autowired
    private OutputFactory outputFactory;
    @Autowired
    private DigitalReportClient digitalReportClient;
    @Autowired
    private BizLogClient bizLogClient;
    @Autowired
    private SystemLogHelper systemLogHelper;
    @Autowired
    private IBUParam buParam;
    @Autowired
    private ObjectConvertService convertService;
    @Autowired
    private QuotationClient quotationClient;
    @Autowired
    private IObjectSampleQuantityService objectSampleQuantityService;

    /**
     * 校验打印参数
     *
     * @param context
     * @return
     */
    @Override
    public BaseResponse validateParam(DataPrintContext context) {
        if (Func.isEmpty(context.getParam()) || Func.isEmpty(context.getParam().getPrintType())) {
            return BaseResponse.newFailInstance("打印参数缺少");
        }
        if (Func.isEmpty(context.getParam().getOrderList())) {
            return BaseResponse.newFailInstance("请选择打印的订单");
        }
        if (context.getParam().getOrderList().size() > 30) {
            return BaseResponse.newFailInstance("一次最多只允许打印30个订单.");
        }
        return BaseResponse.newSuccessInstance(true);
    }

    private BaseResponse validateTestLine(OutPutDataBO printData, AssembleDataContext assembleDataContext) {
        BaseResponse response = new BaseResponse();
        // 查询订单下的所有有效的testLine
        if (Func.isEmpty(assembleDataContext.getOrderTestLineMaps())) {
            return BaseResponse.newFailInstance("No TestLine Or All Cancel Status!");
        }
        List<TestLineBO> orderTestLineList = assembleDataContext.getOrderTestLineMaps()
                .get(assembleDataContext.getOrderNo());
        if (Func.isEmpty(orderTestLineList)) {
            return BaseResponse.newFailInstance("No TestLine Or All Cancel Status!");
        }
        orderTestLineList = orderTestLineList.stream().filter(testLine ->
                !TestLineStatus.check(testLine.getHeader().getTestLineStatus(), TestLineStatus.Cancelled)).collect(Collectors.toList());
        if (Func.isEmpty(orderTestLineList)) {
            return BaseResponse.newFailInstance("No TestLine Or All Cancel Status!");
        }
        // 过滤job-testLineList
        List<JobTestLineRelBO> jobTestLineList = assembleDataContext.getJobTestLineMaps().get(assembleDataContext.getOrderNo());
        if (Func.isNotEmpty(jobTestLineList)) {
            Set<String> jobTestLineSets = jobTestLineList.stream().map(JobTestLineRelBO::getTestLineInstanceId).collect(Collectors.toSet());
            if (Func.isNotEmpty(jobTestLineSets)) {
                orderTestLineList = orderTestLineList.stream().filter(testLine -> !jobTestLineSets.contains(testLine.getId().getTestLineInstanceId()))
                        .collect(Collectors.toList());
            }
        }

        // 过滤subcontract-testLineList
        if (Func.isNotEmpty(printData.getSubcontractList())) {
            Set<String> subContractTestLineSets = printData.getSubcontractList().stream()
                    .filter(item -> Func.isNotEmpty(item.getRelationship().getChildren().getTestLineList()))
                    .flatMap(data -> data.getRelationship().getChildren().getTestLineList().stream())
                    .map(TestLineIdRelBO::getTestLineInstanceId).collect(Collectors.toSet());
            if (Func.isNotEmpty(subContractTestLineSets)) {
                orderTestLineList = orderTestLineList.stream().filter(testLine -> !subContractTestLineSets.contains(testLine.getId().getTestLineInstanceId()))
                        .collect(Collectors.toList());
            }
        }
        // 没有设置labSection不需要校验
        // 如果TL Completed了，则不需要检查是否创建Job or 创建subcontract
        orderTestLineList = orderTestLineList.stream().filter(testLine -> Func.isNotEmpty(testLine.getLabSectionList())
                && !TestLineStatus.check(testLine.getHeader().getTestLineStatus(), TestLineStatus.Completed, TestLineStatus.NC))
                .collect(Collectors.toList());
        // 判断是否存在不存在任何关联的testLine
        if (Func.isNotEmpty(orderTestLineList)) {
            return BaseResponse.newFailInstance("Please create job/subcontract first!");
        }
        return response;
    }

    /**
     * 根据PrintType动态组装打印参数
     *
     * @param context
     * @return
     */
    @Override
    public BaseResponse before(DataPrintContext context) {
        List<DigitalReportReq> digitalReportReqList = Lists.newArrayList();
        // 根据订单分开组装
        BaseResponse response = BaseResponse.newSuccessInstance(true);
        List<OutPutDataBO> printDataBOList = new ArrayList<>();
        PrintDataReq printDataReq = context.getParam();
        context.setSlMaster(OutPutType.check(printDataReq.getPrintType(), OutPutType.MASTER_LIST) &&
                Func.equalsSafe(ProductLineType.SL.getProductLineAbbr(), context.getProductLineCode()));
        printDataReq.getOrderList().forEach(order -> {
            // Info
            DigitalReportInfoBO info = new DigitalReportInfoBO(context.getProductLineCode());
            info.setInstanceNumber(order.getOrderNo());
            AssembleDataContext assembleDataContext = new AssembleDataContext();
            assembleDataContext.setPrintType(printDataReq.getPrintType());
            assembleDataContext.setPrintBy(context.getUserInfo().getRegionAccount());
            assembleDataContext.setPrintByPhone(context.getUserInfo().getTelephone());
            // Datasource
            DigitalReportDataSourceBO datasource = new DigitalReportDataSourceBO();
            assembleDataContext.setOrderNo(order.getOrderNo());
            assembleDataContext.setCategoryList(order.getCategoryList());
            assembleDataContext.setLabSectionList(order.getLabSectionList());
            if (Func.isNotEmpty(context.getLab())) {
                assembleDataContext.setLabId(context.getLab().getLabId());
            }
            // 判断查询testLine的数据维度
            Boolean mergePP = false;
            BaseResponse<String> testLinePrintLevelRes = new BaseResponse();
            if (PrintType.check(assembleDataContext.getPrintType(), PrintType.MASTER_LIST)) {
                testLinePrintLevelRes = buParam.getTestLinePrintLevel(context.getProductLineCode());
                mergePP = testLinePrintLevelRes.isSuccess() && !Func.equalsSafe(testLinePrintLevelRes.getData(),
                        Constants.BU_PARAM.ORDER.TEST_LINE_PRINT_LEVEL.LEVEL.PP);
            }
            assembleDataContext.setMergePP(mergePP);
            assembleDataContext.setSlMaster(context.isSlMaster());

            if (Func.isNotEmpty(order.getReportIdList())) {
                assembleDataContext.setReportIdList(order.getReportIdList());
            }
            BaseResponse<OutPutDataBO> assembleResponse = outputFactory.assembleData(assembleDataContext);
            if (assembleResponse.isFail()) {
                response.setMessage(assembleResponse.getMessage());
                response.setStatus(500);
                return;
            }
            OutPutDataBO printData = assembleResponse.getData();
            if (OutPutType.check(printDataReq.getPrintType(), OutPutType.MASTER_LIST)) {
                // 打印MasterList时需要校验所有有效的testLine都已经创建Job,或者Subcontract
                BaseResponse<Boolean> needCheckTestLine = buParam.needCheckTestLine(context.getProductLineCode());
                if (Func.isNotEmpty(needCheckTestLine.getData()) && needCheckTestLine.getData()) {
                    BaseResponse validateTestLineRes = this.validateTestLine(printData, assembleDataContext);
                    if (validateTestLineRes.isFail()) {
                        response.setMessage(validateTestLineRes.getMessage());
                        response.setStatus(500);
                        return;
                    }
                }
                // Master打印时使用TestLineList中的PP-Sample数据组装Protocol/ Package Name
                this.assemblePackageName(printData);
                if (!context.isSlMaster()) {
                    // HL 打印Master时需要组装PP/TL PA信息
                    this.assemblePAInfo(printData, context);
                }
                // 基于配置 Master 打印要处理testLine的对应的PP数据
                if (testLinePrintLevelRes.isSuccess() && Func.equalsSafe(testLinePrintLevelRes.getData(),
                        Constants.BU_PARAM.ORDER.TEST_LINE_PRINT_LEVEL.LEVEL.PP)) {
                    // 基于JOB, SubContract 关联的TestLine去重PP
                    this.distinctPPTestLine(printData);
                }
                // Master List 打印处理 Job & Subcontract SampleQtySummary
                if (!context.isSlMaster()) {
                    this.assembleSampleQtySummary(printData, context);
                }
            }
            printDataBOList.add(printData);
            datasource.setProductLineCode(context.getProductLineCode());
            datasource.setVariableDataList(printData);
            digitalReportReqList.add(new DigitalReportReq(info, datasource));
        });
        context.setDigitalReportReqList(digitalReportReqList);
        context.setPrintDataBOList(printDataBOList);
        return response;
    }

    private void distinctPPTestLine(OutPutDataBO printData) {
        List<TestLineBO> testLineList = printData.getTestLineList();
        if (Func.isEmpty(testLineList)) {
            return;
        }
        // job 数据处理
        List<JobBO> jobList = printData.getJobList();
        //将一个同一个Job下的同一个PP的PA信息放在一起
        Map<String,List<PriceAttributeBO>> jobPaMap = Maps.newHashMap();
        if (Func.isNotEmpty(jobList)) {
            jobList.stream().forEach(job -> {
                if (Func.isEmpty(job.getRelationship()) || Func.isEmpty(job.getRelationship().getChildren()) || Func.isEmpty(job.getRelationship().getChildren().getTestLineList())) {
                    return;
                }
//                Set<Integer> ppNoSets = Sets.newHashSet();
                job.getRelationship().getChildren().getTestLineList().stream().forEach(testLineRel -> {
                    // 判断当前TestLine
                    Iterator<TestLineBO> testLineIt = testLineList.listIterator();
                    while (testLineIt.hasNext()) {
                        TestLineBO testLineBO = testLineIt.next();
                        if (Func.isEmpty(testLineBO) || Func.isEmpty(testLineBO.getPpTestLineRelList())) {
                            continue;
                        }
                        if (!Func.equalsSafe(testLineBO.getId().getTestLineInstanceId(), testLineRel.getTestLineInstanceId())) {
                            continue;
                        }
                        PPTestLineRelBO ppTestLineRel = testLineBO.getPpTestLineRelList().get(0);
                        if (Func.isEmpty(ppTestLineRel.getId().getPpNo())) {
                            continue;
                        }
                        if(Func.isNotEmpty(ppTestLineRel.getHeader()) && Func.isNotEmpty(ppTestLineRel.getHeader().getPriceAttributeList())){
                            String jobKey = String.format("%s_%s", job.getId().getJobNo(),ppTestLineRel.getId().getPpNo());
                            if(jobPaMap.containsKey(jobKey)){
                                jobPaMap.get(jobKey).addAll(ppTestLineRel.getHeader().getPriceAttributeList());
                            }else {
                                jobPaMap.put(jobKey,ppTestLineRel.getHeader().getPriceAttributeList());
                            }
                        }
                        // 已经存在的数据需求做去重处理
//                        if (ppNoSets.contains(ppTestLineRel.getId().getPpNo())) {
//                            testLineIt.remove();
//                            continue;
//                        }
//                        ppNoSets.add(ppTestLineRel.getId().getPpNo());
                    }
                });
            });
        }
        // subcontract数据处理
        //将一个同一个Job下的同一个PP的PA信息放在一起
        Map<String,List<PriceAttributeBO>> subcontractPaMap = Maps.newHashMap();
        List<SubcontractBO> subcontractList = printData.getSubcontractList();
        if (Func.isNotEmpty(subcontractList)) {
            subcontractList.stream().forEach(subcontract -> {
                if (Func.isEmpty(subcontract.getRelationship()) || Func.isEmpty(subcontract.getRelationship().getChildren()) || Func.isEmpty(subcontract.getRelationship().getChildren().getTestLineList())) {
                    return;
                }
//                Set<Integer> ppNoSets = Sets.newHashSet();
                subcontract.getRelationship().getChildren().getTestLineList().stream().forEach(testLineRel -> {
                    // 判断当前TestLine
                    Iterator<TestLineBO> testLineIt = testLineList.listIterator();
                    while (testLineIt.hasNext()) {
                        TestLineBO testLineBO = testLineIt.next();
                        if (Func.isEmpty(testLineBO) || Func.isEmpty(testLineBO.getPpTestLineRelList())) {
                            continue;
                        }
                        if (!Func.equalsSafe(testLineBO.getId().getTestLineInstanceId(), testLineRel.getTestLineInstanceId())) {
                            continue;
                        }
                        PPTestLineRelBO ppTestLineRel = testLineBO.getPpTestLineRelList().get(0);
                        if (Func.isEmpty(ppTestLineRel.getId().getPpNo())) {
                            continue;
                        }
                        if(Func.isNotEmpty(ppTestLineRel.getHeader()) && Func.isNotEmpty(ppTestLineRel.getHeader().getPriceAttributeList())){
                            String subcontractKey = String.format("%s_%s", subcontract.getId().getSubcontractNo(),ppTestLineRel.getId().getPpNo());
                            if(subcontractPaMap.containsKey(subcontractKey)){
                                subcontractPaMap.get(subcontractKey).addAll(ppTestLineRel.getHeader().getPriceAttributeList());
                            }else {
                                subcontractPaMap.put(subcontractKey,ppTestLineRel.getHeader().getPriceAttributeList());
                            }
                        }
                        // 已经存在的数据需求做去重处理
//                        if (ppNoSets.contains(ppTestLineRel.getId().getPpNo())) {
//                            testLineIt.remove();
//                            continue;
//                        }
//                        ppNoSets.add(ppTestLineRel.getId().getPpNo());
                    }
                });
            });
        }
        // TL去重结束后根据PaMap设置PaList
        testLineList.stream().forEach(testLine -> {
            //
            if(Func.isEmpty(testLine.getPpTestLineRelList())){
                return;
            }
            PPTestLineRelBO ppTestLineRel = testLine.getPpTestLineRelList().get(0);
            if (Func.isEmpty(ppTestLineRel.getId()) || Func.isEmpty(ppTestLineRel.getId().getPpNo())) {
                return;
            }
            Integer ppNo = ppTestLineRel.getId().getPpNo();
            // TL 是属于job
            if(Func.isNotEmpty(jobList)){
                List<JobBO> thisJobList = jobList.stream().filter(job ->
                                Func.isNotEmpty(job.getRelationship()) && Func.isNotEmpty(job.getRelationship().getChildren())
                                        &&Func.isNotEmpty(job.getRelationship().getChildren().getTestLineList())
                        && job.getRelationship().getChildren().getTestLineList().stream().anyMatch(rel -> Func.equalsSafe(testLine.getId().getTestLineInstanceId(), rel.getTestLineInstanceId()))
                        ).collect(Collectors.toList());
                if(Func.isNotEmpty(thisJobList)){
                    List<PriceAttributeBO> jobPaList = Lists.newArrayList();
                    thisJobList.stream().forEach(job -> {
                        String jobKey = String.format("%s_%s", job.getId().getJobNo(),ppNo);
                        if(jobPaMap.containsKey(jobKey)){
                            jobPaList.addAll(jobPaMap.get(jobKey));
                        }
                    });
                    if(Func.isNotEmpty(jobPaList)){
                        ppTestLineRel.getHeader().setPriceAttributeList(jobPaList.stream().collect(Collectors.toMap(
                                PriceAttributeBO::getPriceAttributeName,           // 键提取器：使用姓名作为键
                                Function.identity(),       // 值映射：使用对象本身作为值
                                (existing, replacement) -> replacement // 合并函数：保留最后出现的对象
                        )).values().stream().collect(Collectors.toList()));
                    }
                }
            }
            // TL 是属于分包
            if(Func.isNotEmpty(subcontractList)){
                List<SubcontractBO> thisSubContractList = subcontractList.stream().filter(subcontract ->
                        Func.isNotEmpty(subcontract.getRelationship()) && Func.isNotEmpty(subcontract.getRelationship().getChildren())
                                &&Func.isNotEmpty(subcontract.getRelationship().getChildren().getTestLineList())
                                && subcontract.getRelationship().getChildren().getTestLineList().stream().anyMatch(rel -> Func.equalsSafe(testLine.getId().getTestLineInstanceId(), rel.getTestLineInstanceId()))
                ).collect(Collectors.toList());
                if(Func.isNotEmpty(thisSubContractList)){
                    List<PriceAttributeBO> subContractPaList = Lists.newArrayList();
                    thisSubContractList.stream().forEach(subcontract -> {
                        String subContractKey = String.format("%s_%s", subcontract.getId().getSubcontractNo(),ppNo);
                        if(subcontractPaMap.containsKey(subContractKey)){
                            subContractPaList.addAll(subcontractPaMap.get(subContractKey));
                        }
                    });
                    if(Func.isNotEmpty(subContractPaList)){
                        ppTestLineRel.getHeader().setPriceAttributeList(subContractPaList.stream().collect(Collectors.toMap(
                                PriceAttributeBO::getPriceAttributeName,
                                Function.identity(),
                                (existing, replacement) -> replacement
                        )).values().stream().collect(Collectors.toList()));
                    }
                }
            }
        });
    }

    private void assemblePackageName(OutPutDataBO outPutDataBO) {
        List<TestLineBO> ppTestLineList = outPutDataBO.getTestLineList().stream()
                .filter(tl -> Func.isNotEmpty(tl.getPpTestLineRelList()) && Func.isNotEmpty(tl.getPpTestLineRelList().stream().filter(ppRel -> Func.isNotEmpty(ppRel.getHeader().getPpName())).findAny().orElse(null)))
                .collect(Collectors.toList());
        if (Func.isEmpty(ppTestLineList)) {
            return;
        }
        // 1 获取ppName-testSampleIDList
        HashMap<String, Set<String>> ppSampleIDMap = Maps.newHashMap();
        HashMap<String, Integer> ppMap = Maps.newHashMap();
        ppTestLineList.stream().forEach(pp -> {
            if (Func.isEmpty(pp.getRelationship().getParallel()) || Func.isEmpty(pp.getRelationship().getParallel().getTestSampleList())) {
                return;
            }
            Set<String> testSampleSet = pp.getRelationship().getParallel().getTestSampleList().stream().map(TestLineSampleBO::getTestSampleInstanceId).collect(Collectors.toSet());
            if (Func.isEmpty(testSampleSet)) {
                return;
            }
            pp.getPpTestLineRelList().stream().forEach(ppRel -> {
                String ppName = ppRel.getHeader().getPpName();
                if (Func.isEmpty(ppName)) {
                    return;
                }
                ppMap.put(ppName, ppRel.getId().getPpNo());
                if (ppSampleIDMap.containsKey(ppName)) {
                    ppSampleIDMap.get(ppName).addAll(testSampleSet);
                    return;
                }
                ppSampleIDMap.put(ppName, testSampleSet);
            });
        });
        // 2 基于ppSampleIDMap 转换成pp - testSampleBO
        List<TestSampleBO> testSampleList = outPutDataBO.getTestSampleList();
        HashMap<String, List<TestSampleDTO>> ppSampleMap = Maps.newHashMap();
        if (Func.isEmpty(testSampleList) || Func.isEmpty(ppSampleIDMap)) {
            return;
        }
        ppSampleIDMap.forEach((k, val) -> {
            if (Func.isEmpty(val)) {
                return;
            }
            List<TestSampleDTO> testSampleDTOS = Lists.newArrayList();
            val.stream().forEach(id -> {
                TestSampleBO testSampleBO = testSampleList.stream().filter(ts -> Func.equalsSafe(ts.getId().getTestSampleInstanceId(), id)).findAny()
                        .orElse(null);
                if (Func.isNotEmpty(testSampleBO)) {
                    TestSampleDTO testSampleDTO = new TestSampleDTO();
                    testSampleDTO.setTestSampleNo(testSampleBO.getId().getTestSampleNo());
                    if (Func.isNotEmpty(testSampleBO.getRelationship().getParent().getSourceSampleId())) {
                        String combinedSampleNo = "<span style='background-color:#FFFF00'>" + testSampleBO.getId().getTestSampleNo() + "@</span>";
                        testSampleDTO.setTestSampleNo(combinedSampleNo);
                    }
                    testSampleDTO.setTestSampleSeq(testSampleBO.getHeader().getTestSampleSeq());
                    testSampleDTO.setTestSampleType(testSampleBO.getHeader().getTestSampleType());
                    testSampleDTO.setTestSampleInstanceId(testSampleBO.getId().getTestSampleInstanceId());
                    testSampleDTOS.add(testSampleDTO);
                }
            });
            ppSampleMap.put(k, testSampleDTOS.stream().sorted(Comparator.comparing(TestSampleDTO::getTestSampleType).thenComparing(TestSampleDTO::getTestSampleSeq)).collect(Collectors.toList()));
        });

        // 3 基于转换的数据做排序
        List<PPSampleCombinedDTO> ppSampleCombinedList = Lists.newArrayList();
        ppSampleMap.forEach((k, v) -> {
            PPSampleCombinedDTO ppSampleCombinedDTO = new PPSampleCombinedDTO();
            ppSampleCombinedDTO.setPpName(k);
            ppSampleCombinedDTO.setPpNo(ppMap.get(k));
            ppSampleCombinedDTO.setSequence(v.stream().map(item -> Func.isNotEmpty(item.getTestSampleSeq()) ? item.getTestSampleSeq().toString() : "").collect(Collectors.joining("")));
            ppSampleCombinedDTO.setSampleNos(v.stream().map(TestSampleDTO::getTestSampleNo).collect(Collectors.joining(",")));
            ppSampleCombinedList.add(ppSampleCombinedDTO);
        });

        if (Func.isEmpty(ppSampleCombinedList)) {
            return;
        }
        // 4 组装PPName+SampleNO
        List<String> PPSampleList = Lists.newArrayList();
        ppSampleCombinedList.stream().sorted(Comparator.comparing(PPSampleCombinedDTO::getSequence)
                .thenComparing(PPSampleCombinedDTO::getPpNo)).collect(Collectors.toList())
                .stream().forEach(ppSample -> {
            String item = ppSample.getSampleNos() + ":" + ppSample.getPpName();
            PPSampleList.add(item);
        });
        outPutDataBO.getOrder().getOthers().setCombinedPackageName(PPSampleList.stream().collect(Collectors.joining("</br>")));
    }

    /**
     * 调用Digital执行打印
     *
     * @param context
     * @return
     */
    @Override
    public BaseResponse<String> execute(DataPrintContext context) {
        String productLineCode = context.getProductLineCode();
        String requestParam = JsonUtil.toJson(context.getDigitalReportReqList());
        // SL Master打印使用V1的结构生成
        if (context.isSlMaster()) {
            requestParam = handleSLMasterPrint(context.getDigitalReportReqList());
        }
        // 调用接口记录请求日志
        String finalRequestParam = requestParam;
        context.getParam().getOrderList().forEach(order -> {
            SystemLog requestLog = new SystemLog();
            requestLog.setObjectType(OutPutType.findType(context.getParam().getPrintType()).getCode());
            requestLog.setObjectNo(order.getOrderNo());
            requestLog.setProductLineCode(productLineCode);
            requestLog.setType(SystemLogType.API.getType());
            requestLog.setRemark("调用Digital Report 打印");
            requestLog.setRequest(finalRequestParam);
            requestLog.setOperationType("Digital Report Request");
            requestLog.setLocationCode(Func.isNotEmpty(SystemContextHolder.getLab()) ? SystemContextHolder.getLab().getLocationCode() : "");
            requestLog.setCreateBy("system");
            requestLog.setUrl(context.isSlMaster() ? "/DigitReport-TS/api/ReportAPI/GenerateReporting" : "/DigitReport-TS/api/v3/ReportAPI/GenerateReporting");
            systemLogHelper.save(requestLog);
        });
        return digitalReportClient.print(requestParam, context.getParam().getPrintType(), true, productLineCode);
    }

    /**
     * SL  Master打印需要转换为新的结构
     *
     * @return
     */
    private String handleSLMasterPrint(List<DigitalReportReq> digitalReportReqList) {
        if (Func.isEmpty(digitalReportReqList)) {
            return null;
        }
        List<DigitalReportUltraReq> digitalReportUltraReqList = Lists.newArrayList();
        digitalReportReqList.stream().forEach(item -> {
            DigitalReportUltraReq digitalReportUltraReq = new DigitalReportUltraReq();
            digitalReportUltraReq.setInfo(item.getInfo());
            digitalReportUltraReq.setCallbackSetting(item.getCallbackSetting());
            if (Func.isNotEmpty(item.getDatasource())) {
                DigitalReportDataSourceUltraBO datasource = new DigitalReportDataSourceUltraBO();
                datasource.setProductLineCode(item.getDatasource().getProductLineCode());
                datasource.setVariableDataList(convertService.convertOutPutData(item.getDatasource().getVariableDataList()));
                digitalReportUltraReq.setDatasource(datasource);
            }
            digitalReportUltraReqList.add(digitalReportUltraReq);
        });
        return JsonUtil.toJson(digitalReportUltraReqList);
    }

    @Override
    public BaseResponse after(DataPrintContext context) {

        //记录打印的bizLog
        List<OutPutDataBO> printDataBOList = context.getPrintDataBOList();
        UserInfo userInfo = context.getUserInfo();
        if (Func.isNotEmpty(printDataBOList)) {
            for (OutPutDataBO printDataBO : printDataBOList) {
                String orderNo = printDataBO.getOrder().getHeader().getOrderNo();
                BizLogInfo bizLog = new BizLogInfo();
                bizLog.setBizId(orderNo);
                bizLog.setBu(context.getProductLineCode());
                bizLog.setLab(printDataBO.getOrder().getLab().getLocationCode());
                bizLog.setBizOpType(BizLogConstant.TEST_HISTORY);
                bizLog.setOpType("Print " + OutPutType.findType(context.getParam().getPrintType()).getCode());
                bizLog.setOpUser(Func.isNotEmpty(bizLogClient) ? userInfo.getRegionAccount() : "system");
                bizLog.setNewVal("Print " + OutPutType.findType(context.getParam().getPrintType()).getCode());
                bizLogClient.doSend(bizLog);
            }
        }
        return super.after(context);
    }

    private void assemblePAInfo(OutPutDataBO outPutDataBO, DataPrintContext context) {
        if (Func.isEmpty(outPutDataBO.getTestLineList())) {
            return;
        }
        // 查询PA信息
        GetQuotationInfoReq quotationInfoReq = new GetQuotationInfoReq();
        quotationInfoReq.setOrderNoList(Lists.newArrayList(outPutDataBO.getOrder().getId().getOrderNo()));
        quotationInfoReq.setQuotationStatusList(Lists.newArrayList(QuotationStatus.CONFIRM.getCode(), QuotationStatus.DRAFT.getCode(), QuotationStatus.GENERATE.getCode(), QuotationStatus.CLOSED.getCode()));
        quotationInfoReq.setSgsToken(context.getToken());
        quotationInfoReq.setSystemId(SgsSystem.GPO.getSgsSystemId());
        quotationInfoReq.setProductLineCode(context.getProductLineCode());
        BaseResponse<List<OrderQuotationRsp>> orderQuotationRsp = quotationClient.getQuotationInfoList(quotationInfoReq);
        if (orderQuotationRsp.isFail()) {
            throw new BizException("getQuotationInfoList执行错误：" + orderQuotationRsp.getMessage());
        }
        if (Func.isEmpty(orderQuotationRsp.getData())) {
            return;
        }
        OrderQuotationRsp orderQuotation = orderQuotationRsp.getData().get(0);
        // 获取PA信息
        if (Func.isEmpty(orderQuotation) && Func.isEmpty(orderQuotation.getQuotationList())) {
            return;
        }
        // 待匹配的PA数据集合
        List<QuotationServiceItemInfoRsp> testLinePAList = Lists.newArrayList();
        orderQuotation.getQuotationList().stream().forEach(quotation -> {
            if (Func.isEmpty(quotation.getServiceItemList())) {
                return;
            }
            // 遍历PP下的PA
            quotation.getServiceItemList().stream().forEach(item -> {
                if (Func.isAnyEmpty(item.getPpNo(), item.getPriceAttribute(), item.getTestLineList())) {
                    return;
                }
                testLinePAList.add(item);
            });
        });
        if (Func.isNotEmpty(testLinePAList)) {
            outPutDataBO.getTestLineList().stream().forEach(testLine -> {
                if (Func.isEmpty(testLine.getPpTestLineRelList())) {
                    return;
                }
                Integer testLineId = testLine.getId().getTestLineId();
                testLine.getPpTestLineRelList().stream().forEach(ppRel -> {
                    Integer ppNo = ppRel.getId().getPpNo();
                    Integer aid = ppRel.getId().getAid();
                    if (Func.isAnyEmpty(testLineId, ppNo, aid)) {
                        return;
                    }
                    // 根据PPNo+AID+TestLineID匹配PA数据
                    List<QuotationServiceItemInfoRsp> matchTestLineList = testLinePAList.stream().filter(item -> Func.equalsSafe(item.getPpNo(), ppNo)
                            && Func.isNotEmpty(item.getTestLineList())
                            && item.getTestLineList().stream().anyMatch(tl -> Func.equalsSafe(tl.getTestLineId(), testLineId) && Func.equalsSafe(tl.getAid(), aid))).collect(Collectors.toList());
                    List<PriceAttributeBO> ppPriceAttributeList = assemblePriceAttribute(matchTestLineList);
                    if (Func.isNotEmpty(ppPriceAttributeList)) {
                        ppRel.getHeader().setPriceAttributeList(ppPriceAttributeList);
                    }
                });
            });
        }
    }

    private List<PriceAttributeBO> assemblePriceAttribute(List<QuotationServiceItemInfoRsp> quotationServiceItemList) {
        List<PriceAttributeBO> priceAttributeList = Lists.newArrayList();
        if (Func.isEmpty(quotationServiceItemList)) {
            return priceAttributeList;
        }
        quotationServiceItemList.stream().forEach(quotationServiceItemInfo->{
            PriceAttributeBO priceAttribute = new PriceAttributeBO();
            priceAttribute.setPriceAttributeName(quotationServiceItemInfo.getPriceAttribute());
            if (Func.isNotEmpty(quotationServiceItemInfo.getLanguageList())) {
                List<PriceAttributeLanguageBO> languageList = Lists.newArrayList();
                quotationServiceItemInfo.getLanguageList().stream().forEach(item -> {
                    if (Func.isEmpty(item.getPriceAttribute())) {
                        return;
                    }
                    PriceAttributeLanguageBO priceAttributeLanguage = new PriceAttributeLanguageBO();
                    priceAttributeLanguage.setLanguageId(item.getLanguageId());
                    priceAttributeLanguage.setPriceAttributeName(item.getPriceAttribute());
                    languageList.add(priceAttributeLanguage);
                });
                priceAttribute.setLanguageList(languageList);
            }
            priceAttributeList.add(priceAttribute);
        });

        return priceAttributeList;
    }


    private void assembleSampleQtySummary(OutPutDataBO outPutDataBO, DataPrintContext context) {
        Lab lab = context.getLab();
        List<JobBO> jobBOList = outPutDataBO.getJobList();
        List<SubcontractBO> subcontractBOList = outPutDataBO.getSubcontractList();
        Set<String> objectNoList = new HashSet<>();
        if (Func.isNotEmpty(jobBOList)) {
            objectNoList.addAll(jobBOList.stream().map(jobBO -> jobBO.getId().getJobNo()).collect(Collectors.toSet()));
        }
        if (Func.isNotEmpty(subcontractBOList)) {
            objectNoList.addAll(subcontractBOList.stream().map(subcontractBO -> subcontractBO.getId().getSubcontractNo()).collect(Collectors.toSet()));
        }
        ObjectSampleQuantityQueryReq objectSampleQuantitySaveReq = new ObjectSampleQuantityQueryReq();
        objectSampleQuantitySaveReq.setObjectNoList(objectNoList);
        List<ObjectSampleQuantityPO> objectSampleQuantityPOList = objectSampleQuantityService.select(objectSampleQuantitySaveReq);
        //遍历每个Job Subcontract 查询QTY
        if (Func.isNotEmpty(jobBOList)) {
            jobBOList.stream().forEach(jobBO -> {
                if (Func.isNotEmpty(objectSampleQuantityPOList)) {
                    List<ObjectSampleQuantityPO> currentSampleQuantityList = objectSampleQuantityPOList.stream().filter(e -> Func.equalsSafe(e.getObjectNo(), jobBO.getId().getJobNo())).collect(Collectors.toList());
                    if (Func.isNotEmpty(currentSampleQuantityList)) {
                        jobBO.getHeader().setSampleQtySummary(summarySampleQty(currentSampleQuantityList));
                    }
                }
            });
        }
        if (Func.isNotEmpty(subcontractBOList)) {
            subcontractBOList.stream().forEach(subcontractBO -> {
                if (Func.isNotEmpty(objectSampleQuantityPOList)) {
                    List<ObjectSampleQuantityPO> currentSampleQuantityList = objectSampleQuantityPOList.stream().filter(e -> Func.equalsSafe(e.getObjectNo(), subcontractBO.getId().getSubcontractNo())).collect(Collectors.toList());
                    if (Func.isNotEmpty(currentSampleQuantityList)) {
                        subcontractBO.getHeader().setSampleQtySummary(summarySampleQty(currentSampleQuantityList));
                    }

                }
            });
        }
    }

    private String summarySampleQty(List<ObjectSampleQuantityPO> currentSampleQuantityList) {
        StringBuilder summary = new StringBuilder();
        boolean add = false;
        if (Func.isNotEmpty(currentSampleQuantityList)) {
            for (ObjectSampleQuantityPO record : currentSampleQuantityList) {
                if (add) {
                    summary.append(" + ");
                }
                add = true;
                summary.append(record.getStylesQty() + "Style x " + record.getPiecesQty() + "Pcs");
                if (Func.isNotEmpty(record.getRemark())) {
                    summary.append(" +" + record.getRemark());
                }
            }
        }
        return summary.toString();
    }
}
