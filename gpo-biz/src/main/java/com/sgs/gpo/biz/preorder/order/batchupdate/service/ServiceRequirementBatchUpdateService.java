package com.sgs.gpo.biz.preorder.order.batchupdate.service;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.framework.model.order.v2.OrderBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.biz.preorder.order.batchupdate.context.OrderBatchUpdateContext;
import com.sgs.gpo.core.constants.BizLogConstant;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.testrequest.TestRequestContactPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.testrequest.TestRequestPO;
import com.sgs.gpo.domain.service.preorder.testrequest.ITestRequestContactService;
import com.sgs.gpo.domain.service.preorder.testrequest.ITestRequestService;
import com.sgs.gpo.facade.model.preorder.order.req.OrderBatchUpdateReq;
import com.sgs.grus.bizlog.BizLogClient;
import com.sgs.grus.bizlog.info.BizLogInfo;
import com.sgs.otsnotes.facade.model.enums.ReportRequirementEnum;
import com.sgs.preorder.facade.model.enums.ContactsType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service("serviceRequirementBatchUpdateService")
public class ServiceRequirementBatchUpdateService extends OrderBatchUpdateBaseService {
    @Autowired
    private ITestRequestService testRequestService;
    @Autowired
    private ITestRequestContactService testRequestContactService;
    @Autowired
    private BizLogClient bizLogClient;

    @Override
    @Transactional
    protected BaseResponse<Boolean> update(OrderBatchUpdateContext context) {
        OrderBatchUpdateReq orderBatchUpdateReq = context.getParam();
        List<GeneralOrderPO> targetOrderList = context.getTargetOrderList();
        TestRequestPO orgTestRequest = context.getOrgTestRequestPO();
        List<BizLogInfo> bizLogList = new ArrayList<>();
        Set<String> needUpdateReportAccreditationList = new HashSet<>();
        GeneralOrderPO orgOrder = context.getOrgOrder();
        List<TestRequestPO> targetTestRequestPOList = context.getTargetTestRequestPOList();
        List<OrderBO> allOrderBOList = context.getAllOrderBOList();
        if (Func.isEmpty(targetTestRequestPOList)) {
            targetTestRequestPOList = new ArrayList<>();
        }
        if (Func.isEmpty(orderBatchUpdateReq) || Func.isEmpty(orderBatchUpdateReq.getUpdateSection())) {
            return BaseResponse.newSuccessInstance(true);
        }
        List<OrderBatchUpdateReq.BatchUpdateSectionReq> updateSectionList = orderBatchUpdateReq.getUpdateSection();
        OrderBatchUpdateReq.BatchUpdateSectionReq testRequest = updateSectionList.stream().filter(item -> Func.equalsSafe(item.getAttributeCode(), "serviceRequirement")).findAny().orElse(null);
        if (Func.isEmpty(testRequest) || Func.isEmpty(testRequest.getSubSectionReqList())) {
            return BaseResponse.newSuccessInstance(true);
        }

        Set<String> subAttributeCodeList = testRequest.getSubSectionReqList().stream().map(OrderBatchUpdateReq.BatchUpdateSubSectionReq::getAttributeCode).collect(Collectors.toSet());
        List<TestRequestContactPO> saveOrUpdateTestRequestContactList = new ArrayList<>();
        List<TestRequestPO> updateTestRequestList = new ArrayList<>();
        for (GeneralOrderPO generalOrderPO : targetOrderList) {

            TestRequestPO targetTestRequest = targetTestRequestPOList.stream().filter(item -> Func.equalsSafe(item.getGeneralOrderId(), generalOrderPO.getId())).findAny().orElse(null);
            if (Func.isNotEmpty(targetTestRequest)) {
                TestRequestPO targetTestRequestPO = targetTestRequestPOList.stream().filter(item -> Func.equalsSafe(item.getGeneralOrderId(), generalOrderPO.getId())).findAny().orElse(null);
                for (String subAttributeCode : subAttributeCodeList) {
                    TestRequestContactPO testRequestContactPO = null;
                    if (StringUtils.equalsIgnoreCase(subAttributeCode, "softCopyDeliverTo")) {
                        testRequestContactPO = this.buildContact(generalOrderPO,context, ContactsType.SoftCopy);
                    } else if (StringUtils.equalsIgnoreCase(subAttributeCode, "softCopyDeliverCC")) {
                        testRequestContactPO = this.buildContact(generalOrderPO,context, ContactsType.SoftCopyDeliveryCc);
                    } else if (StringUtils.equalsIgnoreCase(subAttributeCode, "returnSampleTo")) {
                        testRequestContactPO = this.buildContact(generalOrderPO,context, ContactsType.ReturnSample);
                    } else if (StringUtils.equalsIgnoreCase(subAttributeCode, "returnSampleWay")
                            || StringUtils.equalsIgnoreCase(subAttributeCode, "invoiceDeliverWay")
                            || StringUtils.equalsIgnoreCase(subAttributeCode, "reportRequirement")
                            || StringUtils.equalsIgnoreCase(subAttributeCode, "qualificationType")) {
                        if(Func.isNotEmpty(orgTestRequest)){
                            if (Func.isNotEmpty(targetTestRequestPOList)) {
                                if (Func.isNotEmpty(targetTestRequestPO)) {
                                    if(StringUtils.equalsIgnoreCase(subAttributeCode, "returnSampleWay")){
                                        if (Func.isNotEmpty(orgTestRequest.getReturnTestedSampleRemark())) {
                                            targetTestRequestPO.setReturnTestedSampleRemark(orgTestRequest.getReturnTestedSampleRemark());
                                        }else{
                                            targetTestRequestPO.setReturnTestedSampleRemark("");
                                        }
                                        if (Func.isNotEmpty(orgTestRequest.getReturnResidueSampleRemark())) {
                                            targetTestRequestPO.setReturnResidueSampleRemark(orgTestRequest.getReturnResidueSampleRemark());
                                        }else{
                                            targetTestRequestPO.setReturnResidueSampleRemark("");
                                        }
                                        targetTestRequestPO.setReturnTestedSampleFlag(Func.toInteger(orgTestRequest.getReturnTestedSampleFlag(),0));
                                        targetTestRequestPO.setReturnResidueSampleFlag(Func.toInteger(orgTestRequest.getReturnResidueSampleFlag(),0));
                                    }
                                    if (StringUtils.equalsIgnoreCase(subAttributeCode, "invoiceDeliverWay")) {
                                        targetTestRequestPO.setInvoiceDeliverWay(orgTestRequest.getInvoiceDeliverWay());
                                    }
                                    if (StringUtils.equalsIgnoreCase(subAttributeCode, "reportRequirement")) {
                                        targetTestRequestPO.setReportRequirement(orgTestRequest.getReportRequirement());
                                        if (ProductLineContextHolder.getProductLineCode().equalsIgnoreCase(ProductLineType.HL.getProductLineAbbr())
                                                && ReportRequirementEnum.check(orgTestRequest.getReportRequirement(),ReportRequirementEnum.Customer_Report_Word,ReportRequirementEnum.Sub_Report_Word)) {
                                            targetTestRequestPO.setDraftReportRequired(0);
                                        }
                                    }
                                    if (StringUtils.equalsIgnoreCase(subAttributeCode, "qualificationType")) {
                                        //targetTestRequestPO.setQualification(orgTestRequest.getQualificationType());
                                        String orgQualificationType =  Func.toStr(orgTestRequest.getQualificationType());
                                        String targetQualificationType = Func.toStr(targetTestRequestPO.getQualificationType());
                                        targetTestRequestPO.setQualificationType(orgTestRequest.getQualificationType());
                                        List<String> orgQualificationTypeList = Func.toStrList(orgQualificationType);
                                        List<String> targetQualificationTypeList = Func.toStrList(targetQualificationType);
                                        orgQualificationTypeList.sort(String::compareTo);
                                        targetQualificationTypeList.sort(String::compareTo);
                                        orgQualificationType = Func.join(orgQualificationTypeList);
                                        targetQualificationType = Func.join(targetQualificationTypeList);
                                        // 只有当排序后的列表不相等时，才添加到needUpdateReportAccreditationList中
                                        log.info("Qualification change,old:{},target:{}",Func.toJson(orgQualificationTypeList),Func.toJson(targetQualificationType));
                                        if (!Func.equalsSafe(orgQualificationType,targetQualificationType)) {
                                            needUpdateReportAccreditationList.add(targetTestRequestPO.getGeneralOrderId());
                                        }
                                    }
                                    targetTestRequestPO.setModifiedDate(context.getNewDate());
                                    targetTestRequestPO.setModifiedBy(context.getUserInfo().getRegionAccount());
                                    updateTestRequestList.add(targetTestRequestPO);
                                }
                            }
                        }else{
                            if (Func.isNotEmpty(targetTestRequestPOList)) {
                                if(StringUtils.equalsIgnoreCase(subAttributeCode, "returnSampleWay")){
                                    targetTestRequestPO.setReturnTestedSampleRemark("");
                                    targetTestRequestPO.setReturnResidueSampleRemark("");
                                    targetTestRequestPO.setReturnTestedSampleFlag(0);
                                    targetTestRequestPO.setReturnResidueSampleFlag(0);
                                }
                                if(StringUtils.equalsIgnoreCase(subAttributeCode, "qualificationType")){
                                    targetTestRequestPO.setQualification("");
                                }
                                targetTestRequestPO.setModifiedDate(context.getNewDate());
                                targetTestRequestPO.setModifiedBy(context.getUserInfo().getRegionAccount());
                                updateTestRequestList.add(targetTestRequestPO);
                            }
                        }
                    } else if (StringUtils.equalsIgnoreCase(subAttributeCode, "prelimReport")) {
                        testRequestContactPO = this.buildContact(generalOrderPO,context, ContactsType.PrelimReport);
                    } else if (StringUtils.equalsIgnoreCase(subAttributeCode, "prelimReportCC")) {
                        testRequestContactPO = this.buildContact(generalOrderPO,context, ContactsType.PrelimReportCC);
                    } else if (StringUtils.equalsIgnoreCase(subAttributeCode, "invoiceDeliverTo")){
                        testRequestContactPO = this.buildContact(generalOrderPO,context, ContactsType.Invoice);
                    }
                    if (Func.isNotEmpty(testRequestContactPO)) {
                        saveOrUpdateTestRequestContactList.add(testRequestContactPO);
                    }
                }
            }
            OrderBO orderBO = allOrderBOList.stream().filter(item -> Func.equalsSafe(item.getId().getOrderNo(), generalOrderPO.getOrderNo())).findAny().orElse(null);
            if(Func.isNotEmpty(orderBO)){
                //记录BizLog
                BizLogInfo bizLog = new BizLogInfo();
                bizLog.setBizId(orderBO.getId().getOrderNo());
                bizLog.setBu(orderBO.getLab().getBuCode());
                bizLog.setLab(orderBO.getLab().getLocationCode());
                bizLog.setBizOpType(BizLogConstant.ORDER_OPERATION_HISTORY);
                bizLog.setOpType("Batch Update");
                bizLog.setOpUser(context.getUserInfo().getRegionAccount());
                String bizNewVal = "Batch update Service Requirement From OrderNo:" + orgOrder.getOrderNo();
                bizLog.setNewVal(bizNewVal);
                bizLogList.add(bizLog);
            }

        }
        if (Func.isNotEmpty(saveOrUpdateTestRequestContactList)) {
            testRequestContactService.saveOrUpdateBatch(saveOrUpdateTestRequestContactList);
        }
        if (Func.isNotEmpty(updateTestRequestList)) {
            testRequestService.updateBatchById(updateTestRequestList);
        }
        if(Func.isNotEmpty(bizLogList)){
            bizLogList.stream().forEach(bizLogInfo -> {
                bizLogClient.doSend(bizLogInfo);
            });
        }
        context.setNeedUpdateReportAccreditationList(needUpdateReportAccreditationList);
        return BaseResponse.newSuccessInstance(true);
    }

    public TestRequestContactPO buildContact(GeneralOrderPO generalOrderPO,OrderBatchUpdateContext context, ContactsType contactsType) {
        TestRequestContactPO newTestRequestContact = null;
        List<TestRequestContactPO> targetTestRequestContactPOList = context.getTargetTestRequestContactPOList();
        if (Func.isEmpty(targetTestRequestContactPOList)) {
            return null;
        }
        List<TestRequestContactPO> targetTestRequestContactList = targetTestRequestContactPOList.stream().filter(item -> Func.equalsSafe(item.getOrderId(), generalOrderPO.getId())).collect(Collectors.toList());
        Date newDate = context.getNewDate();
        String regionAccount = context.getUserInfo().getRegionAccount();
        List<TestRequestContactPO> orgTestRequestContactPOList = context.getOrgTestRequestContactPOList();
        if (Func.isNotEmpty(orgTestRequestContactPOList)) {
            TestRequestContactPO orgTestRequestContactPO = orgTestRequestContactPOList.stream().filter(item -> ContactsType.check(item.getContactsType(), contactsType)).findAny().orElse(null);
            TestRequestContactPO targetTestRequestContactPO = targetTestRequestContactList.stream().filter(item -> ContactsType.check(item.getContactsType(),contactsType)).findAny().orElse(null);
            if (Func.isNotEmpty(orgTestRequestContactPO)) {
                newTestRequestContact = new TestRequestContactPO();
                Func.copy(orgTestRequestContactPO, newTestRequestContact);
                newTestRequestContact.setOrderId(generalOrderPO.getId());
                newTestRequestContact.setModifiedDate(newDate);
                newTestRequestContact.setModifiedBy(regionAccount);
                if (Func.isEmpty(targetTestRequestContactPO)) {
                    newTestRequestContact.setId(IdUtil.uuId());
                    newTestRequestContact.setCreatedDate(newDate);
                    newTestRequestContact.setCreatedBy(regionAccount);
                }else{
                    newTestRequestContact.setId(targetTestRequestContactPO.getId());
                }
            }else{
                if (Func.isNotEmpty(targetTestRequestContactPO)) {
                    newTestRequestContact = new TestRequestContactPO();
                    Func.copy(targetTestRequestContactPO, newTestRequestContact);
                    newTestRequestContact.setDeliverTo("");
                    newTestRequestContact.setDeliverToOthers("");
                    newTestRequestContact.setModifiedDate(newDate);
                    newTestRequestContact.setModifiedBy(regionAccount);
                }
            }
        }
        return newTestRequestContact;
    }

}
