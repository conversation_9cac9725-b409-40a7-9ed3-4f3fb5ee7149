package com.sgs.gpo.biz.otsnotes.dataentry.command;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.model.Lab;
import com.sgs.framework.model.enums.ReportEntryModeEnum;
import com.sgs.framework.model.order.v2.OrderBO;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.biz.otsnotes.dataentry.context.TLDataEntryContext;
import com.sgs.gpo.core.common.SystemCache;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.enums.ReportStatus;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subcontract.SubcontractPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subcontract.SubcontractTestLinePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testdata.TestDataHeaderPO;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportMatrixRelService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportService;
import com.sgs.gpo.domain.service.otsnotes.subcontract.subdomain.ISubcontractService;
import com.sgs.gpo.domain.service.otsnotes.subcontract.subdomain.ISubcontractTestLineService;
import com.sgs.gpo.domain.service.otsnotes.testdata.ITestDataHeaderService;
import com.sgs.gpo.domain.service.otsnotes.testline.ITestLineDomainService;
import com.sgs.gpo.domain.service.preorder.order.IOrderDomainService;
import com.sgs.gpo.facade.model.otsnotes.testdata.req.TLDataHeaderSaveReq;
import com.sgs.gpo.facade.model.otsnotes.testdata.rsp.TLDataHeaderSaveCheckRsp;
import com.sgs.gpo.facade.model.otsnotes.testdata.rsp.TLDataHeaderSaveRsp;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.gpo.facade.model.report.bo.ReportMatrixRelBO;
import com.sgs.gpo.facade.model.report.req.ReportMatrixQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportTestResultStatusUpdateReq;
import com.sgs.gpo.facade.model.subcontract.req.SubcontractQueryReq;
import com.sgs.gpo.facade.model.subcontract.req.SubcontractTestLineQueryReq;
import com.sgs.gpo.integration.dataentry.req.SaveTestHeaderReq;
import com.sgs.otsnotes.facade.model.enums.SubContractStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;
@Primary
@Service("TLDataEntrySaveBizCMD")
@Slf4j
public class TLDataEntrySaveBizCMD extends BaseCommand<TLDataEntryContext<TLDataHeaderSaveReq>> {
    @Autowired
    private IOrderDomainService orderDomainService;
    @Autowired
    private ITestDataHeaderService testDataHeaderService;
    @Autowired
    private ITestLineDomainService testLineDomainService;
    @Autowired
    private IReportMatrixRelService reportMatrixRelService;
    @Autowired
    private IReportService reportService;
    @Autowired
    private ISubcontractTestLineService subcontractTestLineService;
    @Autowired
    private ISubcontractService subcontractService;


    @Override
    public BaseResponse validateParam(TLDataEntryContext<TLDataHeaderSaveReq> context) {
        if (Func.isEmpty(context) || Func.isEmpty(context.getParam())) {
            log.error("TLDataHeaderSave fail: context isEmpty");
            return BaseResponse.newFailInstance("common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
        }
        TLDataHeaderSaveReq param = context.getParam();
        log.info("save Test Data Header req Date:{}",Func.toJson(new Date()));
        log.info("save Test Data Header req:{}",Func.toJson(param));
        if (Func.isAllEmpty(param.getOrderNo(),param.getReportIdList(),param.getReportMatrixItemReqList())) {
            return BaseResponse.newFailInstance("common.param.miss", new Object[]{"OrderNo/ReportId/ReportMatrix"});
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse buildDomain(TLDataEntryContext<TLDataHeaderSaveReq> context) {
        TLDataHeaderSaveReq tlDataHeaderSaveReq = context.getParam();
        List<OrderBO> orderBOList = context.getOrderBOList();
        Set<String> reportIdList = tlDataHeaderSaveReq.getReportIdList();
        List<TLDataHeaderSaveReq.ReportMatrixItemReq> reportMatrixItemReqList = tlDataHeaderSaveReq.getReportMatrixItemReqList();
        if(Func.isNotEmpty(reportMatrixItemReqList)){
            reportIdList = reportMatrixItemReqList.stream().map(TLDataHeaderSaveReq.ReportMatrixItemReq::getReportId).collect(Collectors.toSet());
        }
        List<TestDataHeaderPO> saveTestDataHeaderList = new ArrayList<>();
        List<ReportPO> reportPOList = context.getReportPOList();
        List<ReportMatrixRelBO> reportMatrixRelBOList = context.getReportMatrixRelBOList();
        List<TestLineBO> testLineBOList = context.getTestLineBOList();
        if (Func.isNoneEmpty(orderBOList,reportIdList)) {
//            Map<String, List<ReportMatrixRelBO>> groupByReportMatrix = reportMatrixRelBOList.stream().collect(Collectors.groupingBy(ReportMatrixRelBO::getReportNo));
            for (String reportId : reportIdList) {
                ReportPO reportPO = reportPOList.stream().filter(item -> Func.equalsSafe(item.getId(), reportId)).findAny().orElse(null);
                if(Func.isNotEmpty(reportPO)){
                    String reportNo = reportPO.getReportNo();
                    OrderBO orderBO = orderBOList.stream().filter(item -> Func.equalsSafe(item.getId().getOrderNo(), reportPO.getOrderNo())).findAny().orElse(null);
                    JSONArray jsonArray = new JSONArray();
                    TestDataHeaderPO testDataHeaderPO = new TestDataHeaderPO();
                    testDataHeaderPO.setOrderId(orderBO.getId().getOrderId());
                    testDataHeaderPO.setOrderNo(orderBO.getId().getOrderNo());
                    testDataHeaderPO.setReportId(reportId);
                    testDataHeaderPO.setReportNo(reportNo);
                    if(Func.isNotEmpty(reportMatrixRelBOList)){
                        List<ReportMatrixRelBO> reportMatrixRelBOS = reportMatrixRelBOList.stream().filter(item->Func.equalsSafe(item.getReportId(),reportId)).collect(Collectors.toList());
                        if(Func.isNotEmpty(reportMatrixRelBOS)){
                            reportMatrixRelBOS = reportMatrixRelBOS.stream().filter(item->Func.isNotEmpty(item.getTestLineInstanceId())).collect(
                                    Collectors.collectingAndThen(Collectors.toCollection(
                                            () -> new TreeSet<>(Comparator.comparing(p -> p.getTestLineInstanceId()))), ArrayList::new)
                            );
                            for (ReportMatrixRelBO reportMatrixRelBO : reportMatrixRelBOS) {
                                JSONObject dimension = new JSONObject();
                                String testLineInstanceId = reportMatrixRelBO.getTestLineInstanceId();
                                if(Func.isNotEmpty(testLineBOList)){
                                    TestLineBO testLineBO = testLineBOList.stream().filter(item -> Func.equalsSafe(item.getTestLineInstanceId(), testLineInstanceId)).findAny().orElse(null);
                                    if(Func.isNotEmpty(testLineBO)){
                                        dimension.put("testItemNo", testLineBO.getTestItemNo());
                                    }
                                }
                                dimension.put("testLineInstanceId", testLineInstanceId);
                                jsonArray.add(dimension);
                            }
                            testDataHeaderPO.setDimension(Func.toJson(jsonArray));
                        }
                    }
                    saveTestDataHeaderList.add(testDataHeaderPO);
                }
            }
        }
        List<SaveTestHeaderReq> saveTestHeaderReqList = new ArrayList<>();
        if (Func.isNoneEmpty(saveTestDataHeaderList)) {
            for (TestDataHeaderPO testDataHeaderPO : saveTestDataHeaderList) {
                SaveTestHeaderReq saveTestHeaderReq = new SaveTestHeaderReq();
                saveTestHeaderReq.setOrderId(testDataHeaderPO.getOrderId());
                saveTestHeaderReq.setOrderNo(testDataHeaderPO.getOrderNo());
                saveTestHeaderReq.setReportId(testDataHeaderPO.getReportId());
                saveTestHeaderReq.setReportNo(testDataHeaderPO.getReportNo());
                saveTestHeaderReq.setDimension(testDataHeaderPO.getDimension());
                saveTestHeaderReqList.add(saveTestHeaderReq);
            }
        }
        context.setSaveTestHeaderReqList(saveTestHeaderReqList);
        return super.buildDomain(context);
    }

    @Override
    public BaseResponse execute(TLDataEntryContext<TLDataHeaderSaveReq> context) {
        BaseResponse baseResponse = new BaseResponse();
        TLDataHeaderSaveRsp tlDataHeaderSaveRsp = new TLDataHeaderSaveRsp();
        List<OrderBO> orderBOList = context.getOrderBOList();
        TLDataHeaderSaveReq tlDataHeaderSaveReq = context.getParam();
        boolean needDueDateConfirm = tlDataHeaderSaveReq.isNeedDueDateConfirm();
        if(Func.isNotEmpty(orderBOList) && needDueDateConfirm){
            Integer dueDateConfirmFlag = orderBOList.get(0).getHeader().getDueDateConfirmFlag();
            if(!Func.equalsSafe(dueDateConfirmFlag,1)){
                return BaseResponse.newSuccessInstance("未ConfirmDueDate,暂不处理");
            }
        }
        this.buildDomain(context);
        List<SaveTestHeaderReq> saveTestHeaderReqList = context.getSaveTestHeaderReqList();
        if(tlDataHeaderSaveReq.isCheckFlag()){
            BaseResponse<List<TLDataHeaderSaveCheckRsp>> checkResult = testDataHeaderService.saveCheckTestDataHeader(saveTestHeaderReqList);
            if(checkResult.isFail()){
                List<TLDataHeaderSaveCheckRsp> tlDataHeaderSaveCheckRsps = checkResult.getData();
                tlDataHeaderSaveRsp.setCheckResultList(tlDataHeaderSaveCheckRsps);
                baseResponse.setData(tlDataHeaderSaveRsp);
                baseResponse.setMessage(checkResult.getMessage());
                baseResponse.setStatus(checkResult.getStatus());
                baseResponse.setRespCode(checkResult.getRespCode());
                return baseResponse;
            }
        }
        return testDataHeaderService.saveTestDataHeader(saveTestHeaderReqList);
    }

    @Override
    public BaseResponse before(TLDataEntryContext<TLDataHeaderSaveReq> context) {
        TLDataHeaderSaveReq param = context.getParam();
        Set<String> reportIdList = param.getReportIdList();
        boolean excludeSubContractTestLine = param.isExcludeSubContractTestLine();
        boolean checkOneReport = param.isCheckOneReport();
        String orderNo = param.getOrderNo();

        //以Order的维度触发
        //当请求参数中的OrderNo不为空，ReportIdList为空，则根据OrderNo查询出有效的Report
        if(Func.isNotEmpty(orderNo) && Func.isEmpty(reportIdList)){
            OrderIdReq orderQueryReq = new OrderIdReq();
            orderQueryReq.setOrderNoList(Sets.newHashSet(orderNo));
            BaseResponse<List<ReportPO>> reportListRes = reportService.queryReportByOrderNo(orderQueryReq);
            if(Func.isEmpty(reportListRes) || reportListRes.isFail() || Func.isEmpty(reportListRes.getData())){
                return BaseResponse.newFailInstance("common.param.invalid",new Object[]{Constants.TERM.REQUEST.getCode()});
            }
            List<ReportPO> reportPOList = reportListRes.getData();
            reportIdList = reportPOList.stream().filter(item->!ReportStatus.checkCategory(item.getReportStatus(), Constants.OBJECT.REPORT.STATUS_CATEGORY.INACTIVE)).map(ReportPO::getId).collect(Collectors.toSet());
            if(Func.isEmpty(reportIdList)){
                return BaseResponse.newFailInstance("common.records.empty",new Object[]{Constants.OBJECT.REPORT.OBJECT_CODE});
            }
            param.setReportIdList(reportIdList);
            if(checkOneReport && reportIdList.size() > 1){
                return BaseResponse.newSuccessInstance(true);
            }
        }
        if(excludeSubContractTestLine && Func.isEmpty(param.getSubcontractIdList())){
            SubcontractQueryReq  subcontractQueryReq = new SubcontractQueryReq();
            subcontractQueryReq.setOrderNoList(Sets.newHashSet(orderNo));
            BaseResponse<List<SubcontractPO>> subcontractListRes = subcontractService.select(subcontractQueryReq);
            if(subcontractListRes.isSuccess() && Func.isNotEmpty(subcontractListRes.getData())){
                log.info("根据OrderNo查询Subcontract,OrderNo:{},SubcontractList:{}",orderNo,subcontractListRes.getData());
                Set<String> subContractIdList = subcontractListRes.getData().stream().filter(item -> SubContractStatusEnum.check(item.getStatus(), SubContractStatusEnum.Complete)).map(SubcontractPO::getId).collect(Collectors.toSet());
                param.setSubcontractIdList(subContractIdList);
            }
        }

        List<TLDataHeaderSaveReq.ReportMatrixItemReq> reportMatrixItemReqList = param.getReportMatrixItemReqList();
        List<ReportMatrixRelBO> reportMatrixRelBOList = new ArrayList<>();
        if(Func.isNotEmpty(reportMatrixItemReqList)){
            reportIdList = reportMatrixItemReqList.stream().map(TLDataHeaderSaveReq.ReportMatrixItemReq::getReportId).collect(Collectors.toSet());
            for (TLDataHeaderSaveReq.ReportMatrixItemReq reportMatrixItemReq : reportMatrixItemReqList) {
                ReportMatrixRelBO reportMatrixRelBO = new ReportMatrixRelBO();
                reportMatrixRelBO.setReportId(reportMatrixItemReq.getReportId());
                reportMatrixRelBO.setTestLineInstanceId(reportMatrixItemReq.getTestLineInstanceId());
                reportMatrixRelBOList.add(reportMatrixRelBO);
            }
        }
        ReportQueryReq reportQueryReq = new ReportQueryReq();
        reportQueryReq.setReportIdList(reportIdList);
        List<ReportPO> reportPOList = reportService.select(reportQueryReq).getData();
        context.setReportPOList(reportPOList);
        if(Func.isEmpty(reportMatrixItemReqList)){
            ReportMatrixQueryReq reportMatrixQueryReq = new ReportMatrixQueryReq();
            reportMatrixQueryReq.setReportIdList(reportIdList);
            reportMatrixRelBOList = reportMatrixRelService.queryReportMatrix(reportMatrixQueryReq).getData();
        }
        //是否排除分包出去的TL
        Set<String> subContractTestLineInstanceIdList = new HashSet<>();
        if(excludeSubContractTestLine && Func.isNotEmpty(param.getSubcontractIdList())){
            SubcontractTestLineQueryReq subcontractTestLineQueryReq = new SubcontractTestLineQueryReq();
            subcontractTestLineQueryReq.setSubcontractIds(param.getSubcontractIdList());
            List<SubcontractTestLinePO> subcontractTestLinePOList = subcontractTestLineService.query(subcontractTestLineQueryReq).getData();
            if(Func.isNotEmpty(subcontractTestLinePOList)){
                subContractTestLineInstanceIdList = subcontractTestLinePOList.stream().map(SubcontractTestLinePO::getTestLineInstanceId).collect(Collectors.toSet());
            }
        }
        if(Func.isNotEmpty(reportMatrixRelBOList)){
            Set<String> finalSubContractTestLineInstanceIdList = subContractTestLineInstanceIdList;
            reportMatrixRelBOList = reportMatrixRelBOList.stream().filter(item->!finalSubContractTestLineInstanceIdList.contains(item.getTestLineInstanceId())).collect(Collectors.toList());
        }
        context.setReportMatrixRelBOList(reportMatrixRelBOList);
        if(Func.isNotEmpty(reportPOList)){
            Set<String> orderNoList = reportPOList.stream().map(ReportPO::getOrderNo).collect(Collectors.toSet());
            OrderQueryReq orderQueryReq = new OrderQueryReq();
            orderQueryReq.setOrderNoList(orderNoList);
            List<OrderBO> orderBOList = orderDomainService.queryBO(orderQueryReq).getData();
            if (Func.isEmpty(orderBOList)) {
                return BaseResponse.newFailInstance("order.result.empty", null);
            }
            String labCode = orderBOList.get(0).getLab().getLabCode();
            if(Func.isEmpty(SystemContextHolder.getLabCode()) && Func.isNotEmpty(labCode)){
                Lab currentLab = SystemCache.labCache.get(labCode);
                if(Func.isNotEmpty(currentLab)){
                    SystemContextHolder.getContext().setLab(currentLab);
                }
            }
            context.setOrderBOList(orderBOList);
        }
        if(Func.isNotEmpty(reportMatrixRelBOList)){
            Set<String> testLineInstanceIdList = reportMatrixRelBOList.stream().map(ReportMatrixRelBO::getTestLineInstanceId).filter(Func::isNotEmpty).collect(Collectors.toSet());
            if(Func.isNotEmpty(testLineInstanceIdList)){
                OrderTestLineReq orderTestLineReq = new OrderTestLineReq();
                orderTestLineReq.setTestLineInstanceIdList(testLineInstanceIdList);
                List<TestLineBO> testLineBOList = testLineDomainService.queryTestLine(orderTestLineReq).getData();
                context.setTestLineBOList(testLineBOList);
            }
        }
        return super.before(context);
    }

    @Override
    public BaseResponse after(TLDataEntryContext<TLDataHeaderSaveReq> context) {
        try {
            List<ReportPO> reportPOList = context.getReportPOList();
            String orderNo = context.getParam().getOrderNo();
            if(Func.isNotEmpty(reportPOList)){
                Set<String> reportIdList = reportPOList.stream().map(ReportPO::getId).collect(Collectors.toSet());
                ReportTestResultStatusUpdateReq reportTestResultStatusUpdateReq = new ReportTestResultStatusUpdateReq();
                reportTestResultStatusUpdateReq.setOrderNo(orderNo);
                reportTestResultStatusUpdateReq.setReportIdList(reportIdList);
                reportTestResultStatusUpdateReq.setEntryMode(ReportEntryModeEnum.REPORT_TEST_LINE.getCode());
                reportTestResultStatusUpdateReq.setToken(context.getToken());
                reportService.updateReportTestResultStatus(reportTestResultStatusUpdateReq);
            }
        } catch (Exception e) {
            log.error("after TlReport Save,call updateReportTestResultStatus error:{}",e);
        }
        return BaseResponse.newSuccessInstance(true);
    }
}
