<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.report.ReportMapper">
    <resultMap id="BaseResultMap" type="com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO" >
        <id column="ID" property="id" jdbcType="VARCHAR" />
        <result column="OrderNo" property="orderNo" jdbcType="VARCHAR" />
        <result column="ReportNo" property="reportNo" jdbcType="VARCHAR" />
        <result column="ParentReportNo" property="parentReportNo" jdbcType="VARCHAR" />
        <result column="ReportDueDate" property="reportDueDate" jdbcType="TIMESTAMP" />
        <result column="ReportStatus" property="reportStatus" jdbcType="INTEGER" />
        <result column="CoverPageTemplateID" property="coverPageTemplateID" jdbcType="VARCHAR" />
        <result column="CoverPageTemplateName" property="coverPageTemplateName" jdbcType="VARCHAR" />
        <result column="CoverPageTemplatePath" property="coverPageTemplatePath" jdbcType="VARCHAR" />
        <result column="CoverPageTemplateNewMappingFlag" property="coverPageTemplateNewMappingFlag" jdbcType="BIT" />
        <result column="TemplateID" property="templateID" jdbcType="VARCHAR" />
        <result column="ReportTypeID" property="reportTypeID" jdbcType="VARCHAR" />
        <result column="RequestID" property="requestID" jdbcType="VARCHAR" />
        <result column="RequestSentDate" property="requestSentDate" jdbcType="TIMESTAMP" />
        <result column="RequestFinishedDate" property="requestFinishedDate" jdbcType="TIMESTAMP" />
        <result column="ApproverBy" property="approverBy" jdbcType="VARCHAR" />
        <result column="Approver" property="approver" jdbcType="VARCHAR" />
        <result column="ApproverDate" property="approverDate" jdbcType="TIMESTAMP" />
        <result column="CustomerCode" property="customerCode" jdbcType="VARCHAR" />
        <result column="CustomerGroupCode" property="customerGroupCode" jdbcType="VARCHAR" />
        <result column="CertificateFileCloudKey" property="certificateFileCloudKey" jdbcType="VARCHAR" />
        <result column="CertificateName" property="certificateName" jdbcType="VARCHAR" />
        <result column="AmendRemark" property="amendRemark" jdbcType="VARCHAR" />
        <result column="ActiveIndicator" property="activeIndicator" jdbcType="BIT" />
        <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
        <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
        <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
        <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
        <result column="RecalculationFlag" property="recalculationFlag" jdbcType="INTEGER" />
        <result column="ConclusionMd5" property="conclusionMd5" jdbcType="VARCHAR" />
        <result column="AwbNo" property="awbNo" jdbcType="VARCHAR" />
        <result column="IsLastReport" property="isLastReport" jdbcType="INTEGER" />
        <result column="Remark" property="remark" jdbcType="VARCHAR" />
        <result column="IsToDm" property="isToDm" jdbcType="INTEGER" />
        <result column="IsDeliveryApprove" property="isDeliveryApprove" jdbcType="BIT" />
        <result column="DeliverReportFormat" property="deliverReportFormat" jdbcType="VARCHAR" />
        <result column="ApproveStatus" property="approveStatus" jdbcType="INTEGER" />
        <result column="WatermarkCode" property="watermarkCode" jdbcType="INTEGER" />
        <result column="LogoAliyunID" property="logoAliyunID" jdbcType="VARCHAR" />
        <result column="AmendReportType" property="amendReportType" jdbcType="INTEGER" />
        <result column="ReportFlag" property="reportFlag" jdbcType="INTEGER" />
        <result column="NeedReview" property="needReview" jdbcType="INTEGER" />
        <result column="TypingFinishedFlag" property="typingFinishedFlag" jdbcType="INTEGER" />
        <result column="SoftcopyDeliveryDate" property="softcopyDeliveryDate" jdbcType="TIMESTAMP" />
        <result column="WorkFlow" property="workFlow" jdbcType="INTEGER" />
        <result column="EditorBy" property="editorBy" jdbcType="VARCHAR" />
        <result column="Editor" property="editor" jdbcType="VARCHAR" />
        <result column="ReviewerBy" property="reviewerBy" jdbcType="VARCHAR" />
        <result column="Reviewer" property="reviewer" jdbcType="VARCHAR" />
        <result column="SubReportReviseFlag" property="subReportReviseFlag" jdbcType="INTEGER" />
        <result column="ActualReportNo" property="actualReportNo" jdbcType="VARCHAR" />
        <result column="ExternalReportNo" property="externalReportNo" jdbcType="VARCHAR" />
        <result column="SignatureLanguage" property="signatureLanguage" jdbcType="INTEGER" />
    </resultMap>
    <resultMap id="testSampleResultMap" type="com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.TestSampleReportPO" extends="BaseResultMap">
        <result column="testSampleId" property="testSampleId" jdbcType="VARCHAR" />
        <result column="testSampleNo" property="testSampleNo" jdbcType="VARCHAR" />
    </resultMap>
    <sql id="BaseColumns">
        ${alias}.ID as id, ${alias}. OrderNo, ${alias}.ReportNo, ${alias}.ParentReportNo, ${alias}.ReportDueDate,
        ${alias}.ReportStatus, ${alias}.CoverPageTemplateID, ${alias}.CoverPageTemplateName, ${alias}.CoverPageTemplatePath,
        ${alias}.CoverPageTemplateNewMappingFlag, ${alias}.TemplateID,
        ${alias}.ReportTypeID, ${alias}.RequestID, ${alias}.RequestSentDate, ${alias}.RequestFinishedDate,
        ${alias}.ApproverBy, ${alias}.Approver, ${alias}.EditorBy, ${alias}.Editor, ${alias}.ReviewerBy,
        ${alias}.Reviewer, ${alias}.ApproverDate, ${alias}.SealFlag, ${alias}.CustomerCode, ${alias}.CustomerGroupCode, ${alias}.CertificateFileCloudKey,
        ${alias}.CertificateName, ${alias}.AmendRemark, ${alias}.ActiveIndicator, ${alias}.CreatedBy, ${alias}.CreatedDate, ${alias}.ModifiedBy,
        ${alias}.ModifiedDate, ${alias}.RecalculationFlag, ${alias}.ConclusionMd5, ${alias}.LastModifiedTimestamp, ${alias}.AwbNo, ${alias}.IsLastReport,
        ${alias}.Remark, ${alias}.IsToDm, ${alias}.IsDeliveryApprove, ${alias}.deliverReportFormat,
        ${alias}.QrcodeFlag, ${alias}.ApproveStatus, ${alias}.WatermarkCode, ${alias}.LogoAliyunID,
        ${alias}.AmendReportType, ${alias}.ReportFlag, ${alias}.NeedReview, ${alias}.TypingFinishedFlag,
        ${alias}.SoftcopyDeliveryDate, ${alias}.WorkFlow, ${alias}.SubReportReviseFlag, ${alias}.NeedToAmendFlag, ${alias}.ActualReportNo,
        ${alias}.OperationType, ${alias}.ExternalReportNo, ${alias}.SignatureLanguage
    </sql>
    <sql id="select">
        SELECT
        report.ID,
        report.OrderNo,
        report.ReportNo,
        report.ParentReportNo,
        report.RootReportNo,
        report.ReportDueDate,
        report.ReportStatus,
        report.CoverPageTemplateID,
        report.CoverPageTemplateName,
        report.CoverPageTemplatePath,
        report.CoverPageTemplateNewMappingFlag,
        report.TemplateID,
        report.ReportTypeID,
        report.RequestID,
        report.RequestSentDate,
        report.RequestFinishedDate,
        report.ApproverBy,
        report.Approver,
        report.EditorBy,
        report.Editor,
        report.ReviewerBy,
        report.Reviewer,
        report.ApproverDate,
        report.SealFlag,
        report.CustomerCode,
        report.CustomerGroupCode,
        report.CertificateFileCloudKey,
        report.CertificateName,
        report.AmendRemark,
        report.ActiveIndicator,
        report.CreatedBy,
        report.CreatedDate,
        report.ModifiedBy,
        report.ModifiedDate,
        report.RecalculationFlag,
        report.ConclusionMd5,
        report.LastModifiedTimestamp,
        report.AwbNo,
        report.IsLastReport,
        report.Remark,
        report.IsToDm,
        report.IsDeliveryApprove,
        report.deliverReportFormat,
        report.QrcodeFlag,
        report.ApproveStatus,
        report.WatermarkCode,
        report.LogoAliyunID,
        report.AmendReportType,
        report.ReportFlag,
        report.NeedReview,
        report.TypingFinishedFlag,
        report.SoftcopyDeliveryDate,
        report.WorkFlow,
        report.SubReportReviseFlag,
        report.NeedToAmendFlag,
        report.ActualReportNo,
        report.OperationType,
        report.ExternalReportNo,
        report.SignatureLanguage,
        report.countryOfDestination,
        report.TestMatrixMergeMode,
        report.ReportVersion,
        report.TestingType,
        report.PhotoRemark
        FROM
        tb_report report
        left join tb_general_order_instance goi on report.orderNo = goi.orderNo
        <where>
            report.ActiveIndicator = 1
            <!--使用了ReportId，ReportNo的。忽略lab条件-->
            <if test="!req.useAccurateQuery">
                and goi.LabCode = #{req.lab.labCode}
            </if>
            <if test="req.orderNoList != null and req.orderNoList.size() > 0">
                and report.OrderNo in
                <foreach item="orderNo" collection="req.orderNoList" open="(" separator="," close=")">
                    #{orderNo}
                </foreach>
            </if>
            <if test="req.reportIdList != null and req.reportIdList.size() > 0">
                and report.ID in
                <foreach item="reportId" collection="req.reportIdList" open="(" separator="," close=")">
                    #{reportId}
                </foreach>
            </if>
            <if test="req.reportNoList != null and req.reportNoList.size() > 0">
                and report.reportNo in
                <foreach item="reportNo" collection="req.reportNoList" open="(" separator="," close=")">
                    #{reportNo}
                </foreach>
            </if>
            <if test="req.reportId != null and req.reportId != '' ">
                and report.ID = #{req.reportId}
            </if>
            <if test="req.reportNo != null and req.reportNo != '' ">
                and report.reportNo = #{req.reportNo}
            </if>
        </where>
        group by report.ID
        order by report.reportNo desc
    </sql>
    <select id="queryReportByTestSample" resultMap="testSampleResultMap">
        SELECT
            s.id AS testSampleId,
            s.SampleNo AS testSampleNo,
            <include refid="BaseColumns"><property name="alias" value="r"/></include>
        FROM
            tb_test_sample s
            INNER JOIN tb_test_matrix m ON m.TestSampleID = s.id
            INNER JOIN tre_report_matrix_relationship rmrel ON rmrel.TestMatrixID = m.id
            INNER JOIN tb_report r ON r.id = rmrel.ReportID
            <where>
                <choose>
                    <when test="testSampleIdList != null and testSampleIdList.size() > 0">
                        AND s.id in
                        <foreach collection="testSampleIdList" item="testSampleId" open="(" separator="," close=")">
                            #{testSampleId}
                        </foreach>
                    </when>
                    <otherwise>
                        1 != 1
                    </otherwise>
                </choose>

            </where>
    </select>

    <select id="queryReportConclusionList" resultType="com.sgs.gpo.facade.model.otsnotes.conclusion.ConclusionDTO">
        SELECT
            c.ConclusionID as conclusionId,
            c.ConclusionRemark as conclusionRemark,
            c.ObjectID as objectId,
            cl.ConclusionCode as conclusionCode,
            cl.Description as description,
            c.ReportID as reportId
        FROM
            tb_conclusion c
            LEFT JOIN tb_conclusion_list cl ON cl.ID = c.ConclusionID
        <where>
            <choose>
                <when test="reportIdList != null and reportIdList.size() > 0">
                    AND c.ReportID in
                    <foreach collection="reportIdList" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    1 != 1
                </otherwise>
            </choose>
            AND c.ConclusionLevelID = 603
            AND c.ConclusionID is not null
        </where>
    </select>

    <select id="queryReportMatrixConclusionList" resultType="com.sgs.gpo.facade.model.otsnotes.conclusion.ConclusionDTO">
        SELECT
        c.ConclusionID as conclusionId,
        c.ConclusionRemark as conclusionRemark,
        c.ObjectID as objectId,
        cl.ConclusionCode as conclusionCode,
        cl.Description as description,
        c.ReportID as reportId
        FROM
        tb_conclusion c
        LEFT JOIN tb_conclusion_list cl ON cl.ID = c.ConclusionID
        <where>
            <choose>
                <when test="reportIdList != null and reportIdList.size() > 0">
                    AND c.ReportID in
                    <foreach collection="reportIdList" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    1 != 1
                </otherwise>
            </choose>
            AND c.ConclusionLevelID = 601
            AND c.ConclusionID is not null
        </where>
    </select>
    <select id="queryReportConclusion" resultType="com.sgs.gpo.dbstorages.mybatis.model.otsnotes.conclusion.ConclusionListPO">
        SELECT
            cl.ConclusionCode,
            cl.Description,
            c.ConclusionSettingID
        FROM
            tb_conclusion c
            LEFT JOIN tb_conclusion_list cl ON cl.ID = c.ConclusionID
        WHERE
            c.ReportID = #{reportId}
            AND c.ConclusionLevelID = 603
            AND c.ConclusionID is not null
            LIMIT 1
    </select>
    <select id="select" resultType="com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO">
        <include refid="select"></include>
    </select>

    <select id="getJobValidateByReportId" resultType="java.lang.String">
        SELECT
            tl.`ValidateBy`
        FROM
            tb_job tj
                INNER JOIN tre_job_test_line_relationship tjtr ON tj.`ID` = tjtr.`JobID`
                INNER JOIN tb_test_matrix ttm ON ttm.`TestLineInstanceID` = tjtr.`TestLineInstanceID`
                INNER JOIN tre_report_matrix_relationship trmr ON trmr.`TestMatrixID` = ttm.`ID`
                INNER JOIN tb_test_line_instance tl ON tl.id = tjtr.TestLineInstanceID
        WHERE trmr.`ReportID` = #{reportId}
          AND tl.`TestEndDate` IS NOT NULL
          AND tl.`ValidateBy` IS NOT NULL
        ORDER BY tl.`TestEndDate` DESC
            LIMIT 1
    </select>
    <update id="updateReportEntryMode">
        update tb_report
        set EntryMode = #{entryMode},
        ModifiedDate = #{modifiedDate},
        ModifiedBy = #{modifiedBy}
        <where>
            AND (EntryMode is null or EntryMode = '')
            <choose>
                <when test="reportIdList != null and reportIdList.size() > 0">
                    AND ID in
                    <foreach collection="reportIdList" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    1 != 1
                </otherwise>
            </choose>
        </where>
    </update>
    <update id="updateReportCertificate">
        update tb_report
        set CertificateName = #{certificateName},
        CertificateFileCloudKey = #{certificateFileCloudKey},
        CertificateId = #{certificateId},
        ModifiedDate = #{modifiedDate},
        ModifiedBy = #{modifiedBy}
        <where>
            <choose>
                <when test="reportIdList != null and reportIdList.size() > 0">
                    AND ID in
                    <foreach collection="reportIdList" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    1 != 1
                </otherwise>
            </choose>
        </where>
    </update>

    <select id="queryReferReportList" resultType="com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO"
            parameterType="com.sgs.gpo.facade.model.report.req.ReferenceReportQueryReq">
        select
            distinct
            tr.ReportNo reportNo,
            tr.ID id,
            tr.ActualReportNo actualReportNo
        from
            tb_test_matrix ttm
                inner join tre_report_matrix_relationship trmr on
                ttm.ID = trmr.TestMatrixID
                inner join tb_report tr on
                tr.ID = trmr.ReportID
                inner join tb_test_line_instance ttli on ttli.ID = ttm.TestLineInstanceID
        where
            ttm.TestSampleID = #{testSampleId}
          and ttli.TestLineVersionID = #{testLineVersionId}
          and ttli.CitationVersionId = #{citationVersionId}
    </select>

    <select id="selectTrackingToolData" resultType="com.sgs.gpo.facade.model.trackingtool.TrackingToolDataRsp">
        select distinct tr.ID reportId, tr.ReportNo reportNo,tr.ReportStatus reportStatus,tr.ReportDueDate reportDueDate,tr.SoftcopyDeliveryDate softcopyDeliveryDate,
        toi.applicant_name applicantName,toi.buyer_name buyerName,toi.payer_name payerName, toi.cs csName,toi.order_id orderId,toi.order_no orderNo,toi.service_type serviceType
        <if test="req.isJobOrSubContract != null and req.isJobOrSubContract!='' and req.isJobOrSubContract == '1'.toString()">
            ,tj.JobNo jobNo,tj.JobStatus jobStatus,tj.LabInDate labInDate,tj.LabOutDate labOutDate,tj.Remark jobRemark,tj.ExpectedDueDate jobExpectedDueDate
        </if>
        <if test="req.isJobOrSubContract != null and req.isJobOrSubContract!='' and req.isJobOrSubContract == '2'.toString()">
            ,tsc.SubContractNo subcontractNo,tsc.Status subcontractStatus,tsc.SubContractRemark subcontractRemark,tsc.SubContractExpectDueDate subcontractExpectDueDate,serel.ExternalNo referenceNo
        </if>
        from tb_report tr
        inner join tb_order_index toi on tr.OrderNo = toi.order_no
        inner join tre_report_matrix_relationship trmr on tr.ID =trmr.ReportID
        inner join tb_test_matrix ttm on ttm.ID = trmr.TestMatrixID
        <if test="req.isJobOrSubContract != null and req.isJobOrSubContract!='' and req.isJobOrSubContract == '1'.toString()">
        inner join tre_job_test_line_relationship tjtlr on tjtlr.TestLineInstanceID = ttm.TestLineInstanceID
        inner join tb_job tj on tj.ID = tjtlr.JobID
        </if>
        <if test="req.isJobOrSubContract != null and req.isJobOrSubContract!='' and req.isJobOrSubContract == '2'.toString()">
        inner join tb_sub_contract_test_line_mapping tsctlm on tsctlm.TestLineInstanceID = ttm.TestLineInstanceID
        inner join tb_sub_contract tsc on tsc.ID = tsctlm.SubContractID
        left join tb_subcontract_external_relationship serel on serel.SubContractNo = tsc.SubContractNo
        </if>
        <where>
            <if test="req.reportCreateStartDate != null and req.reportCreateStartDate!=''">
                and tr.CreatedDate  <![CDATA[>=]]>#{req.reportCreateStartDate,jdbcType=TIMESTAMP}
            </if>
            <if test="req.reportCreateEndDate != null and req.reportCreateEndDate!=''">
                and tr.CreatedDate  <![CDATA[<=]]>#{req.reportCreateEndDate,jdbcType=TIMESTAMP}
            </if>
            <if test="req.reportDueStartDate != null and req.reportDueStartDate!=''">
                and tr.ReportDueDate  <![CDATA[>=]]>#{req.reportDueStartDate,jdbcType=TIMESTAMP}
            </if>
            <if test="req.reportDueEndDate != null and req.reportDueEndDate!=''">
                and tr.ReportDueDate  <![CDATA[<=]]>#{req.reportDueEndDate,jdbcType=TIMESTAMP}
            </if>
            and tr.LabId = #{req.labId}
            <if test="req.reportNo != null and req.reportNo!=''">
                and tr.ReportNo like concat('%', #{req.reportNo},'%')
            </if>
            <if test="req.reportNosList != null and req.reportNosList.size() > 0">
                and tr.ReportNo in
                <foreach item="reportNo" collection="req.reportNosList" open="(" separator="," close=")">
                    #{reportNo}
                </foreach>
            </if>
            <if test="req.reportStatusList != null and req.reportStatusList.size() > 0">
                and tr.ReportStatus in
                <foreach item="reportStatus" collection="req.reportStatusList" open="(" separator="," close=")">
                    #{reportStatus}
                </foreach>
            </if>
            <if test="req.orderNo != null and req.orderNo!=''">
                and toi.order_no like concat('%', #{req.orderNo},'%')
            </if>
            <if test="req.serviceType != null and req.serviceType!=''">
                and toi.service_type = #{req.serviceType}
            </if>
            <if test="req.applicantName != null and req.applicantName!=''">
                and toi.applicant_name like concat('%',#{req.applicantName},'%')
            </if>
            <if test="req.csName != null and req.csName!=''">
                and toi.cs like concat('%',#{req.csName},'%')
            </if>
            <if test="req.buyerName != null and req.buyerName!=''">
                and toi.buyer_name like concat('%',#{req.buyerName},'%')
            </if>
            <if test="req.payerName != null and req.payerName!=''">
                and toi.payer_name like concat('%',#{req.payerName},'%')
            </if>
            <if test="req.isJobOrSubContract != null and req.isJobOrSubContract!='' and req.isJobOrSubContract == '1'.toString()">
                <if test="req.jobNo != null and req.jobNo!=''">
                    and tj.JobNo like concat('%', #{req.jobNo},'%')
                </if>
                <if test="req.jobStatus != null and req.jobStatus!=''">
                    and tj.JobStatus = #{req.jobStatus}
                </if>
            </if>
            <if test="req.isJobOrSubContract != null and req.isJobOrSubContract!='' and req.isJobOrSubContract == '2'.toString()">
                <if test="req.subcontractNo != null and req.subcontractNo!=''">
                    and tsc.SubContractNo like concat('%', #{req.subcontractNo},'%')
                </if>
                <if test="req.subcontractStatus != null and req.subcontractStatus!=''">
                    and tsc.Status = #{req.subcontractStatus}
                </if>
            </if>
        </where>
    </select>

    <update id="updateDelayForOdc">
       update  gpn.tb_delay set last_modified_timestamp='2024-12-05 18:12:00.000' where object_id in (
        '40adc4a8fb5059a2017d7ce9e98bef41',
        'b9bcafd6-0156-4f33-a185-00154735495a',
        '5be88dd0b91b3f48fa17196bcf2485a3',
        '5101c984-272f-4b34-90db-032babc94b81',
        'a5378282-b1a6-4c83-bf8d-615386d3487e',
        'c83e98ec-88c3-4e49-ab61-cd0e2f9707d9',
        'ee660aaa-1af5-47b7-8b8f-dda67ffddb13',
        'eeecd618-7f24-4856-9494-8ed3fdd433b0',
        '932840e2-7e85-41b1-a1ac-12b0e7ae08bc',
        '7b496d3bc248fe8da7a3a89c6855fc22',
        '8643182f0394675c83fbd67c5574df57',
        '24c18c99-625a-4358-b5b5-544c3af71868',
        'a872e004-6335-41fe-bbde-090c9ac0d795',
        '5eeadac37ac24bce9bb73566c5416a1c',
        '24a8fdda-f510-4491-89d5-dcef5072e80e',
        '284b8cb9-7427-4b7b-89d6-6d3aec0124c1',
        'ebc37e79061c7bd446f895793284e2f3',
        'dd77ffce-ad6b-4dfa-9c00-a40b7184ed1d'
        )
    </update>


    <select id="queryReportBySubReportId" resultType="com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO">
        SELECT distinct
            r.id,
            r.ReportNo,
            r.ReportFlag,
            r.ReportStatus,
            r.OrderNo,
            r.ActualReportNo
        FROM tb_sub_report sr
                 inner join tre_report_sub_report_relationship srrel on sr.id = srrel.sub_report_id
                 inner join tb_report r on r.id = srrel.report_id
        where sr.id = #{subReportId}
    </select>

</mapper>