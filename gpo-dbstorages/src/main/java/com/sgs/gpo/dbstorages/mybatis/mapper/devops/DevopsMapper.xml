<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.devops.DevopsMapper" >
    <update id="cancelTestingOrder">
        update tb_enquiry e join tb_general_order o on e.enquiry_no = o.EnquiryNo  set enquiry_status =30 where o.OrderNo in
        <foreach collection="orderNoList" item="orderNo" open="(" separator="," close=")">
            #{orderNo}
        </foreach>;
        update gpo.tb_general_order set OrderStatus = 7 WHERE OrderNo in
        <foreach collection="orderNoList" item="orderNo" open="(" separator="," close=")">
            #{orderNo}
        </foreach>;
        update gpo.tb_report set ReportStatus= 202 WHERE OrderNo in
        <foreach collection="orderNoList" item="orderNo" open="(" separator="," close=")">
            #{orderNo}
        </foreach>;
        update gpn.tb_report set ReportStatus= 202 WHERE OrderNo in
        <foreach collection="orderNoList" item="orderNo" open="(" separator="," close=")">
            #{orderNo}
        </foreach>;
    </update>

    <select id="getOrderNoListByAccNone" resultType="com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO">
        select
            tr.reportNo,
		    tr.OrderNo
        from
		    gpn.tb_report tr
        inner join gpo.tb_general_order tgo on tgo.OrderNo = tr.OrderNo and tgo.BUCode = #{buCode}
        where
		(tr.CertificateId is null or tr.CertificateId = '') and tr.ReportStatus in (201,204)
    </select>
    <select id="getErrorSubReportPO" resultType="com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subreport.SubReportPO">
        select
            *
        from
            gpn.tb_sub_report tsr
        where
            ReportNo is null
          and ReportFileType = 1501
          and ReportSource = 'Upload'
          and ObjectType = 'subcontract'
          and Filename like '%.docx'
          and tsr.CreatedDate > '2024-12-22 20:00:00'
          and id not in (
            select
                sub_report_id
            from
                gpn.tre_report_sub_report_relationship) order by tsr.CreatedDate desc limit 200;
    </select>

    <select id="get01ReportByOrderId" resultType="com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO">
        select
            *
        from
            gpn.tb_report tr
                inner join gpn.tb_general_order_instance tgoi on
                tgoi.OrderNo = tr.OrderNo
        where
            tr.ReportStatus not in (202,205,207)
          and tgoi.ID = #{orderId}
        order by
            tr.CreatedDate asc limit 1
    </select>

    <select id="getGeneralOrderList" resultType="com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO">
        select
        *
        from
        tb_general_order tgo
        where CreatedDate  &gt;= #{start} and CreatedDate &lt;= #{end} and OrderStatus not in (5,7,10) and buCode = 'MR' and OperationMode &lt;&gt; 8
    </select>
</mapper>