package com.sgs.gpo.web.controller.devops;

import com.alibaba.dubbo.common.json.ParseException;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.base.command.BaseExecutor;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.kafka.client.ErrorCode;
import com.sgs.framework.kafka.client.KafkaMessageRsp;
import com.sgs.framework.kafka.client.MessageReq;
import com.sgs.framework.log.SystemLogHelper;
import com.sgs.framework.log.enums.SystemLogType;
import com.sgs.framework.log.model.SystemLog;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.model.order.v2.OrderIdBO;
import com.sgs.framework.security.annotation.AccessRule;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.framework.tool.utils.StringPool;
import com.sgs.gpo.biz.kafka.DataEntryEventConsumer;
import com.sgs.gpo.biz.kafka.SciTrfEventConsumer;
import com.sgs.gpo.biz.otsnotes.dataentry.IDateEntryBizService;
import com.sgs.gpo.biz.otsnotes.report.IReportBizService;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.report.ReportMapper;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order.GeneralOrderMapper;
import com.sgs.gpo.dbstorages.mybatis.mapper.setting.log.SystemLogMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.attachment.OrderAttachmentPO;
import com.sgs.gpo.dbstorages.mybatis.model.setting.log.SystemLogPO;
import com.sgs.gpo.domain.service.common.eventcenter.EventCenterService;
import com.sgs.gpo.domain.service.common.eventcenter.PreEventListener;
import com.sgs.gpo.domain.service.extservice.sci.SciTrfContext;
import com.sgs.gpo.domain.service.extservice.sci.command.SciOrderToTrfCMD;
import com.sgs.gpo.domain.service.extservice.sci.command.SciSyncTrfCMD;
import com.sgs.gpo.domain.service.extservice.sci.command.SciUnBindTrfCMD;
import com.sgs.gpo.domain.service.otsnotes.order.IGeneralOrderInstanceService;
import com.sgs.gpo.domain.service.otsnotes.report.IReportDomainService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportService;
import com.sgs.gpo.domain.service.preorder.attachment.IOrderAttachmentService;
import com.sgs.gpo.domain.service.preorder.devops.IDevopsService;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryService;
import com.sgs.gpo.domain.service.preorder.order.IOrderDomainService;
import com.sgs.gpo.domain.service.preorder.order.impl.OrderIndexServiceImpl;
import com.sgs.gpo.domain.service.preorder.ordertrfrel.IOrderTrfRelationshipService;
import com.sgs.gpo.domain.service.system.ISystemService;
import com.sgs.gpo.facade.model.attachment.OrderAttachmentTagDTO;
import com.sgs.gpo.facade.model.dataentry.DataEntryEventReq;
import com.sgs.gpo.facade.model.otsnotes.report.req.ReportTestMatrixModelGetReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQualificationTypeUpdateReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderSyncTrfReq;
import com.sgs.gpo.facade.model.report.req.ReportDefaultAccreditationQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportEntryModeUpdateReq;
import com.sgs.gpo.facade.model.report.req.ReportExtForTLUpdateReq;
import com.sgs.gpo.facade.model.report.req.ReportTestResultStatusUpdateReq;
import com.sgs.gpo.facade.model.report.rsp.DefaultAccreditationRsp;
import com.sgs.gpo.facade.model.sci.bo.OrderToTrfBO;
import com.sgs.gpo.facade.model.sci.bo.SciTrfSyncBO;
import com.sgs.gpo.facade.model.sci.bo.SciUnBindTrfBO;
import com.sgs.gpo.facade.model.sci.req.GpoSciOrderToTrfReq;
import com.sgs.gpo.facade.model.sci.req.GpoSciTrfSyncReq;
import com.sgs.gpo.facade.sci.SciFacade;
import com.sgs.gpo.integration.TokenClient;
import com.sgs.gpo.integration.sci.SciTrfServiceNew;
import com.sgs.gpo.integration.sci.req.TrfUpdateReq;
import com.sgs.preorder.core.common.PreEvent;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/26 11:41
 */
@Slf4j
@RestController
@RequestMapping("/devops/")
@Api(value = "Devops 管理", tags = "Devops")
public class DevopsController {

    @Autowired
    private ISystemService systemService;
    @Autowired
    private SystemLogHelper systemLogHelper;
    @Autowired
    private IOrderTrfRelationshipService orderTrfRelationshipService;
    @Autowired
    private IOrderDomainService orderDomainService;
    @Autowired
    private SciTrfServiceNew sciTrfService;
    @Autowired
    private IEnquiryService enquiryService;
    @Autowired
    private SciOrderToTrfCMD orderToTrfCMD;
    @Autowired
    private SciUnBindTrfCMD unBindTrfCMD;
    @Autowired
    private SciSyncTrfCMD sciSyncTrfCMD;
    @Resource
    private EventCenterService eventCenterService;
    @Autowired
    private SystemLogMapper systemLogMapper;
    @Autowired
    private IReportDomainService reportDomainService;
    @Autowired
    private PreEventListener preEventListener;
    @Autowired
    private GeneralOrderMapper generalOrderMapper;
    @Autowired
    private SciFacade sciFacade;
    @Autowired
    private IReportService reportService;
    @Autowired
    private IGeneralOrderInstanceService generalOrderInstanceService;
    @Autowired
    private IDevopsService devopsService;
    @Autowired
    private IReportBizService reportBizService;
    @Autowired
    private IDateEntryBizService dateEntryBizService;
    @Autowired
    private DataEntryEventConsumer dataEntryEventConsumer;
    @Autowired
    private TokenClient tokenClient;
    @Autowired
    private IOrderAttachmentService orderAttachmentService;
    @Autowired
    private ReportMapper reportMapper;
    @Autowired
    private SciTrfEventConsumer sciTrfEventConsumer;
    @Autowired
    private OrderIndexServiceImpl orderIndexService;
    /**
     * 刷新系统缓存
     * @return
     */
    @PostMapping("refresh-system-cache")
    @ApiOperation(value = "刷新系统缓存", notes = "重新加载系统缓存数据[Lab]")
    public BaseResponse refreshSystemCache(){
        systemService.initLabCache();
        return BaseResponse.newSuccessInstance(true);
    }

    @PostMapping("system-log")
    @ApiOperation(value = "记录系统日志", notes = "记录系统日志")
    public BaseResponse systemLog(@RequestBody SystemLog systemLog){
        try{
            systemLog = new SystemLog();
            systemLog.setProductLineCode("HL");
            systemLog.setRequest("1");
            systemLog.setResponse("1");
            systemLog.setUrl("1");
            systemLog.setTarget(SgsSystem.SCI.getCode());
            systemLog.setOperationType("UnBindTRF");
            systemLog.setType(SystemLogType.API.getType());
            systemLog.setObjectNo("1");
            systemLogHelper.save(systemLog);
        }catch (Exception e){
            log.error("log:{}",e);
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @PostMapping("clickhouse")
    @ApiOperation(value = "记录系统日志", notes = "记录系统日志")
    public BaseResponse clickhouse(@RequestBody SystemLog systemLog){
        DynamicDataSourceContextHolder.push("clickhouse");
        SystemLogPO systemLogPO = Func.copy(systemLog,SystemLogPO.class);
        return BaseResponse.newSuccessInstance(systemLogMapper.insert(systemLogPO));
    }

    @GetMapping("orderToTrf")
    public BaseResponse<Boolean> orderToTrf(){
        GpoSciOrderToTrfReq orderToTrfReq = new GpoSciOrderToTrfReq();
        orderToTrfReq.setRefSystemId(2);
        orderToTrfReq.setOrderNo("GZHL2405002634SG");
        orderToTrfReq.setToken("9a87f52606064fb390547af4cb0ec63b");
        orderToTrfReq.setProductLineCode("HL");

        SciTrfContext<GpoSciOrderToTrfReq, OrderToTrfBO> context = new SciTrfContext<>();
        context.setParam(orderToTrfReq);
        context.setUserInfo(SecurityContextHolder.getUserInfo());
        context.setToken("9a87f52606064fb390547af4cb0ec63b");
        context.setProductLineCode(orderToTrfReq.getProductLineCode());
        BaseResponse response = BaseExecutor.start(SciOrderToTrfCMD.class, context);
        log.warn("orderToTrf:{}",JSON.toJSONString(context));
        return  BaseResponse.newSuccessInstance(context);
    }

    @PostMapping("trfSync")
    public BaseResponse<Boolean> TrfSync(@RequestBody GpoSciTrfSyncReq gpoSciTrfSyncReq){
        SciTrfContext<GpoSciTrfSyncReq, SciTrfSyncBO> sciTrfContext = new SciTrfContext<>();
        sciTrfContext.setParam(gpoSciTrfSyncReq);
        sciTrfContext.setUserInfo(SecurityContextHolder.getUserInfo());
        sciTrfContext.setToken(SecurityContextHolder.getSgsToken());
        sciTrfContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        BaseResponse response = BaseExecutor.start(SciSyncTrfCMD.class, sciTrfContext);
        log.warn("TrfSync:{}",JSON.toJSONString(response));
        return  response;
    }

    @PostMapping("preEventListener")
    public BaseResponse<Boolean> preEventListener(@RequestBody PreEvent preEvent){
        return preEventListener.processEvent(preEvent);
    }

    @GetMapping("unbindTrf")
    public BaseResponse<Boolean> unbindTrf(){
        GpoSciTrfSyncReq gpoSciTrfSyncReq = new GpoSciTrfSyncReq();
        gpoSciTrfSyncReq.setRefSystemId(2);
        gpoSciTrfSyncReq.setTrfNo("TRF4A001819");
        gpoSciTrfSyncReq.setOrderNo("SHHL2401000113PL");
        gpoSciTrfSyncReq.setObjectType("order");
        gpoSciTrfSyncReq.setSourceNo("SHHL2401000113PL");
        gpoSciTrfSyncReq.setSourceId("0b5622a5b37933e022478a1b8719d5b7");
        gpoSciTrfSyncReq.setSciUrl("");
        gpoSciTrfSyncReq.setEventType("Unbound");
        gpoSciTrfSyncReq.setSciAction("");
        gpoSciTrfSyncReq.setToken("c8b21c8bc6b940fbb3dbb671115ce83f");
        gpoSciTrfSyncReq.setProductLineCode("HL");
        SciTrfContext<GpoSciTrfSyncReq, SciUnBindTrfBO> sciTrfContext = new SciTrfContext<>();
        sciTrfContext.setParam(gpoSciTrfSyncReq);
        sciTrfContext.setUserInfo(SecurityContextHolder.getUserInfo());
        sciTrfContext.setToken(SecurityContextHolder.getSgsToken());
        sciTrfContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
//        BaseResponse response = BaseExecutor.start(SciUnBindTrfCMD.class, sciTrfContext);
        log.warn("unbindTrf:{}",JSON.toJSONString(sciTrfContext));
        String data= "{\"eventSource\":\"order\",\"eventSourceId\":\"0b5622a5b37933e022478a1b8719d5b7\",\"eventSourceNo\":\"SHHL2401000113PL\",\"eventSourceStatus\":1,\"eventType\":\"Unbound\",\"externalOrderList\":[{\"extInfo\":\"{}\",\"externalSubOrderNo\":\"TRF4A001819\",\"refSystemId\":2,\"trfSourceType\":\"TRF2Order\"}],\"orderId\":\"0b5622a5b37933e022478a1b8719d5b7\",\"orderNo\":\"SHHL2401000113PL\",\"productLineCode\":\"HL\",\"token\":\"c8b21c8bc6b940fbb3dbb671115ce83f\"}";
        PreEvent preEvent = JSON.parseObject(data, PreEvent.class);

        try {
            eventCenterService.processEvent(preEvent);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return  BaseResponse.newSuccessInstance(sciTrfContext);
    }



    @PostMapping("testLineLabOut")
    public BaseResponse testLineLabOut(@RequestBody ReportExtForTLUpdateReq reportExtForTLUpdateReq ){
        //更新TestLine相关的Report的testCompletedFlag
        Set<String> testLineInstanceIdList = Sets.newHashSet("e6d83471-b711-41ae-b1e0-ebd1bfeb4bcfc","294d375e-1660-4b78-b113-02a4e3173a7b");
        BaseResponse<Boolean> updateReportExtRes = reportDomainService.updateReportExtForTL(reportExtForTLUpdateReq);
        log.info("TL LabOut updateReportExt res:{}", Func.toJson(updateReportExtRes));
        return updateReportExtRes;
    }

    @PostMapping("cp-to-sgsmart")
    public BaseResponse cpToSgsMart(@RequestBody OrderSyncTrfReq orderSyncTrfReq){
        orderSyncTrfReq.setCpToSgsMart(orderSyncTrfReq.isCpToSgsMart());
        if(Func.isNotEmpty(orderSyncTrfReq.getOrderNoList())){
            return sciFacade.orderSyncTrf(orderSyncTrfReq);
        }else{
            List<Map<String, String>> maps = generalOrderMapper.selectCpToSgsMartData(orderSyncTrfReq);
            Set<String> collect = maps.stream().map(item -> item.get("orderNo")).distinct().filter(Func::isNotEmpty).collect(Collectors.toSet());
            if(Func.isNotEmpty(collect)){
                orderSyncTrfReq.setOrderNoList(collect);
                return sciFacade.orderSyncTrf(orderSyncTrfReq);
            }else{
                return BaseResponse.newFailInstance("没有要处理的数据");
            }
        }
    }

    //ADD GPO2-14443
    @PostMapping("cancel-order")
    @AccessRule
    public  BaseResponse  cancelQaTestingOrder(@RequestBody List<String>  orderNoList){
        try {
            if (Func.isNotEmpty(orderNoList)) {
                log.info("Devops Cancel Order,User:"+SystemContextHolder.getRegionAccount()+" orderNoList:"+Func.toJson(orderNoList));
                devopsService.cancelTestingOrder(orderNoList);
            }else{
                return BaseResponse.newFailInstance(ResponseCode.PARAM_MISS);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return BaseResponse.newSuccessInstance(true);
    }
    //修复资质默认值
    @PostMapping("accreditation-repair")
    @AccessRule
    public BaseResponse accreditationRepair(){
        String productLineCode = ProductLineContextHolder.getProductLineCode();
        if(Func.isEmpty(productLineCode)){
            return BaseResponse.newFailInstance("buCode为空");
        }
        //查询Bu下的全部OrderNo
        //查询全部ReportNo和OrderNo
        List<ReportPO> reportPOList = devopsService.getOrderNoListByAccNone(productLineCode);
//        ReportPO reportPO1 = new ReportPO();
//        reportPO1.setReportNo("GZMR230300028703");
//        reportPO1.setOrderNo("GZMR2303000287");
//        List<ReportPO> reportPOList = Lists.newArrayList(reportPO1);
        Set<String> reportNos = Sets.newHashSet();
        if(Func.isNotEmpty(reportPOList)){
            //单次执行500条
            for(int i =0; i < 500; i++){
                ReportPO report = reportPOList.get(i);
                reportNos.add(report.getReportNo());
                String orderNo = report.getOrderNo();
                log.info("devops修复Report资质数据:{}", JSON.toJSONString(reportNos));
                Set<String> orderNoList = Sets.newHashSet(orderNo);
                List<DefaultAccreditationRsp> accreditationList = new ArrayList<>();
                ReportDefaultAccreditationQueryReq req = new ReportDefaultAccreditationQueryReq();
                req.setOrderNoList(orderNoList);
                BaseResponse<List<DefaultAccreditationRsp>> defaultAccreditationRsp = reportBizService.defaultAccreditationQuery(req);
                if(defaultAccreditationRsp.isSuccess() && Func.isNotEmpty(defaultAccreditationRsp.getData())){
                    accreditationList = defaultAccreditationRsp.getData();
                }
                DefaultAccreditationRsp accreditation = accreditationList.stream().filter(
                        e -> Func.equalsSafe(e.getOrderNo(),orderNo)).findAny().orElse(null);
                if(Func.isNotEmpty(accreditation)){
                    //更新
                    ReportPO reportPO = new ReportPO();
                    reportPO.setCertificateId(accreditation.getCertificateId());
                    reportPO.setCertificateFileCloudKey(accreditation.getCertificateFileCloudKey());
                    reportPO.setCertificateName(accreditation.getCertificateName());
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
                    try{
                        Date parsedDate = sdf.parse("2024-06-30 21:21:21");
                        reportPO.setModifiedDate(parsedDate);
                    }catch (Exception e){
                        log.info(e.getMessage());
                    }
                    LambdaUpdateWrapper<ReportPO> wrapper = new LambdaUpdateWrapper<>();
                    wrapper.eq(ReportPO::getReportNo,report.getReportNo());
                    reportService.update(reportPO,wrapper);
                }
            }
        }
        return BaseResponse.newSuccessInstance(reportNos);
    }

    @PostMapping("test-data-entry-event")
    public BaseResponse dealDataEntreEvent(@RequestBody DataEntryEventReq req){
        return dateEntryBizService.handleDataEntryEvent(req);
    }
    @PostMapping("test-data-entry-event-consumer")
    public KafkaMessageRsp dataEntryEventConsumer(@RequestBody MessageReq messageReq){
        log.info("DataEntryEventConsumer receive req:{}", Func.toJson(messageReq));
        KafkaMessageRsp rsp = new KafkaMessageRsp();
        if (StringUtils.isBlank(messageReq.getData())) {
            rsp.setErrorCode(ErrorCode.ILLEGAL_ARGUMENT);
            return rsp;
        }
        String productLineCode = messageReq.getProductLineCode();
        String labCode = messageReq.getLabCode();
        String token = messageReq.getSgsToken();
        UserInfo user = tokenClient.getUser(token);
        String regionAccount = StringPool.EMPTY;
        if (Func.isNotEmpty(user)){
            regionAccount = user.getRegionAccount();
        }
        ProductLineContextHolder.setProductLineCode(productLineCode);

        if (StringUtils.isBlank(productLineCode)) {
            log.error("productLineCode cannot be null");
            rsp.setErrorCode(ErrorCode.ILLEGAL_ARGUMENT);
            rsp.setErrorMsg("productLineCode cannot be null");
            return rsp;
        }
        ProductLineContextHolder.setProductLineCode(productLineCode);
        String data = messageReq.getData();
        DataEntryEventReq dataEntryEventReq = null;
        BaseResponse<Boolean> booleanBaseResponse = new BaseResponse<>();
        try {
            dataEntryEventReq = com.alibaba.dubbo.common.json.JSON.parse(data, DataEntryEventReq.class);
            dataEntryEventReq.setToken(token);
            dataEntryEventReq.setProductLineCode(productLineCode);
            dataEntryEventReq.setLabCode(labCode);
            dataEntryEventReq.setRegionAccount(regionAccount);
            //log.info("SystemContextHolder info:{}",Func.toJson(SystemContextHolder.getContext()));
            log.info("dataEntryEventReq:{}",Func.toJson(dataEntryEventReq));
            booleanBaseResponse = dateEntryBizService.handleDataEntryEvent(dataEntryEventReq);
        } catch (ParseException e) {
            rsp.setSuccess(false);
            rsp.setErrorMsg("parse DataEntryEvent Data error:"+e.getMessage());
            return rsp;
        }
        rsp.setSuccess(booleanBaseResponse.isSuccess());
        rsp.setErrorMsg(booleanBaseResponse.getMessage());
        return rsp;
    }

    @PostMapping("report-test-matrix-mode")
    public BaseResponse reportTestMatrixMode(@RequestBody ReportTestMatrixModelGetReq reportTestMatrixModelGetReq){
        return reportService.getReportTestMatrixMode(reportTestMatrixModelGetReq);
    }

    @GetMapping("require-tag")
    public BaseResponse requireTag(){
        LambdaQueryWrapper<OrderAttachmentPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.isNotNull(OrderAttachmentPO::getDescription);
        wrapper.ne(OrderAttachmentPO::getDescription,"");
        wrapper.isNull(OrderAttachmentPO::getTag);
        wrapper.last("limit 200");
        List<String> ids = new ArrayList<>();
        List<OrderAttachmentPO> attachmentPOList = orderAttachmentService.list(wrapper);
        for(OrderAttachmentPO orderAttachmentPO : attachmentPOList){
            if(Func.isNotEmpty(orderAttachmentPO.getDescription())){
                OrderAttachmentTagDTO orderAttachmentTagDTO = new OrderAttachmentTagDTO();
                List<OrderAttachmentTagDTO.OrderAttachmentTagLanguageDTO> languageList = new ArrayList<>();
                OrderAttachmentTagDTO.OrderAttachmentTagLanguageDTO dto = new OrderAttachmentTagDTO.OrderAttachmentTagLanguageDTO();
                dto.setLanguageId(1);
                dto.setPhotoDescription(orderAttachmentPO.getDescription());
                languageList.add(dto);
                orderAttachmentTagDTO.setLanguageList(languageList);
                OrderAttachmentPO po = new OrderAttachmentPO();
                po.setTag(JSON.toJSONString(orderAttachmentTagDTO));
                LambdaUpdateWrapper<OrderAttachmentPO> wrapper1 = new LambdaUpdateWrapper();
                wrapper1.eq(OrderAttachmentPO::getId, orderAttachmentPO.getId());
                orderAttachmentService.update(po,wrapper1);
                ids.add(orderAttachmentPO.getId());
            }
        }
        return BaseResponse.newSuccessInstance(ids);
    }

    @PostMapping("updateReportTestResultStatus")
    public BaseResponse updateReportTestResultStatus(@RequestBody ReportTestResultStatusUpdateReq reportTestResultStatusUpdateReq){
        return reportService.updateReportTestResultStatus(reportTestResultStatusUpdateReq);
    }


    @PostMapping("updateReportEntryMode")
    public BaseResponse updateReportEntryMode(@RequestBody ReportEntryModeUpdateReq reportEntryModeUpdateReq){
        return reportService.updateReportEntryMode(reportEntryModeUpdateReq);
    }

    @PostMapping("updateOrderTestRequest")
    public BaseResponse updateReportAccreditation(@RequestBody OrderQualificationTypeUpdateReq reportAccreditationUpdateReq){
        return reportBizService.updateReportAccreditation(reportAccreditationUpdateReq);
    }
    @GetMapping("updateDelayForOdc")
    public BaseResponse updateDelayForOdc(){
        int i = reportMapper.updateDelayForOdc();
        return BaseResponse.newSuccessInstance(i);
    }
    @GetMapping("updateOrderForOdc")
    public BaseResponse updateOrderForOdc(){
        int i = generalOrderMapper.updateForOdc();
        return BaseResponse.newSuccessInstance(i);
    }

    @PostMapping("sciTrfEventConsumer")
    public BaseResponse sciTrfEventConsumer(@RequestBody TrfUpdateReq trfUpdateReq){
        MessageReq messageReq = new MessageReq();
        messageReq.setUserName("trevor_yuan");
        messageReq.setActionType("");
        messageReq.setAction("TrfUpdated");
        messageReq.setProductLineCode("HL");
        messageReq.setLabCode("GZ HL");
        messageReq.setData(Func.toJson(trfUpdateReq));
        messageReq.setSgsToken("");
        messageReq.setSystemId("15");

        KafkaMessageRsp kafkaMessageRsp = sciTrfEventConsumer.processMessage(messageReq);
        return BaseResponse.newSuccessInstance(kafkaMessageRsp);
    }

    @PostMapping("refresh-order-index")
    public BaseResponse refreshOrderIndex(@RequestBody List<String> orderIdList){
        if(Func.isEmpty(orderIdList)){
            return BaseResponse.newFailInstance(ResponseCode.PARAM_MISS);
        }
        Map<String,Object> resultMap = new HashMap<>();
        orderIdList.forEach(orderId -> {
            OrderIdBO orderIdBO = new OrderIdBO();
            orderIdBO.setOrderId(orderId);
            boolean result = orderIndexService.saveOrUpdate(orderIdBO);
            resultMap.put(orderIdBO.getOrderId(), result);
        });
        return BaseResponse.newSuccessInstance(resultMap);
    }

    @PostMapping("repair-subreport")
    public BaseResponse repairSubReport(){
        return devopsService.repairSubReport();
    }

    @PostMapping("update-mr-dff")
    public BaseResponse updateDFFForMR(@RequestParam String start, @RequestParam String end) {
        return devopsService.updateDFFForMR(start,end);
    }
}
