# ArchUnit 架构测试说明

## 概述

本项目使用 ArchUnit 1.4.1 版本进行架构验证，确保代码遵循既定的架构规则。

## 验证规则

### 核心规则：Service依赖必须是接口

**规则描述：** 所有类的属性如果以Service或ServiceImpl结尾，其类型必须是接口，不能是实现类。

**验证内容：**
1. 检查所有类的字段
2. 如果字段名以Service或ServiceImpl结尾
3. 则该字段的类型必须是接口
4. 不允许直接依赖实现类

**示例：**

✅ **正确的用法：**
```java
@Service
public class OrderBizServiceImpl {
    @Autowired
    private IOrderService orderService;  // 依赖接口
    
    @Autowired
    private IUserService userService;    // 依赖接口
}
```

❌ **错误的用法：**
```java
@Service
public class OrderBizServiceImpl {
    @Autowired
    private OrderServiceImpl orderService;  // 直接依赖实现类，违反规则
    
    @Autowired
    private UserServiceImpl userService;    // 直接依赖实现类，违反规则
}
```

## 测试文件说明

### 1. ServiceInterfaceDependencyTest.java
- **位置：** `src/test/java/com/sgs/gpo/archunit/ServiceInterfaceDependencyTest.java`
- **功能：** 核心的Service依赖验证规则
- **包含规则：**
  - `SERVICE_FIELDS_MUST_BE_INTERFACES`: 验证Service字段必须是接口
  - `NO_DIRECT_SERVICE_IMPL_DEPENDENCIES`: 验证不能直接依赖实现类
  - `SERVICE_FIELDS_SHOULD_FOLLOW_INTERFACE_NAMING`: 验证接口命名规范

### 2. ServiceDependencyArchitectureTest.java
- **位置：** `src/test/java/com/sgs/gpo/archunit/ServiceDependencyArchitectureTest.java`
- **功能：** 扩展的架构验证规则
- **包含规则：**
  - 针对不同注解类型的类的验证
  - 更详细的命名规范检查

### 3. ArchUnitTestRunner.java
- **位置：** `src/test/java/com/sgs/gpo/archunit/ArchUnitTestRunner.java`
- **功能：** 测试运行器，整合所有规则

## 运行测试

### 方法1：运行单个测试类
```bash
# 运行核心Service依赖测试
mvn test -Dtest=ServiceInterfaceDependencyTest

# 运行完整架构测试
mvn test -Dtest=ServiceDependencyArchitectureTest
```

### 方法2：运行测试运行器
```bash
# 运行所有ArchUnit测试
mvn test -Dtest=ArchUnitTestRunner
```

### 方法3：运行整个测试模块
```bash
# 在gpo-test目录下运行
cd gpo-test
mvn test
```

## 测试输出说明

### 成功情况
如果所有规则都通过，测试将显示：
```
Tests run: X, Failures: 0, Errors: 0, Skipped: 0
```

### 失败情况
如果发现架构违规，测试将显示详细的错误信息：
```
架构违规：类 com.sgs.gpo.biz.order.OrderBizServiceImpl 中的字段 orderService 类型为 com.sgs.gpo.biz.order.impl.OrderServiceImpl，但字段名以Service结尾，类型必须是接口而不是实现类
```

## 修复建议

当发现架构违规时，请按照以下步骤修复：

1. **识别违规的字段**
2. **找到对应的接口**
3. **修改字段类型为接口**
4. **确保Spring配置正确注入实现类**

**修复示例：**
```java
// 修复前
@Autowired
private OrderServiceImpl orderService;

// 修复后
@Autowired
private IOrderService orderService;
```

## 配置说明

### Maven依赖
项目已在 `pom.xml` 中添加了必要的依赖：
```xml
<dependency>
    <groupId>com.tngtech.archunit</groupId>
    <artifactId>archunit</artifactId>
    <version>1.4.1</version>
    <scope>test</scope>
</dependency>
<dependency>
    <groupId>com.tngtech.archunit</groupId>
    <artifactId>archunit-junit4</artifactId>
    <version>1.4.1</version>
    <scope>test</scope>
</dependency>
```

### 测试范围
- **分析包：** `com.sgs.gpo`
- **测试位置：** `gpo-test` 模块
- **版本要求：** ArchUnit 1.4.1

## 注意事项

1. **静态字段和常量：** 测试会自动跳过静态字段和final字段
2. **Spring注解：** 测试会检查带有@Service、@Component、@Controller、@Repository注解的类
3. **命名规范：** 建议接口名以I开头，但不是强制要求
4. **依赖注入：** 确保Spring配置正确，能够注入实现类到接口类型的字段中

## 扩展规则

如需添加新的架构规则，请：

1. 在 `ServiceInterfaceDependencyTest.java` 中添加新的 `@ArchTest` 方法
2. 在 `ArchUnitTestRunner.java` 中导入新规则
3. 更新此README文档

## 联系方式

如有问题或建议，请联系开发团队。
