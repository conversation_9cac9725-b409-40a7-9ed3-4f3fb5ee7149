package com.sgs.gpo.archunit;

import com.tngtech.archunit.junit.AnalyzeClasses;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.junit.ArchUnitRunner;
import com.tngtech.archunit.lang.ArchRule;
import org.junit.runner.RunWith;

/**
 * ArchUnit测试运行器
 * 
 * 用于执行所有的架构测试规则
 * 
 * <AUTHOR> Test Runner
 * @version 1.0
 */
@RunWith(ArchUnitRunner.class)
@AnalyzeClasses(packages = "com.sgs.gpo")
public class ArchUnitTestRunner {

    /**
     * 导入ServiceInterfaceDependencyTest中的所有规则
     */
    @ArchTest
    public static final ArchRule serviceFieldsMustBeInterfaces = 
        ServiceInterfaceDependencyTest.SERVICE_FIELDS_MUST_BE_INTERFACES;

    @ArchTest
    public static final ArchRule noDirectServiceImplDependencies = 
        ServiceInterfaceDependencyTest.NO_DIRECT_SERVICE_IMPL_DEPENDENCIES;

    @ArchTest
    public static final ArchRule serviceFieldsShouldFollowInterfaceNaming = 
        ServiceInterfaceDependencyTest.SERVICE_FIELDS_SHOULD_FOLLOW_INTERFACE_NAMING;

    /**
     * 导入ServiceDependencyArchitectureTest中的所有规则
     */
    @ArchTest
    public static final ArchRule serviceFieldsMustBeInterfacesFromArchitecture = 
        ServiceDependencyArchitectureTest.SERVICE_FIELDS_MUST_BE_INTERFACES;

    @ArchTest
    public static final ArchRule serviceImplFieldsMustBeInterfaces = 
        ServiceDependencyArchitectureTest.SERVICE_IMPL_FIELDS_MUST_BE_INTERFACES;

    @ArchTest
    public static final ArchRule noDirectServiceImplDependenciesFromArchitecture = 
        ServiceDependencyArchitectureTest.NO_DIRECT_SERVICE_IMPL_DEPENDENCIES;

    @ArchTest
    public static final ArchRule serviceFieldsMustFollowInterfaceNaming = 
        ServiceDependencyArchitectureTest.SERVICE_FIELDS_MUST_FOLLOW_INTERFACE_NAMING;
}
