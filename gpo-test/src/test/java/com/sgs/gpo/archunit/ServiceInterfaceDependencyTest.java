package com.sgs.gpo.archunit;

import com.tngtech.archunit.core.domain.JavaClass;
import com.tngtech.archunit.core.domain.JavaField;
import com.tngtech.archunit.core.domain.JavaModifier;
import com.tngtech.archunit.junit.AnalyzeClasses;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchCondition;
import com.tngtech.archunit.lang.ArchRule;
import com.tngtech.archunit.lang.ConditionEvents;
import com.tngtech.archunit.lang.SimpleConditionEvent;
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition;

import java.util.List;
import java.util.Set;

/**
 * Service接口依赖架构测试
 * 
 * 验证规则：
 * 1. 所有类的属性如果以Service或ServiceImpl结尾，其类型必须是接口，不能是实现类
 * 2. 不允许直接依赖Service实现类
 * 
 * <AUTHOR> Test
 * @version 1.0
 */
@AnalyzeClasses(packages = "com.sgs.gpo")
public class ServiceInterfaceDependencyTest {

    /**
     * 核心规则：验证所有类的Service属性必须是接口类型
     * 
     * 规则说明：
     * - 检查所有类的字段
     * - 如果字段名以Service或ServiceImpl结尾
     * - 则该字段的类型必须是接口
     * - 不允许直接依赖实现类
     */
    @ArchTest
    public static final ArchRule SERVICE_FIELDS_MUST_BE_INTERFACES = ArchRuleDefinition
            .classes()
            .should(new ArchCondition<JavaClass>("Service字段必须是接口类型") {
                @Override
                public void check(JavaClass javaClass, ConditionEvents events) {
                    Set<JavaField> fields = javaClass.getAllFields();
                    
                    for (JavaField field : fields) {
                        // 跳过静态字段、final字段和常量
                        if (field.getModifiers().contains(JavaModifier.STATIC) || 
                            field.getModifiers().contains(JavaModifier.FINAL)) {
                            continue;
                        }
                        
                        String fieldName = field.getName();
                        JavaClass fieldType = field.getRawType();
                        String fieldTypeName = fieldType.getFullName();
                        
                        // 检查字段名是否以Service或ServiceImpl结尾
                        if (fieldName.endsWith("Service") || fieldName.endsWith("ServiceImpl")) {
                            // 检查字段类型是否为接口
                            if (!fieldType.isInterface()) {
                                String message = String.format(
                                    "架构违规：类 %s 中的字段 %s 类型为 %s，但字段名以Service结尾，类型必须是接口而不是实现类",
                                    javaClass.getFullName(),
                                    fieldName,
                                    fieldTypeName
                                );
                                events.add(SimpleConditionEvent.violated(javaClass, message));
                            }
                            
                            // 额外检查：字段类型不能以ServiceImpl结尾
                            if (fieldTypeName.endsWith("ServiceImpl")) {
                                String message = String.format(
                                    "架构违规：类 %s 中的字段 %s 直接依赖了实现类 %s，应该依赖接口",
                                    javaClass.getFullName(),
                                    fieldName,
                                    fieldTypeName
                                );
                                events.add(SimpleConditionEvent.violated(javaClass, message));
                            }
                        }
                    }
                }
            });

    /**
     * 验证所有类的字段不能直接依赖Service实现类
     * 
     * 规则说明：
     * - 检查所有类的字段类型
     * - 如果字段类型以ServiceImpl结尾，则违反架构规则
     * - 必须依赖接口而不是实现类
     */
    @ArchTest
    public static final ArchRule NO_DIRECT_SERVICE_IMPL_DEPENDENCIES = ArchRuleDefinition
            .classes()
            .should(new ArchCondition<JavaClass>("不能直接依赖Service实现类") {
                @Override
                public void check(JavaClass javaClass, ConditionEvents events) {
                    Set<JavaField> fields = javaClass.getAllFields();
                    
                    for (JavaField field : fields) {
                        // 跳过静态字段、final字段和常量
                        if (field.getModifiers().contains(JavaModifier.STATIC) || 
                            field.getModifiers().contains(JavaModifier.FINAL)) {
                            continue;
                        }
                        
                        JavaClass fieldType = field.getRawType();
                        String fieldTypeName = fieldType.getFullName();
                        
                        // 检查字段类型是否以ServiceImpl结尾
                        if (fieldTypeName.endsWith("ServiceImpl")) {
                            String message = String.format(
                                "架构违规：类 %s 中的字段 %s 直接依赖了实现类 %s，应该依赖接口",
                                javaClass.getFullName(),
                                field.getName(),
                                fieldTypeName
                            );
                            events.add(SimpleConditionEvent.violated(javaClass, message));
                        }
                    }
                }
            });

    /**
     * 验证Service字段必须遵循接口命名规范
     * 
     * 规则说明：
     * - 检查所有类的字段
     * - 如果字段名以Service结尾（不包括ServiceImpl）
     * - 则该字段的类型应该是接口
     * - 建议接口名以I开头（可选规则）
     */
    @ArchTest
    public static final ArchRule SERVICE_FIELDS_SHOULD_FOLLOW_INTERFACE_NAMING = ArchRuleDefinition
            .classes()
            .should(new ArchCondition<JavaClass>("Service字段应该遵循接口命名规范") {
                @Override
                public void check(JavaClass javaClass, ConditionEvents events) {
                    Set<JavaField> fields = javaClass.getAllFields();
                    
                    for (JavaField field : fields) {
                        // 跳过静态字段、final字段和常量
                        if (field.getModifiers().contains(JavaModifier.STATIC) || 
                            field.getModifiers().contains(JavaModifier.FINAL)) {
                            continue;
                        }
                        
                        String fieldName = field.getName();
                        JavaClass fieldType = field.getRawType();
                        String fieldTypeName = fieldType.getFullName();
                        
                        // 检查字段名是否以Service结尾（不包括ServiceImpl）
                        if (fieldName.endsWith("Service") && !fieldName.endsWith("ServiceImpl")) {
                            // 检查字段类型是否为接口
                            if (fieldType.isInterface()) {
                                // 建议接口名以I开头（可选规则，不强制）
                                String simpleClassName = fieldType.getSimpleName();
                                if (!simpleClassName.startsWith("I")) {
                                    String message = String.format(
                                        "建议：类 %s 中的字段 %s 类型为 %s，建议接口名以I开头以遵循命名规范",
                                        javaClass.getFullName(),
                                        fieldName,
                                        fieldTypeName
                                    );
                                    // 这里使用info级别，不违反规则
                                    // events.add(SimpleConditionEvent.violated(javaClass, message));
                                }
                            }
                        }
                    }
                }
            });
}
