package com.sgs.gpo.archunit;

import com.tngtech.archunit.core.domain.JavaClass;
import com.tngtech.archunit.core.domain.JavaField;
import com.tngtech.archunit.core.domain.JavaModifier;
import com.tngtech.archunit.junit.AnalyzeClasses;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchCondition;
import com.tngtech.archunit.lang.ArchRule;
import com.tngtech.archunit.lang.ConditionEvents;
import com.tngtech.archunit.lang.SimpleConditionEvent;
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * ArchUnit架构测试类
 * 验证Service依赖规则：所有类的属性如果以Service或ServiceImpl结尾，其类型必须是接口，不能是实现类
 * 
 * <AUTHOR> Test
 * @version 1.0
 */
@AnalyzeClasses(packages = "com.sgs.gpo")
public class ServiceDependencyArchitectureTest {

    /**
     * 验证所有类的Service属性必须是接口类型
     * 规则：如果类的属性名以Service或ServiceImpl结尾，则该属性的类型必须是接口
     */
    @ArchTest
    public static final ArchRule SERVICE_FIELDS_MUST_BE_INTERFACES = ArchRuleDefinition
            .classes()
            .that().areAnnotatedWith(Service.class)
            .or().areAnnotatedWith(Component.class)
            .or().areAnnotatedWith(Controller.class)
            .or().areAnnotatedWith(Repository.class)
            .should(new ArchCondition<JavaClass>("Service字段必须是接口类型") {
                @Override
                public void check(JavaClass javaClass, ConditionEvents events) {
                    Set<JavaField> fields = javaClass.getAllFields();
                    
                    for (JavaField field : fields) {
                        // 跳过静态字段和final字段
                        if (field.getModifiers().contains(JavaModifier.STATIC) || 
                            field.getModifiers().contains(JavaModifier.FINAL)) {
                            continue;
                        }
                        
                        String fieldName = field.getName();
                        JavaClass fieldType = field.getRawType();
                        
                        // 检查字段名是否以Service或ServiceImpl结尾
                        if (fieldName.endsWith("Service") || fieldName.endsWith("ServiceImpl")) {
                            // 检查字段类型是否为接口
                            if (!fieldType.isInterface()) {
                                String message = String.format(
                                    "类 %s 中的字段 %s 类型为 %s，但字段名以Service结尾，类型必须是接口",
                                    javaClass.getFullName(),
                                    fieldName,
                                    fieldType.getFullName()
                                );
                                events.add(SimpleConditionEvent.violated(javaClass, message));
                            }
                        }
                    }
                }
            });

    /**
     * 验证所有类的ServiceImpl属性必须是接口类型
     * 规则：如果类的属性名以ServiceImpl结尾，则该属性的类型必须是接口
     */
    @ArchTest
    public static final ArchRule SERVICE_IMPL_FIELDS_MUST_BE_INTERFACES = ArchRuleDefinition
            .classes()
            .should(new ArchCondition<JavaClass>("ServiceImpl字段必须是接口类型") {
                @Override
                public void check(JavaClass javaClass, ConditionEvents events) {
                    Set<JavaField> fields = javaClass.getAllFields();
                    
                    for (JavaField field : fields) {
                        // 跳过静态字段和final字段
                        if (field.getModifiers().contains(JavaModifier.STATIC) || 
                            field.getModifiers().contains(JavaModifier.FINAL)) {
                            continue;
                        }
                        
                        String fieldName = field.getName();
                        JavaClass fieldType = field.getRawType();
                        
                        // 检查字段名是否以ServiceImpl结尾
                        if (fieldName.endsWith("ServiceImpl")) {
                            // 检查字段类型是否为接口
                            if (!fieldType.isInterface()) {
                                String message = String.format(
                                    "类 %s 中的字段 %s 类型为 %s，但字段名以ServiceImpl结尾，类型必须是接口",
                                    javaClass.getFullName(),
                                    fieldName,
                                    fieldType.getFullName()
                                );
                                events.add(SimpleConditionEvent.violated(javaClass, message));
                            }
                        }
                    }
                }
            });

    /**
     * 验证所有类的Service属性不能直接依赖实现类
     * 规则：如果类的属性类型以ServiceImpl结尾，则违反架构规则
     */
    @ArchTest
    public static final ArchRule NO_DIRECT_SERVICE_IMPL_DEPENDENCIES = ArchRuleDefinition
            .classes()
            .should(new ArchCondition<JavaClass>("不能直接依赖Service实现类") {
                @Override
                public void check(JavaClass javaClass, ConditionEvents events) {
                    Set<JavaField> fields = javaClass.getAllFields();
                    
                    for (JavaField field : fields) {
                        // 跳过静态字段和final字段
                        if (field.getModifiers().contains(JavaModifier.STATIC) || 
                            field.getModifiers().contains(JavaModifier.FINAL)) {
                            continue;
                        }
                        
                        JavaClass fieldType = field.getRawType();
                        String fieldTypeName = fieldType.getFullName();
                        
                        // 检查字段类型是否以ServiceImpl结尾
                        if (fieldTypeName.endsWith("ServiceImpl")) {
                            String message = String.format(
                                "类 %s 中的字段 %s 直接依赖了实现类 %s，应该依赖接口",
                                javaClass.getFullName(),
                                field.getName(),
                                fieldTypeName
                            );
                            events.add(SimpleConditionEvent.violated(javaClass, message));
                        }
                    }
                }
            });

    /**
     * 验证所有类的Service属性必须依赖接口
     * 规则：如果类的属性名以Service结尾，则该属性的类型必须以I开头（接口命名规范）
     */
    @ArchTest
    public static final ArchRule SERVICE_FIELDS_MUST_FOLLOW_INTERFACE_NAMING = ArchRuleDefinition
            .classes()
            .should(new ArchCondition<JavaClass>("Service字段必须遵循接口命名规范") {
                @Override
                public void check(JavaClass javaClass, ConditionEvents events) {
                    Set<JavaField> fields = javaClass.getAllFields();
                    
                    for (JavaField field : fields) {
                        // 跳过静态字段和final字段
                        if (field.getModifiers().contains(JavaModifier.STATIC) || 
                            field.getModifiers().contains(JavaModifier.FINAL)) {
                            continue;
                        }
                        
                        String fieldName = field.getName();
                        JavaClass fieldType = field.getRawType();
                        String fieldTypeName = fieldType.getFullName();
                        
                        // 检查字段名是否以Service结尾
                        if (fieldName.endsWith("Service") && !fieldName.endsWith("ServiceImpl")) {
                            // 检查字段类型是否为接口且以I开头
                            if (fieldType.isInterface() && !fieldTypeName.contains("." + "I")) {
                                String message = String.format(
                                    "类 %s 中的字段 %s 类型为 %s，虽然实现了接口，但建议遵循接口命名规范（以I开头）",
                                    javaClass.getFullName(),
                                    fieldName,
                                    fieldTypeName
                                );
                                events.add(SimpleConditionEvent.violated(javaClass, message));
                            }
                        }
                    }
                }
            });
}
