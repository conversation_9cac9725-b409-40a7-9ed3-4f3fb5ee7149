<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>gpo-micro-service</artifactId>
        <groupId>com.sgs.gpo</groupId>
        <version>0.1.83</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>gpo-test</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.sgs.gpo</groupId>
            <artifactId>gpo-facade-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>4.3.22.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        
        <!-- ArchUnit 依赖 -->
        <dependency>
            <groupId>com.tngtech.archunit</groupId>
            <artifactId>archunit</artifactId>
            <version>1.4.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.tngtech.archunit</groupId>
            <artifactId>archunit-junit4</artifactId>
            <version>1.4.1</version>
            <scope>test</scope>
        </dependency>
        
        <!-- 项目模块依赖，用于ArchUnit测试 -->
        <dependency>
            <groupId>com.sgs.gpo</groupId>
            <artifactId>gpo-biz</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.sgs.gpo</groupId>
            <artifactId>gpo-domain</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.sgs.gpo</groupId>
            <artifactId>gpo-web</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.sgs.gpo</groupId>
            <artifactId>gpo-facade-impl</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.sgs.gpo</groupId>
            <artifactId>gpo-integration</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.sgs.gpo</groupId>
            <artifactId>gpo-core</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.sgs.gpo</groupId>
            <artifactId>gpo-web</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>